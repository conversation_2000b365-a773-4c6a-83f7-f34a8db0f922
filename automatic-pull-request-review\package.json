{"name": "automatic-pull-request-review", "version": "0.0.5", "description": "👍 Github action to automate pull requests", "private": true, "main": "dist/index.js", "scripts": {"watch": "ncc build index.ts --watch --minify", "build": "ncc build index.ts --minify"}, "repository": {"type": "git", "url": "git+https://github.com/andrewmusgrave/automatic-pull-request-review.git"}, "keywords": ["github-actions", "pulls", "request"], "author": "<PERSON> <andrewdmus<PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/andrewmusgrave/automatic-pull-request-review/issues"}, "homepage": "https://github.com/andrewmusgrave/automatic-pull-request-review#readme", "dependencies": {"@actions/core": "^1.2.7", "@actions/github": "^4.0.0"}, "devDependencies": {"@types/node": "^12.7.4", "@vercel/ncc": "^0.28.5", "husky": "^3.0.5", "typescript": "^4.2.4"}, "files": ["dist/index.js"], "husky": {"hooks": {"pre-commit": "npm run build && git add ./dist/index.js"}}}