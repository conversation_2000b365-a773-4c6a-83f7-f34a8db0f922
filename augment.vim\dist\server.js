/*!
 * Copyright (c) 2025 Augment
 * Proprietary and Confidential
 * Licensed for use only with Vim plugin - See LICENSE.md
 * Modification or reverse engineering prohibited
 */
"use strict";var XM=Object.create;var rf=Object.defineProperty;var YM=Object.getOwnPropertyDescriptor;var ZM=Object.getOwnPropertyNames;var eA=Object.getPrototypeOf,tA=Object.prototype.hasOwnProperty;var I=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var s_=(t,e,n,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of ZM(e))!tA.call(t,s)&&s!==n&&rf(t,s,{get:()=>e[s],enumerable:!(i=YM(e,s))||i.enumerable});return t};var Ze=(t,e,n)=>(n=t!=null?XM(eA(t)):{},s_(e||!t||!t.__esModule?rf(n,"default",{value:t,enumerable:!0}):n,t)),nA=t=>s_(rf({},"__esModule",{value:!0}),t);var pc=I(Et=>{"use strict";Object.defineProperty(Et,"__esModule",{value:!0});Et.thenable=Et.typedArray=Et.stringArray=Et.array=Et.func=Et.error=Et.number=Et.string=Et.boolean=void 0;function iA(t){return t===!0||t===!1}Et.boolean=iA;function l_(t){return typeof t=="string"||t instanceof String}Et.string=l_;function oA(t){return typeof t=="number"||t instanceof Number}Et.number=oA;function sA(t){return t instanceof Error}Et.error=sA;function d_(t){return typeof t=="function"}Et.func=d_;function f_(t){return Array.isArray(t)}Et.array=f_;function aA(t){return f_(t)&&t.every(e=>l_(e))}Et.stringArray=aA;function cA(t,e){return Array.isArray(t)&&t.every(e)}Et.typedArray=cA;function uA(t){return t&&d_(t.then)}Et.thenable=uA});var go=I(Vt=>{"use strict";Object.defineProperty(Vt,"__esModule",{value:!0});Vt.stringArray=Vt.array=Vt.func=Vt.error=Vt.number=Vt.string=Vt.boolean=void 0;function lA(t){return t===!0||t===!1}Vt.boolean=lA;function h_(t){return typeof t=="string"||t instanceof String}Vt.string=h_;function dA(t){return typeof t=="number"||t instanceof Number}Vt.number=dA;function fA(t){return t instanceof Error}Vt.error=fA;function hA(t){return typeof t=="function"}Vt.func=hA;function g_(t){return Array.isArray(t)}Vt.array=g_;function gA(t){return g_(t)&&t.every(e=>h_(e))}Vt.stringArray=gA});var Ff=I(ce=>{"use strict";Object.defineProperty(ce,"__esModule",{value:!0});ce.Message=ce.NotificationType9=ce.NotificationType8=ce.NotificationType7=ce.NotificationType6=ce.NotificationType5=ce.NotificationType4=ce.NotificationType3=ce.NotificationType2=ce.NotificationType1=ce.NotificationType0=ce.NotificationType=ce.RequestType9=ce.RequestType8=ce.RequestType7=ce.RequestType6=ce.RequestType5=ce.RequestType4=ce.RequestType3=ce.RequestType2=ce.RequestType1=ce.RequestType=ce.RequestType0=ce.AbstractMessageSignature=ce.ParameterStructures=ce.ResponseError=ce.ErrorCodes=void 0;var qi=go(),sf;(function(t){t.ParseError=-32700,t.InvalidRequest=-32600,t.MethodNotFound=-32601,t.InvalidParams=-32602,t.InternalError=-32603,t.jsonrpcReservedErrorRangeStart=-32099,t.serverErrorStart=-32099,t.MessageWriteError=-32099,t.MessageReadError=-32098,t.PendingResponseRejected=-32097,t.ConnectionInactive=-32096,t.ServerNotInitialized=-32002,t.UnknownErrorCode=-32001,t.jsonrpcReservedErrorRangeEnd=-32e3,t.serverErrorEnd=-32e3})(sf||(ce.ErrorCodes=sf={}));var af=class t extends Error{constructor(e,n,i){super(n),this.code=qi.number(e)?e:sf.UnknownErrorCode,this.data=i,Object.setPrototypeOf(this,t.prototype)}toJson(){let e={code:this.code,message:this.message};return this.data!==void 0&&(e.data=this.data),e}};ce.ResponseError=af;var fn=class t{constructor(e){this.kind=e}static is(e){return e===t.auto||e===t.byName||e===t.byPosition}toString(){return this.kind}};ce.ParameterStructures=fn;fn.auto=new fn("auto");fn.byPosition=new fn("byPosition");fn.byName=new fn("byName");var lt=class{constructor(e,n){this.method=e,this.numberOfParams=n}get parameterStructures(){return fn.auto}};ce.AbstractMessageSignature=lt;var cf=class extends lt{constructor(e){super(e,0)}};ce.RequestType0=cf;var uf=class extends lt{constructor(e,n=fn.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};ce.RequestType=uf;var lf=class extends lt{constructor(e,n=fn.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};ce.RequestType1=lf;var df=class extends lt{constructor(e){super(e,2)}};ce.RequestType2=df;var ff=class extends lt{constructor(e){super(e,3)}};ce.RequestType3=ff;var hf=class extends lt{constructor(e){super(e,4)}};ce.RequestType4=hf;var gf=class extends lt{constructor(e){super(e,5)}};ce.RequestType5=gf;var pf=class extends lt{constructor(e){super(e,6)}};ce.RequestType6=pf;var mf=class extends lt{constructor(e){super(e,7)}};ce.RequestType7=mf;var bf=class extends lt{constructor(e){super(e,8)}};ce.RequestType8=bf;var _f=class extends lt{constructor(e){super(e,9)}};ce.RequestType9=_f;var vf=class extends lt{constructor(e,n=fn.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};ce.NotificationType=vf;var yf=class extends lt{constructor(e){super(e,0)}};ce.NotificationType0=yf;var wf=class extends lt{constructor(e,n=fn.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};ce.NotificationType1=wf;var Sf=class extends lt{constructor(e){super(e,2)}};ce.NotificationType2=Sf;var xf=class extends lt{constructor(e){super(e,3)}};ce.NotificationType3=xf;var Pf=class extends lt{constructor(e){super(e,4)}};ce.NotificationType4=Pf;var Cf=class extends lt{constructor(e){super(e,5)}};ce.NotificationType5=Cf;var kf=class extends lt{constructor(e){super(e,6)}};ce.NotificationType6=kf;var Tf=class extends lt{constructor(e){super(e,7)}};ce.NotificationType7=Tf;var Rf=class extends lt{constructor(e){super(e,8)}};ce.NotificationType8=Rf;var Df=class extends lt{constructor(e){super(e,9)}};ce.NotificationType9=Df;var p_;(function(t){function e(s){let c=s;return c&&qi.string(c.method)&&(qi.string(c.id)||qi.number(c.id))}t.isRequest=e;function n(s){let c=s;return c&&qi.string(c.method)&&s.id===void 0}t.isNotification=n;function i(s){let c=s;return c&&(c.result!==void 0||!!c.error)&&(qi.string(c.id)||qi.number(c.id)||c.id===null)}t.isResponse=i})(p_||(ce.Message=p_={}))});var If=I(ni=>{"use strict";var m_;Object.defineProperty(ni,"__esModule",{value:!0});ni.LRUCache=ni.LinkedMap=ni.Touch=void 0;var Gt;(function(t){t.None=0,t.First=1,t.AsOld=t.First,t.Last=2,t.AsNew=t.Last})(Gt||(ni.Touch=Gt={}));var mc=class{constructor(){this[m_]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,n=Gt.None){let i=this._map.get(e);if(i)return n!==Gt.None&&this.touch(i,n),i.value}set(e,n,i=Gt.None){let s=this._map.get(e);if(s)s.value=n,i!==Gt.None&&this.touch(s,i);else{switch(s={key:e,value:n,next:void 0,previous:void 0},i){case Gt.None:this.addItemLast(s);break;case Gt.First:this.addItemFirst(s);break;case Gt.Last:this.addItemLast(s);break;default:this.addItemLast(s);break}this._map.set(e,s),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){let n=this._map.get(e);if(n)return this._map.delete(e),this.removeItem(n),this._size--,n.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");let e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,n){let i=this._state,s=this._head;for(;s;){if(n?e.bind(n)(s.value,s.key,this):e(s.value,s.key,this),this._state!==i)throw new Error("LinkedMap got modified during iteration.");s=s.next}}keys(){let e=this._state,n=this._head,i={[Symbol.iterator]:()=>i,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let s={value:n.key,done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return i}values(){let e=this._state,n=this._head,i={[Symbol.iterator]:()=>i,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let s={value:n.value,done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return i}entries(){let e=this._state,n=this._head,i={[Symbol.iterator]:()=>i,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let s={value:[n.key,n.value],done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return i}[(m_=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(e===0){this.clear();return}let n=this._head,i=this.size;for(;n&&i>e;)this._map.delete(n.key),n=n.next,i--;this._head=n,this._size=i,n&&(n.previous=void 0),this._state++}addItemFirst(e){if(!this._head&&!this._tail)this._tail=e;else if(this._head)e.next=this._head,this._head.previous=e;else throw new Error("Invalid list");this._head=e,this._state++}addItemLast(e){if(!this._head&&!this._tail)this._head=e;else if(this._tail)e.previous=this._tail,this._tail.next=e;else throw new Error("Invalid list");this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{let n=e.next,i=e.previous;if(!n||!i)throw new Error("Invalid list");n.previous=i,i.next=n}e.next=void 0,e.previous=void 0,this._state++}touch(e,n){if(!this._head||!this._tail)throw new Error("Invalid list");if(!(n!==Gt.First&&n!==Gt.Last)){if(n===Gt.First){if(e===this._head)return;let i=e.next,s=e.previous;e===this._tail?(s.next=void 0,this._tail=s):(i.previous=s,s.next=i),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(n===Gt.Last){if(e===this._tail)return;let i=e.next,s=e.previous;e===this._head?(i.previous=void 0,this._head=i):(i.previous=s,s.next=i),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}}toJSON(){let e=[];return this.forEach((n,i)=>{e.push([i,n])}),e}fromJSON(e){this.clear();for(let[n,i]of e)this.set(n,i)}};ni.LinkedMap=mc;var Ef=class extends mc{constructor(e,n=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,n),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get ratio(){return this._ratio}set ratio(e){this._ratio=Math.min(Math.max(0,e),1),this.checkTrim()}get(e,n=Gt.AsNew){return super.get(e,n)}peek(e){return super.get(e,Gt.None)}set(e,n){return super.set(e,n,Gt.Last),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}};ni.LRUCache=Ef});var __=I(bc=>{"use strict";Object.defineProperty(bc,"__esModule",{value:!0});bc.Disposable=void 0;var b_;(function(t){function e(n){return{dispose:n}}t.create=e})(b_||(bc.Disposable=b_={}))});var ri=I(Nf=>{"use strict";Object.defineProperty(Nf,"__esModule",{value:!0});var Mf;function Af(){if(Mf===void 0)throw new Error("No runtime abstraction layer installed");return Mf}(function(t){function e(n){if(n===void 0)throw new Error("No runtime abstraction layer provided");Mf=n}t.install=e})(Af||(Af={}));Nf.default=Af});var mo=I(po=>{"use strict";Object.defineProperty(po,"__esModule",{value:!0});po.Emitter=po.Event=void 0;var pA=ri(),v_;(function(t){let e={dispose(){}};t.None=function(){return e}})(v_||(po.Event=v_={}));var qf=class{add(e,n=null,i){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(n),Array.isArray(i)&&i.push({dispose:()=>this.remove(e,n)})}remove(e,n=null){if(!this._callbacks)return;let i=!1;for(let s=0,c=this._callbacks.length;s<c;s++)if(this._callbacks[s]===e)if(this._contexts[s]===n){this._callbacks.splice(s,1),this._contexts.splice(s,1);return}else i=!0;if(i)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let n=[],i=this._callbacks.slice(0),s=this._contexts.slice(0);for(let c=0,u=i.length;c<u;c++)try{n.push(i[c].apply(s[c],e))}catch(d){(0,pA.default)().console.error(d)}return n}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},_c=class t{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,n,i)=>{this._callbacks||(this._callbacks=new qf),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,n);let s={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,n),s.dispose=t._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(i)&&i.push(s),s}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};po.Emitter=_c;_c._noop=function(){}});var wc=I(bo=>{"use strict";Object.defineProperty(bo,"__esModule",{value:!0});bo.CancellationTokenSource=bo.CancellationToken=void 0;var mA=ri(),bA=go(),Of=mo(),vc;(function(t){t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Of.Event.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Of.Event.None});function e(n){let i=n;return i&&(i===t.None||i===t.Cancelled||bA.boolean(i.isCancellationRequested)&&!!i.onCancellationRequested)}t.is=e})(vc||(bo.CancellationToken=vc={}));var _A=Object.freeze(function(t,e){let n=(0,mA.default)().timer.setTimeout(t.bind(e),0);return{dispose(){n.dispose()}}}),yc=class{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?_A:(this._emitter||(this._emitter=new Of.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},Lf=class{get token(){return this._token||(this._token=new yc),this._token}cancel(){this._token?this._token.cancel():this._token=vc.Cancelled}dispose(){this._token?this._token instanceof yc&&this._token.dispose():this._token=vc.None}};bo.CancellationTokenSource=Lf});var y_=I(_o=>{"use strict";Object.defineProperty(_o,"__esModule",{value:!0});_o.SharedArrayReceiverStrategy=_o.SharedArraySenderStrategy=void 0;var vA=wc(),Ts;(function(t){t.Continue=0,t.Cancelled=1})(Ts||(Ts={}));var Bf=class{constructor(){this.buffers=new Map}enableCancellation(e){if(e.id===null)return;let n=new SharedArrayBuffer(4),i=new Int32Array(n,0,1);i[0]=Ts.Continue,this.buffers.set(e.id,n),e.$cancellationData=n}async sendCancellation(e,n){let i=this.buffers.get(n);if(i===void 0)return;let s=new Int32Array(i,0,1);Atomics.store(s,0,Ts.Cancelled)}cleanup(e){this.buffers.delete(e)}dispose(){this.buffers.clear()}};_o.SharedArraySenderStrategy=Bf;var Uf=class{constructor(e){this.data=new Int32Array(e,0,1)}get isCancellationRequested(){return Atomics.load(this.data,0)===Ts.Cancelled}get onCancellationRequested(){throw new Error("Cancellation over SharedArrayBuffer doesn't support cancellation events")}},$f=class{constructor(e){this.token=new Uf(e)}cancel(){}dispose(){}},Wf=class{constructor(){this.kind="request"}createCancellationTokenSource(e){let n=e.$cancellationData;return n===void 0?new vA.CancellationTokenSource:new $f(n)}};_o.SharedArrayReceiverStrategy=Wf});var zf=I(Sc=>{"use strict";Object.defineProperty(Sc,"__esModule",{value:!0});Sc.Semaphore=void 0;var yA=ri(),jf=class{constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise((n,i)=>{this._waiting.push({thunk:e,resolve:n,reject:i}),this.runNext()})}get active(){return this._active}runNext(){this._waiting.length===0||this._active===this._capacity||(0,yA.default)().timer.setImmediate(()=>this.doRunNext())}doRunNext(){if(this._waiting.length===0||this._active===this._capacity)return;let e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{let n=e.thunk();n instanceof Promise?n.then(i=>{this._active--,e.resolve(i),this.runNext()},i=>{this._active--,e.reject(i),this.runNext()}):(this._active--,e.resolve(n),this.runNext())}catch(n){this._active--,e.reject(n),this.runNext()}}};Sc.Semaphore=jf});var S_=I(ii=>{"use strict";Object.defineProperty(ii,"__esModule",{value:!0});ii.ReadableStreamMessageReader=ii.AbstractMessageReader=ii.MessageReader=void 0;var Vf=ri(),vo=go(),Hf=mo(),wA=zf(),w_;(function(t){function e(n){let i=n;return i&&vo.func(i.listen)&&vo.func(i.dispose)&&vo.func(i.onError)&&vo.func(i.onClose)&&vo.func(i.onPartialMessage)}t.is=e})(w_||(ii.MessageReader=w_={}));var xc=class{constructor(){this.errorEmitter=new Hf.Emitter,this.closeEmitter=new Hf.Emitter,this.partialMessageEmitter=new Hf.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error(`Reader received error. Reason: ${vo.string(e.message)?e.message:"unknown"}`)}};ii.AbstractMessageReader=xc;var Gf;(function(t){function e(n){let i,s,c,u=new Map,d,f=new Map;if(n===void 0||typeof n=="string")i=n??"utf-8";else{if(i=n.charset??"utf-8",n.contentDecoder!==void 0&&(c=n.contentDecoder,u.set(c.name,c)),n.contentDecoders!==void 0)for(let m of n.contentDecoders)u.set(m.name,m);if(n.contentTypeDecoder!==void 0&&(d=n.contentTypeDecoder,f.set(d.name,d)),n.contentTypeDecoders!==void 0)for(let m of n.contentTypeDecoders)f.set(m.name,m)}return d===void 0&&(d=(0,Vf.default)().applicationJson.decoder,f.set(d.name,d)),{charset:i,contentDecoder:c,contentDecoders:u,contentTypeDecoder:d,contentTypeDecoders:f}}t.fromOptions=e})(Gf||(Gf={}));var Qf=class extends xc{constructor(e,n){super(),this.readable=e,this.options=Gf.fromOptions(n),this.buffer=(0,Vf.default)().messageBuffer.create(this.options.charset),this._partialMessageTimeout=1e4,this.nextMessageLength=-1,this.messageToken=0,this.readSemaphore=new wA.Semaphore(1)}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e;let n=this.readable.onData(i=>{this.onData(i)});return this.readable.onError(i=>this.fireError(i)),this.readable.onClose(()=>this.fireClose()),n}onData(e){try{for(this.buffer.append(e);;){if(this.nextMessageLength===-1){let i=this.buffer.tryReadHeaders(!0);if(!i)return;let s=i.get("content-length");if(!s){this.fireError(new Error(`Header must provide a Content-Length property.
${JSON.stringify(Object.fromEntries(i))}`));return}let c=parseInt(s);if(isNaN(c)){this.fireError(new Error(`Content-Length value must be a number. Got ${s}`));return}this.nextMessageLength=c}let n=this.buffer.tryReadBody(this.nextMessageLength);if(n===void 0){this.setPartialMessageTimer();return}this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.readSemaphore.lock(async()=>{let i=this.options.contentDecoder!==void 0?await this.options.contentDecoder.decode(n):n,s=await this.options.contentTypeDecoder.decode(i,this.options);this.callback(s)}).catch(i=>{this.fireError(i)})}}catch(n){this.fireError(n)}}clearPartialMessageTimer(){this.partialMessageTimer&&(this.partialMessageTimer.dispose(),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),!(this._partialMessageTimeout<=0)&&(this.partialMessageTimer=(0,Vf.default)().timer.setTimeout((e,n)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:n}),this.setPartialMessageTimer())},this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}};ii.ReadableStreamMessageReader=Qf});var T_=I(oi=>{"use strict";Object.defineProperty(oi,"__esModule",{value:!0});oi.WriteableStreamMessageWriter=oi.AbstractMessageWriter=oi.MessageWriter=void 0;var x_=ri(),Rs=go(),SA=zf(),P_=mo(),xA="Content-Length: ",C_=`\r
`,k_;(function(t){function e(n){let i=n;return i&&Rs.func(i.dispose)&&Rs.func(i.onClose)&&Rs.func(i.onError)&&Rs.func(i.write)}t.is=e})(k_||(oi.MessageWriter=k_={}));var Pc=class{constructor(){this.errorEmitter=new P_.Emitter,this.closeEmitter=new P_.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,n,i){this.errorEmitter.fire([this.asError(e),n,i])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error(`Writer received error. Reason: ${Rs.string(e.message)?e.message:"unknown"}`)}};oi.AbstractMessageWriter=Pc;var Kf;(function(t){function e(n){return n===void 0||typeof n=="string"?{charset:n??"utf-8",contentTypeEncoder:(0,x_.default)().applicationJson.encoder}:{charset:n.charset??"utf-8",contentEncoder:n.contentEncoder,contentTypeEncoder:n.contentTypeEncoder??(0,x_.default)().applicationJson.encoder}}t.fromOptions=e})(Kf||(Kf={}));var Jf=class extends Pc{constructor(e,n){super(),this.writable=e,this.options=Kf.fromOptions(n),this.errorCount=0,this.writeSemaphore=new SA.Semaphore(1),this.writable.onError(i=>this.fireError(i)),this.writable.onClose(()=>this.fireClose())}async write(e){return this.writeSemaphore.lock(async()=>this.options.contentTypeEncoder.encode(e,this.options).then(i=>this.options.contentEncoder!==void 0?this.options.contentEncoder.encode(i):i).then(i=>{let s=[];return s.push(xA,i.byteLength.toString(),C_),s.push(C_),this.doWrite(e,s,i)},i=>{throw this.fireError(i),i}))}async doWrite(e,n,i){try{return await this.writable.write(n.join(""),"ascii"),this.writable.write(i)}catch(s){return this.handleError(s,e),Promise.reject(s)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){this.writable.end()}};oi.WriteableStreamMessageWriter=Jf});var R_=I(Cc=>{"use strict";Object.defineProperty(Cc,"__esModule",{value:!0});Cc.AbstractMessageBuffer=void 0;var PA=13,CA=10,kA=`\r
`,Xf=class{constructor(e="utf-8"){this._encoding=e,this._chunks=[],this._totalLength=0}get encoding(){return this._encoding}append(e){let n=typeof e=="string"?this.fromString(e,this._encoding):e;this._chunks.push(n),this._totalLength+=n.byteLength}tryReadHeaders(e=!1){if(this._chunks.length===0)return;let n=0,i=0,s=0,c=0;e:for(;i<this._chunks.length;){let m=this._chunks[i];for(s=0;s<m.length;){switch(m[s]){case PA:switch(n){case 0:n=1;break;case 2:n=3;break;default:n=0}break;case CA:switch(n){case 1:n=2;break;case 3:n=4,s++;break e;default:n=0}break;default:n=0}s++}c+=m.byteLength,i++}if(n!==4)return;let u=this._read(c+s),d=new Map,f=this.toString(u,"ascii").split(kA);if(f.length<2)return d;for(let m=0;m<f.length-2;m++){let y=f[m],x=y.indexOf(":");if(x===-1)throw new Error(`Message header must separate key and value using ':'
${y}`);let D=y.substr(0,x),N=y.substr(x+1).trim();d.set(e?D.toLowerCase():D,N)}return d}tryReadBody(e){if(!(this._totalLength<e))return this._read(e)}get numberOfBytes(){return this._totalLength}_read(e){if(e===0)return this.emptyBuffer();if(e>this._totalLength)throw new Error("Cannot read so many bytes!");if(this._chunks[0].byteLength===e){let c=this._chunks[0];return this._chunks.shift(),this._totalLength-=e,this.asNative(c)}if(this._chunks[0].byteLength>e){let c=this._chunks[0],u=this.asNative(c,e);return this._chunks[0]=c.slice(e),this._totalLength-=e,u}let n=this.allocNative(e),i=0,s=0;for(;e>0;){let c=this._chunks[s];if(c.byteLength>e){let u=c.slice(0,e);n.set(u,i),i+=e,this._chunks[s]=c.slice(e),this._totalLength-=e,e-=e}else n.set(c,i),i+=c.byteLength,this._chunks.shift(),this._totalLength-=c.byteLength,e-=c.byteLength}return n}};Cc.AbstractMessageBuffer=Xf});var M_=I(Ce=>{"use strict";Object.defineProperty(Ce,"__esModule",{value:!0});Ce.createMessageConnection=Ce.ConnectionOptions=Ce.MessageStrategy=Ce.CancellationStrategy=Ce.CancellationSenderStrategy=Ce.CancellationReceiverStrategy=Ce.RequestCancellationReceiverStrategy=Ce.IdCancellationReceiverStrategy=Ce.ConnectionStrategy=Ce.ConnectionError=Ce.ConnectionErrors=Ce.LogTraceNotification=Ce.SetTraceNotification=Ce.TraceFormat=Ce.TraceValues=Ce.Trace=Ce.NullLogger=Ce.ProgressType=Ce.ProgressToken=void 0;var D_=ri(),_t=go(),he=Ff(),F_=If(),Ds=mo(),Yf=wc(),Is;(function(t){t.type=new he.NotificationType("$/cancelRequest")})(Is||(Is={}));var Zf;(function(t){function e(n){return typeof n=="string"||typeof n=="number"}t.is=e})(Zf||(Ce.ProgressToken=Zf={}));var Fs;(function(t){t.type=new he.NotificationType("$/progress")})(Fs||(Fs={}));var eh=class{constructor(){}};Ce.ProgressType=eh;var th;(function(t){function e(n){return _t.func(n)}t.is=e})(th||(th={}));Ce.NullLogger=Object.freeze({error:()=>{},warn:()=>{},info:()=>{},log:()=>{}});var He;(function(t){t[t.Off=0]="Off",t[t.Messages=1]="Messages",t[t.Compact=2]="Compact",t[t.Verbose=3]="Verbose"})(He||(Ce.Trace=He={}));var E_;(function(t){t.Off="off",t.Messages="messages",t.Compact="compact",t.Verbose="verbose"})(E_||(Ce.TraceValues=E_={}));(function(t){function e(i){if(!_t.string(i))return t.Off;switch(i=i.toLowerCase(),i){case"off":return t.Off;case"messages":return t.Messages;case"compact":return t.Compact;case"verbose":return t.Verbose;default:return t.Off}}t.fromString=e;function n(i){switch(i){case t.Off:return"off";case t.Messages:return"messages";case t.Compact:return"compact";case t.Verbose:return"verbose";default:return"off"}}t.toString=n})(He||(Ce.Trace=He={}));var Tn;(function(t){t.Text="text",t.JSON="json"})(Tn||(Ce.TraceFormat=Tn={}));(function(t){function e(n){return _t.string(n)?(n=n.toLowerCase(),n==="json"?t.JSON:t.Text):t.Text}t.fromString=e})(Tn||(Ce.TraceFormat=Tn={}));var nh;(function(t){t.type=new he.NotificationType("$/setTrace")})(nh||(Ce.SetTraceNotification=nh={}));var kc;(function(t){t.type=new he.NotificationType("$/logTrace")})(kc||(Ce.LogTraceNotification=kc={}));var Es;(function(t){t[t.Closed=1]="Closed",t[t.Disposed=2]="Disposed",t[t.AlreadyListening=3]="AlreadyListening"})(Es||(Ce.ConnectionErrors=Es={}));var yo=class t extends Error{constructor(e,n){super(n),this.code=e,Object.setPrototypeOf(this,t.prototype)}};Ce.ConnectionError=yo;var rh;(function(t){function e(n){let i=n;return i&&_t.func(i.cancelUndispatched)}t.is=e})(rh||(Ce.ConnectionStrategy=rh={}));var Tc;(function(t){function e(n){let i=n;return i&&(i.kind===void 0||i.kind==="id")&&_t.func(i.createCancellationTokenSource)&&(i.dispose===void 0||_t.func(i.dispose))}t.is=e})(Tc||(Ce.IdCancellationReceiverStrategy=Tc={}));var ih;(function(t){function e(n){let i=n;return i&&i.kind==="request"&&_t.func(i.createCancellationTokenSource)&&(i.dispose===void 0||_t.func(i.dispose))}t.is=e})(ih||(Ce.RequestCancellationReceiverStrategy=ih={}));var Rc;(function(t){t.Message=Object.freeze({createCancellationTokenSource(n){return new Yf.CancellationTokenSource}});function e(n){return Tc.is(n)||ih.is(n)}t.is=e})(Rc||(Ce.CancellationReceiverStrategy=Rc={}));var Dc;(function(t){t.Message=Object.freeze({sendCancellation(n,i){return n.sendNotification(Is.type,{id:i})},cleanup(n){}});function e(n){let i=n;return i&&_t.func(i.sendCancellation)&&_t.func(i.cleanup)}t.is=e})(Dc||(Ce.CancellationSenderStrategy=Dc={}));var Fc;(function(t){t.Message=Object.freeze({receiver:Rc.Message,sender:Dc.Message});function e(n){let i=n;return i&&Rc.is(i.receiver)&&Dc.is(i.sender)}t.is=e})(Fc||(Ce.CancellationStrategy=Fc={}));var Ec;(function(t){function e(n){let i=n;return i&&_t.func(i.handleMessage)}t.is=e})(Ec||(Ce.MessageStrategy=Ec={}));var I_;(function(t){function e(n){let i=n;return i&&(Fc.is(i.cancellationStrategy)||rh.is(i.connectionStrategy)||Ec.is(i.messageStrategy))}t.is=e})(I_||(Ce.ConnectionOptions=I_={}));var Zn;(function(t){t[t.New=1]="New",t[t.Listening=2]="Listening",t[t.Closed=3]="Closed",t[t.Disposed=4]="Disposed"})(Zn||(Zn={}));function TA(t,e,n,i){let s=n!==void 0?n:Ce.NullLogger,c=0,u=0,d=0,f="2.0",m,y=new Map,x,D=new Map,N=new Map,A,L=new F_.LinkedMap,B=new Map,K=new Set,F=new Map,P=He.Off,U=Tn.Text,te,ye=Zn.New,Be=new Ds.Emitter,Je=new Ds.Emitter,Dt=new Ds.Emitter,xe=new Ds.Emitter,X=new Ds.Emitter,z=i&&i.cancellationStrategy?i.cancellationStrategy:Fc.Message;function se(C){if(C===null)throw new Error("Can't send requests with id null since the response can't be correlated.");return"req-"+C.toString()}function re(C){return C===null?"res-unknown-"+(++d).toString():"res-"+C.toString()}function De(){return"not-"+(++u).toString()}function ge(C,H){he.Message.isRequest(H)?C.set(se(H.id),H):he.Message.isResponse(H)?C.set(re(H.id),H):C.set(De(),H)}function me(C){}function St(){return ye===Zn.Listening}function We(){return ye===Zn.Closed}function je(){return ye===Zn.Disposed}function bn(){(ye===Zn.New||ye===Zn.Listening)&&(ye=Zn.Closed,Je.fire(void 0))}function yr(C){Be.fire([C,void 0,void 0])}function Vn(C){Be.fire(C)}t.onClose(bn),t.onError(yr),e.onClose(bn),e.onError(Vn);function ur(){A||L.size===0||(A=(0,D_.default)().timer.setImmediate(()=>{A=void 0,lr()}))}function xi(C){he.Message.isRequest(C)?Ki(C):he.Message.isNotification(C)?Sr(C):he.Message.isResponse(C)?wr(C):xr(C)}function lr(){if(L.size===0)return;let C=L.shift();try{let H=i?.messageStrategy;Ec.is(H)?H.handleMessage(C,xi):xi(C)}finally{ur()}}let dr=C=>{try{if(he.Message.isNotification(C)&&C.method===Is.type.method){let H=C.params.id,Z=se(H),ie=L.get(Z);if(he.Message.isRequest(ie)){let Le=i?.connectionStrategy,Ge=Le&&Le.cancelUndispatched?Le.cancelUndispatched(ie,me):void 0;if(Ge&&(Ge.error!==void 0||Ge.result!==void 0)){L.delete(Z),F.delete(H),Ge.id=ie.id,Bt(Ge,C.method,Date.now()),e.write(Ge).catch(()=>s.error("Sending response for canceled message failed."));return}}let Ue=F.get(H);if(Ue!==void 0){Ue.cancel(),zr(C);return}else K.add(H)}ge(L,C)}finally{ur()}};function Ki(C){if(je())return;function H(Fe,Qe,Ne){let ct={jsonrpc:f,id:C.id};Fe instanceof he.ResponseError?ct.error=Fe.toJson():ct.result=Fe===void 0?null:Fe,Bt(ct,Qe,Ne),e.write(ct).catch(()=>s.error("Sending response failed."))}function Z(Fe,Qe,Ne){let ct={jsonrpc:f,id:C.id,error:Fe.toJson()};Bt(ct,Qe,Ne),e.write(ct).catch(()=>s.error("Sending response failed."))}function ie(Fe,Qe,Ne){Fe===void 0&&(Fe=null);let ct={jsonrpc:f,id:C.id,result:Fe};Bt(ct,Qe,Ne),e.write(ct).catch(()=>s.error("Sending response failed."))}Pi(C);let Ue=y.get(C.method),Le,Ge;Ue&&(Le=Ue.type,Ge=Ue.handler);let tt=Date.now();if(Ge||m){let Fe=C.id??String(Date.now()),Qe=Tc.is(z.receiver)?z.receiver.createCancellationTokenSource(Fe):z.receiver.createCancellationTokenSource(C);C.id!==null&&K.has(C.id)&&Qe.cancel(),C.id!==null&&F.set(Fe,Qe);try{let Ne;if(Ge)if(C.params===void 0){if(Le!==void 0&&Le.numberOfParams!==0){Z(new he.ResponseError(he.ErrorCodes.InvalidParams,`Request ${C.method} defines ${Le.numberOfParams} params but received none.`),C.method,tt);return}Ne=Ge(Qe.token)}else if(Array.isArray(C.params)){if(Le!==void 0&&Le.parameterStructures===he.ParameterStructures.byName){Z(new he.ResponseError(he.ErrorCodes.InvalidParams,`Request ${C.method} defines parameters by name but received parameters by position`),C.method,tt);return}Ne=Ge(...C.params,Qe.token)}else{if(Le!==void 0&&Le.parameterStructures===he.ParameterStructures.byPosition){Z(new he.ResponseError(he.ErrorCodes.InvalidParams,`Request ${C.method} defines parameters by position but received parameters by name`),C.method,tt);return}Ne=Ge(C.params,Qe.token)}else m&&(Ne=m(C.method,C.params,Qe.token));let ct=Ne;Ne?ct.then?ct.then(Ft=>{F.delete(Fe),H(Ft,C.method,tt)},Ft=>{F.delete(Fe),Ft instanceof he.ResponseError?Z(Ft,C.method,tt):Ft&&_t.string(Ft.message)?Z(new he.ResponseError(he.ErrorCodes.InternalError,`Request ${C.method} failed with message: ${Ft.message}`),C.method,tt):Z(new he.ResponseError(he.ErrorCodes.InternalError,`Request ${C.method} failed unexpectedly without providing any details.`),C.method,tt)}):(F.delete(Fe),H(Ne,C.method,tt)):(F.delete(Fe),ie(Ne,C.method,tt))}catch(Ne){F.delete(Fe),Ne instanceof he.ResponseError?H(Ne,C.method,tt):Ne&&_t.string(Ne.message)?Z(new he.ResponseError(he.ErrorCodes.InternalError,`Request ${C.method} failed with message: ${Ne.message}`),C.method,tt):Z(new he.ResponseError(he.ErrorCodes.InternalError,`Request ${C.method} failed unexpectedly without providing any details.`),C.method,tt)}}else Z(new he.ResponseError(he.ErrorCodes.MethodNotFound,`Unhandled method ${C.method}`),C.method,tt)}function wr(C){if(!je())if(C.id===null)C.error?s.error(`Received response message without id: Error is: 
${JSON.stringify(C.error,void 0,4)}`):s.error("Received response message without id. No further error information provided.");else{let H=C.id,Z=B.get(H);if(hr(C,Z),Z!==void 0){B.delete(H);try{if(C.error){let ie=C.error;Z.reject(new he.ResponseError(ie.code,ie.message,ie.data))}else if(C.result!==void 0)Z.resolve(C.result);else throw new Error("Should never happen.")}catch(ie){ie.message?s.error(`Response handler '${Z.method}' failed with message: ${ie.message}`):s.error(`Response handler '${Z.method}' failed unexpectedly.`)}}}}function Sr(C){if(je())return;let H,Z;if(C.method===Is.type.method){let ie=C.params.id;K.delete(ie),zr(C);return}else{let ie=D.get(C.method);ie&&(Z=ie.handler,H=ie.type)}if(Z||x)try{if(zr(C),Z)if(C.params===void 0)H!==void 0&&H.numberOfParams!==0&&H.parameterStructures!==he.ParameterStructures.byName&&s.error(`Notification ${C.method} defines ${H.numberOfParams} params but received none.`),Z();else if(Array.isArray(C.params)){let ie=C.params;C.method===Fs.type.method&&ie.length===2&&Zf.is(ie[0])?Z({token:ie[0],value:ie[1]}):(H!==void 0&&(H.parameterStructures===he.ParameterStructures.byName&&s.error(`Notification ${C.method} defines parameters by name but received parameters by position`),H.numberOfParams!==C.params.length&&s.error(`Notification ${C.method} defines ${H.numberOfParams} params but received ${ie.length} arguments`)),Z(...ie))}else H!==void 0&&H.parameterStructures===he.ParameterStructures.byPosition&&s.error(`Notification ${C.method} defines parameters by position but received parameters by name`),Z(C.params);else x&&x(C.method,C.params)}catch(ie){ie.message?s.error(`Notification handler '${C.method}' failed with message: ${ie.message}`):s.error(`Notification handler '${C.method}' failed unexpectedly.`)}else Dt.fire(C)}function xr(C){if(!C){s.error("Received empty message.");return}s.error(`Received message which is neither a response nor a notification message:
${JSON.stringify(C,null,4)}`);let H=C;if(_t.string(H.id)||_t.number(H.id)){let Z=H.id,ie=B.get(Z);ie&&ie.reject(new Error("The received response has neither a result nor an error property."))}}function mt(C){if(C!=null)switch(P){case He.Verbose:return JSON.stringify(C,null,4);case He.Compact:return JSON.stringify(C);default:return}}function fr(C){if(!(P===He.Off||!te))if(U===Tn.Text){let H;(P===He.Verbose||P===He.Compact)&&C.params&&(H=`Params: ${mt(C.params)}

`),te.log(`Sending request '${C.method} - (${C.id})'.`,H)}else xt("send-request",C)}function Ji(C){if(!(P===He.Off||!te))if(U===Tn.Text){let H;(P===He.Verbose||P===He.Compact)&&(C.params?H=`Params: ${mt(C.params)}

`:H=`No parameters provided.

`),te.log(`Sending notification '${C.method}'.`,H)}else xt("send-notification",C)}function Bt(C,H,Z){if(!(P===He.Off||!te))if(U===Tn.Text){let ie;(P===He.Verbose||P===He.Compact)&&(C.error&&C.error.data?ie=`Error data: ${mt(C.error.data)}

`:C.result?ie=`Result: ${mt(C.result)}

`:C.error===void 0&&(ie=`No result returned.

`)),te.log(`Sending response '${H} - (${C.id})'. Processing request took ${Date.now()-Z}ms`,ie)}else xt("send-response",C)}function Pi(C){if(!(P===He.Off||!te))if(U===Tn.Text){let H;(P===He.Verbose||P===He.Compact)&&C.params&&(H=`Params: ${mt(C.params)}

`),te.log(`Received request '${C.method} - (${C.id})'.`,H)}else xt("receive-request",C)}function zr(C){if(!(P===He.Off||!te||C.method===kc.type.method))if(U===Tn.Text){let H;(P===He.Verbose||P===He.Compact)&&(C.params?H=`Params: ${mt(C.params)}

`:H=`No parameters provided.

`),te.log(`Received notification '${C.method}'.`,H)}else xt("receive-notification",C)}function hr(C,H){if(!(P===He.Off||!te))if(U===Tn.Text){let Z;if((P===He.Verbose||P===He.Compact)&&(C.error&&C.error.data?Z=`Error data: ${mt(C.error.data)}

`:C.result?Z=`Result: ${mt(C.result)}

`:C.error===void 0&&(Z=`No result returned.

`)),H){let ie=C.error?` Request failed: ${C.error.message} (${C.error.code}).`:"";te.log(`Received response '${H.method} - (${C.id})' in ${Date.now()-H.timerStart}ms.${ie}`,Z)}else te.log(`Received response ${C.id} without active response promise.`,Z)}else xt("receive-response",C)}function xt(C,H){if(!te||P===He.Off)return;let Z={isLSPMessage:!0,type:C,message:H,timestamp:Date.now()};te.log(Z)}function sn(){if(We())throw new yo(Es.Closed,"Connection is closed.");if(je())throw new yo(Es.Disposed,"Connection is disposed.")}function Pr(){if(St())throw new yo(Es.AlreadyListening,"Connection is already listening")}function Xi(){if(!St())throw new Error("Call listen() first.")}function jt(C){return C===void 0?null:C}function Ci(C){if(C!==null)return C}function Gn(C){return C!=null&&!Array.isArray(C)&&typeof C=="object"}function _n(C,H){switch(C){case he.ParameterStructures.auto:return Gn(H)?Ci(H):[jt(H)];case he.ParameterStructures.byName:if(!Gn(H))throw new Error("Received parameters by name but param is not an object literal.");return Ci(H);case he.ParameterStructures.byPosition:return[jt(H)];default:throw new Error(`Unknown parameter structure ${C.toString()}`)}}function Cr(C,H){let Z,ie=C.numberOfParams;switch(ie){case 0:Z=void 0;break;case 1:Z=_n(C.parameterStructures,H[0]);break;default:Z=[];for(let Ue=0;Ue<H.length&&Ue<ie;Ue++)Z.push(jt(H[Ue]));if(H.length<ie)for(let Ue=H.length;Ue<ie;Ue++)Z.push(null);break}return Z}let vn={sendNotification:(C,...H)=>{sn();let Z,ie;if(_t.string(C)){Z=C;let Le=H[0],Ge=0,tt=he.ParameterStructures.auto;he.ParameterStructures.is(Le)&&(Ge=1,tt=Le);let Fe=H.length,Qe=Fe-Ge;switch(Qe){case 0:ie=void 0;break;case 1:ie=_n(tt,H[Ge]);break;default:if(tt===he.ParameterStructures.byName)throw new Error(`Received ${Qe} parameters for 'by Name' notification parameter structure.`);ie=H.slice(Ge,Fe).map(Ne=>jt(Ne));break}}else{let Le=H;Z=C.method,ie=Cr(C,Le)}let Ue={jsonrpc:f,method:Z,params:ie};return Ji(Ue),e.write(Ue).catch(Le=>{throw s.error("Sending notification failed."),Le})},onNotification:(C,H)=>{sn();let Z;return _t.func(C)?x=C:H&&(_t.string(C)?(Z=C,D.set(C,{type:void 0,handler:H})):(Z=C.method,D.set(C.method,{type:C,handler:H}))),{dispose:()=>{Z!==void 0?D.delete(Z):x=void 0}}},onProgress:(C,H,Z)=>{if(N.has(H))throw new Error(`Progress handler for token ${H} already registered`);return N.set(H,Z),{dispose:()=>{N.delete(H)}}},sendProgress:(C,H,Z)=>vn.sendNotification(Fs.type,{token:H,value:Z}),onUnhandledProgress:xe.event,sendRequest:(C,...H)=>{sn(),Xi();let Z,ie,Ue;if(_t.string(C)){Z=C;let Fe=H[0],Qe=H[H.length-1],Ne=0,ct=he.ParameterStructures.auto;he.ParameterStructures.is(Fe)&&(Ne=1,ct=Fe);let Ft=H.length;Yf.CancellationToken.is(Qe)&&(Ft=Ft-1,Ue=Qe);let Zt=Ft-Ne;switch(Zt){case 0:ie=void 0;break;case 1:ie=_n(ct,H[Ne]);break;default:if(ct===he.ParameterStructures.byName)throw new Error(`Received ${Zt} parameters for 'by Name' request parameter structure.`);ie=H.slice(Ne,Ft).map(Yi=>jt(Yi));break}}else{let Fe=H;Z=C.method,ie=Cr(C,Fe);let Qe=C.numberOfParams;Ue=Yf.CancellationToken.is(Fe[Qe])?Fe[Qe]:void 0}let Le=c++,Ge;Ue&&(Ge=Ue.onCancellationRequested(()=>{let Fe=z.sender.sendCancellation(vn,Le);return Fe===void 0?(s.log(`Received no promise from cancellation strategy when cancelling id ${Le}`),Promise.resolve()):Fe.catch(()=>{s.log(`Sending cancellation messages for id ${Le} failed`)})}));let tt={jsonrpc:f,id:Le,method:Z,params:ie};return fr(tt),typeof z.sender.enableCancellation=="function"&&z.sender.enableCancellation(tt),new Promise(async(Fe,Qe)=>{let Ne=Zt=>{Fe(Zt),z.sender.cleanup(Le),Ge?.dispose()},ct=Zt=>{Qe(Zt),z.sender.cleanup(Le),Ge?.dispose()},Ft={method:Z,timerStart:Date.now(),resolve:Ne,reject:ct};try{await e.write(tt),B.set(Le,Ft)}catch(Zt){throw s.error("Sending request failed."),Ft.reject(new he.ResponseError(he.ErrorCodes.MessageWriteError,Zt.message?Zt.message:"Unknown reason")),Zt}})},onRequest:(C,H)=>{sn();let Z=null;return th.is(C)?(Z=void 0,m=C):_t.string(C)?(Z=null,H!==void 0&&(Z=C,y.set(C,{handler:H,type:void 0}))):H!==void 0&&(Z=C.method,y.set(C.method,{type:C,handler:H})),{dispose:()=>{Z!==null&&(Z!==void 0?y.delete(Z):m=void 0)}}},hasPendingResponse:()=>B.size>0,trace:async(C,H,Z)=>{let ie=!1,Ue=Tn.Text;Z!==void 0&&(_t.boolean(Z)?ie=Z:(ie=Z.sendNotification||!1,Ue=Z.traceFormat||Tn.Text)),P=C,U=Ue,P===He.Off?te=void 0:te=H,ie&&!We()&&!je()&&await vn.sendNotification(nh.type,{value:He.toString(C)})},onError:Be.event,onClose:Je.event,onUnhandledNotification:Dt.event,onDispose:X.event,end:()=>{e.end()},dispose:()=>{if(je())return;ye=Zn.Disposed,X.fire(void 0);let C=new he.ResponseError(he.ErrorCodes.PendingResponseRejected,"Pending response rejected since connection got disposed");for(let H of B.values())H.reject(C);B=new Map,F=new Map,K=new Set,L=new F_.LinkedMap,_t.func(e.dispose)&&e.dispose(),_t.func(t.dispose)&&t.dispose()},listen:()=>{sn(),Pr(),ye=Zn.Listening,t.listen(dr)},inspect:()=>{(0,D_.default)().console.log("inspect")}};return vn.onNotification(kc.type,C=>{if(P===He.Off||!te)return;let H=P===He.Verbose||P===He.Compact;te.log(C.message,H?C.verbose:void 0)}),vn.onNotification(Fs.type,C=>{let H=N.get(C.token);H?H(C.value):xe.fire(C)}),vn}Ce.createMessageConnection=TA});var Ic=I(j=>{"use strict";Object.defineProperty(j,"__esModule",{value:!0});j.ProgressType=j.ProgressToken=j.createMessageConnection=j.NullLogger=j.ConnectionOptions=j.ConnectionStrategy=j.AbstractMessageBuffer=j.WriteableStreamMessageWriter=j.AbstractMessageWriter=j.MessageWriter=j.ReadableStreamMessageReader=j.AbstractMessageReader=j.MessageReader=j.SharedArrayReceiverStrategy=j.SharedArraySenderStrategy=j.CancellationToken=j.CancellationTokenSource=j.Emitter=j.Event=j.Disposable=j.LRUCache=j.Touch=j.LinkedMap=j.ParameterStructures=j.NotificationType9=j.NotificationType8=j.NotificationType7=j.NotificationType6=j.NotificationType5=j.NotificationType4=j.NotificationType3=j.NotificationType2=j.NotificationType1=j.NotificationType0=j.NotificationType=j.ErrorCodes=j.ResponseError=j.RequestType9=j.RequestType8=j.RequestType7=j.RequestType6=j.RequestType5=j.RequestType4=j.RequestType3=j.RequestType2=j.RequestType1=j.RequestType0=j.RequestType=j.Message=j.RAL=void 0;j.MessageStrategy=j.CancellationStrategy=j.CancellationSenderStrategy=j.CancellationReceiverStrategy=j.ConnectionError=j.ConnectionErrors=j.LogTraceNotification=j.SetTraceNotification=j.TraceFormat=j.TraceValues=j.Trace=void 0;var rt=Ff();Object.defineProperty(j,"Message",{enumerable:!0,get:function(){return rt.Message}});Object.defineProperty(j,"RequestType",{enumerable:!0,get:function(){return rt.RequestType}});Object.defineProperty(j,"RequestType0",{enumerable:!0,get:function(){return rt.RequestType0}});Object.defineProperty(j,"RequestType1",{enumerable:!0,get:function(){return rt.RequestType1}});Object.defineProperty(j,"RequestType2",{enumerable:!0,get:function(){return rt.RequestType2}});Object.defineProperty(j,"RequestType3",{enumerable:!0,get:function(){return rt.RequestType3}});Object.defineProperty(j,"RequestType4",{enumerable:!0,get:function(){return rt.RequestType4}});Object.defineProperty(j,"RequestType5",{enumerable:!0,get:function(){return rt.RequestType5}});Object.defineProperty(j,"RequestType6",{enumerable:!0,get:function(){return rt.RequestType6}});Object.defineProperty(j,"RequestType7",{enumerable:!0,get:function(){return rt.RequestType7}});Object.defineProperty(j,"RequestType8",{enumerable:!0,get:function(){return rt.RequestType8}});Object.defineProperty(j,"RequestType9",{enumerable:!0,get:function(){return rt.RequestType9}});Object.defineProperty(j,"ResponseError",{enumerable:!0,get:function(){return rt.ResponseError}});Object.defineProperty(j,"ErrorCodes",{enumerable:!0,get:function(){return rt.ErrorCodes}});Object.defineProperty(j,"NotificationType",{enumerable:!0,get:function(){return rt.NotificationType}});Object.defineProperty(j,"NotificationType0",{enumerable:!0,get:function(){return rt.NotificationType0}});Object.defineProperty(j,"NotificationType1",{enumerable:!0,get:function(){return rt.NotificationType1}});Object.defineProperty(j,"NotificationType2",{enumerable:!0,get:function(){return rt.NotificationType2}});Object.defineProperty(j,"NotificationType3",{enumerable:!0,get:function(){return rt.NotificationType3}});Object.defineProperty(j,"NotificationType4",{enumerable:!0,get:function(){return rt.NotificationType4}});Object.defineProperty(j,"NotificationType5",{enumerable:!0,get:function(){return rt.NotificationType5}});Object.defineProperty(j,"NotificationType6",{enumerable:!0,get:function(){return rt.NotificationType6}});Object.defineProperty(j,"NotificationType7",{enumerable:!0,get:function(){return rt.NotificationType7}});Object.defineProperty(j,"NotificationType8",{enumerable:!0,get:function(){return rt.NotificationType8}});Object.defineProperty(j,"NotificationType9",{enumerable:!0,get:function(){return rt.NotificationType9}});Object.defineProperty(j,"ParameterStructures",{enumerable:!0,get:function(){return rt.ParameterStructures}});var oh=If();Object.defineProperty(j,"LinkedMap",{enumerable:!0,get:function(){return oh.LinkedMap}});Object.defineProperty(j,"LRUCache",{enumerable:!0,get:function(){return oh.LRUCache}});Object.defineProperty(j,"Touch",{enumerable:!0,get:function(){return oh.Touch}});var RA=__();Object.defineProperty(j,"Disposable",{enumerable:!0,get:function(){return RA.Disposable}});var A_=mo();Object.defineProperty(j,"Event",{enumerable:!0,get:function(){return A_.Event}});Object.defineProperty(j,"Emitter",{enumerable:!0,get:function(){return A_.Emitter}});var N_=wc();Object.defineProperty(j,"CancellationTokenSource",{enumerable:!0,get:function(){return N_.CancellationTokenSource}});Object.defineProperty(j,"CancellationToken",{enumerable:!0,get:function(){return N_.CancellationToken}});var q_=y_();Object.defineProperty(j,"SharedArraySenderStrategy",{enumerable:!0,get:function(){return q_.SharedArraySenderStrategy}});Object.defineProperty(j,"SharedArrayReceiverStrategy",{enumerable:!0,get:function(){return q_.SharedArrayReceiverStrategy}});var sh=S_();Object.defineProperty(j,"MessageReader",{enumerable:!0,get:function(){return sh.MessageReader}});Object.defineProperty(j,"AbstractMessageReader",{enumerable:!0,get:function(){return sh.AbstractMessageReader}});Object.defineProperty(j,"ReadableStreamMessageReader",{enumerable:!0,get:function(){return sh.ReadableStreamMessageReader}});var ah=T_();Object.defineProperty(j,"MessageWriter",{enumerable:!0,get:function(){return ah.MessageWriter}});Object.defineProperty(j,"AbstractMessageWriter",{enumerable:!0,get:function(){return ah.AbstractMessageWriter}});Object.defineProperty(j,"WriteableStreamMessageWriter",{enumerable:!0,get:function(){return ah.WriteableStreamMessageWriter}});var DA=R_();Object.defineProperty(j,"AbstractMessageBuffer",{enumerable:!0,get:function(){return DA.AbstractMessageBuffer}});var $t=M_();Object.defineProperty(j,"ConnectionStrategy",{enumerable:!0,get:function(){return $t.ConnectionStrategy}});Object.defineProperty(j,"ConnectionOptions",{enumerable:!0,get:function(){return $t.ConnectionOptions}});Object.defineProperty(j,"NullLogger",{enumerable:!0,get:function(){return $t.NullLogger}});Object.defineProperty(j,"createMessageConnection",{enumerable:!0,get:function(){return $t.createMessageConnection}});Object.defineProperty(j,"ProgressToken",{enumerable:!0,get:function(){return $t.ProgressToken}});Object.defineProperty(j,"ProgressType",{enumerable:!0,get:function(){return $t.ProgressType}});Object.defineProperty(j,"Trace",{enumerable:!0,get:function(){return $t.Trace}});Object.defineProperty(j,"TraceValues",{enumerable:!0,get:function(){return $t.TraceValues}});Object.defineProperty(j,"TraceFormat",{enumerable:!0,get:function(){return $t.TraceFormat}});Object.defineProperty(j,"SetTraceNotification",{enumerable:!0,get:function(){return $t.SetTraceNotification}});Object.defineProperty(j,"LogTraceNotification",{enumerable:!0,get:function(){return $t.LogTraceNotification}});Object.defineProperty(j,"ConnectionErrors",{enumerable:!0,get:function(){return $t.ConnectionErrors}});Object.defineProperty(j,"ConnectionError",{enumerable:!0,get:function(){return $t.ConnectionError}});Object.defineProperty(j,"CancellationReceiverStrategy",{enumerable:!0,get:function(){return $t.CancellationReceiverStrategy}});Object.defineProperty(j,"CancellationSenderStrategy",{enumerable:!0,get:function(){return $t.CancellationSenderStrategy}});Object.defineProperty(j,"CancellationStrategy",{enumerable:!0,get:function(){return $t.CancellationStrategy}});Object.defineProperty(j,"MessageStrategy",{enumerable:!0,get:function(){return $t.MessageStrategy}});var FA=ri();j.RAL=FA.default});var B_=I(dh=>{"use strict";Object.defineProperty(dh,"__esModule",{value:!0});var O_=require("util"),Nr=Ic(),Mc=class t extends Nr.AbstractMessageBuffer{constructor(e="utf-8"){super(e)}emptyBuffer(){return t.emptyBuffer}fromString(e,n){return Buffer.from(e,n)}toString(e,n){return e instanceof Buffer?e.toString(n):new O_.TextDecoder(n).decode(e)}asNative(e,n){return n===void 0?e instanceof Buffer?e:Buffer.from(e):e instanceof Buffer?e.slice(0,n):Buffer.from(e,0,n)}allocNative(e){return Buffer.allocUnsafe(e)}};Mc.emptyBuffer=Buffer.allocUnsafe(0);var ch=class{constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),Nr.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),Nr.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),Nr.Disposable.create(()=>this.stream.off("end",e))}onData(e){return this.stream.on("data",e),Nr.Disposable.create(()=>this.stream.off("data",e))}},uh=class{constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),Nr.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),Nr.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),Nr.Disposable.create(()=>this.stream.off("end",e))}write(e,n){return new Promise((i,s)=>{let c=u=>{u==null?i():s(u)};typeof e=="string"?this.stream.write(e,n,c):this.stream.write(e,c)})}end(){this.stream.end()}},L_=Object.freeze({messageBuffer:Object.freeze({create:t=>new Mc(t)}),applicationJson:Object.freeze({encoder:Object.freeze({name:"application/json",encode:(t,e)=>{try{return Promise.resolve(Buffer.from(JSON.stringify(t,void 0,0),e.charset))}catch(n){return Promise.reject(n)}}}),decoder:Object.freeze({name:"application/json",decode:(t,e)=>{try{return t instanceof Buffer?Promise.resolve(JSON.parse(t.toString(e.charset))):Promise.resolve(JSON.parse(new O_.TextDecoder(e.charset).decode(t)))}catch(n){return Promise.reject(n)}}})}),stream:Object.freeze({asReadableStream:t=>new ch(t),asWritableStream:t=>new uh(t)}),console,timer:Object.freeze({setTimeout(t,e,...n){let i=setTimeout(t,e,...n);return{dispose:()=>clearTimeout(i)}},setImmediate(t,...e){let n=setImmediate(t,...e);return{dispose:()=>clearImmediate(n)}},setInterval(t,e,...n){let i=setInterval(t,e,...n);return{dispose:()=>clearInterval(i)}}})});function lh(){return L_}(function(t){function e(){Nr.RAL.install(L_)}t.install=e})(lh||(lh={}));dh.default=lh});var Bi=I(Oe=>{"use strict";var EA=Oe&&Oe.__createBinding||(Object.create?function(t,e,n,i){i===void 0&&(i=n);var s=Object.getOwnPropertyDescriptor(e,n);(!s||("get"in s?!e.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,i,s)}:function(t,e,n,i){i===void 0&&(i=n),t[i]=e[n]}),IA=Oe&&Oe.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&EA(e,t,n)};Object.defineProperty(Oe,"__esModule",{value:!0});Oe.createMessageConnection=Oe.createServerSocketTransport=Oe.createClientSocketTransport=Oe.createServerPipeTransport=Oe.createClientPipeTransport=Oe.generateRandomPipeName=Oe.StreamMessageWriter=Oe.StreamMessageReader=Oe.SocketMessageWriter=Oe.SocketMessageReader=Oe.PortMessageWriter=Oe.PortMessageReader=Oe.IPCMessageWriter=Oe.IPCMessageReader=void 0;var wo=B_();wo.default.install();var U_=require("path"),MA=require("os"),AA=require("crypto"),qc=require("net"),Rn=Ic();IA(Ic(),Oe);var fh=class extends Rn.AbstractMessageReader{constructor(e){super(),this.process=e;let n=this.process;n.on("error",i=>this.fireError(i)),n.on("close",()=>this.fireClose())}listen(e){return this.process.on("message",e),Rn.Disposable.create(()=>this.process.off("message",e))}};Oe.IPCMessageReader=fh;var hh=class extends Rn.AbstractMessageWriter{constructor(e){super(),this.process=e,this.errorCount=0;let n=this.process;n.on("error",i=>this.fireError(i)),n.on("close",()=>this.fireClose)}write(e){try{return typeof this.process.send=="function"&&this.process.send(e,void 0,void 0,n=>{n?(this.errorCount++,this.handleError(n,e)):this.errorCount=0}),Promise.resolve()}catch(n){return this.handleError(n,e),Promise.reject(n)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){}};Oe.IPCMessageWriter=hh;var gh=class extends Rn.AbstractMessageReader{constructor(e){super(),this.onData=new Rn.Emitter,e.on("close",()=>this.fireClose),e.on("error",n=>this.fireError(n)),e.on("message",n=>{this.onData.fire(n)})}listen(e){return this.onData.event(e)}};Oe.PortMessageReader=gh;var ph=class extends Rn.AbstractMessageWriter{constructor(e){super(),this.port=e,this.errorCount=0,e.on("close",()=>this.fireClose()),e.on("error",n=>this.fireError(n))}write(e){try{return this.port.postMessage(e),Promise.resolve()}catch(n){return this.handleError(n,e),Promise.reject(n)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){}};Oe.PortMessageWriter=ph;var Oi=class extends Rn.ReadableStreamMessageReader{constructor(e,n="utf-8"){super((0,wo.default)().stream.asReadableStream(e),n)}};Oe.SocketMessageReader=Oi;var Li=class extends Rn.WriteableStreamMessageWriter{constructor(e,n){super((0,wo.default)().stream.asWritableStream(e),n),this.socket=e}dispose(){super.dispose(),this.socket.destroy()}};Oe.SocketMessageWriter=Li;var Ac=class extends Rn.ReadableStreamMessageReader{constructor(e,n){super((0,wo.default)().stream.asReadableStream(e),n)}};Oe.StreamMessageReader=Ac;var Nc=class extends Rn.WriteableStreamMessageWriter{constructor(e,n){super((0,wo.default)().stream.asWritableStream(e),n)}};Oe.StreamMessageWriter=Nc;var $_=process.env.XDG_RUNTIME_DIR,NA=new Map([["linux",107],["darwin",103]]);function qA(){let t=(0,AA.randomBytes)(21).toString("hex");if(process.platform==="win32")return`\\\\.\\pipe\\vscode-jsonrpc-${t}-sock`;let e;$_?e=U_.join($_,`vscode-ipc-${t}.sock`):e=U_.join(MA.tmpdir(),`vscode-${t}.sock`);let n=NA.get(process.platform);return n!==void 0&&e.length>n&&(0,wo.default)().console.warn(`WARNING: IPC handle "${e}" is longer than ${n} characters.`),e}Oe.generateRandomPipeName=qA;function OA(t,e="utf-8"){let n,i=new Promise((s,c)=>{n=s});return new Promise((s,c)=>{let u=(0,qc.createServer)(d=>{u.close(),n([new Oi(d,e),new Li(d,e)])});u.on("error",c),u.listen(t,()=>{u.removeListener("error",c),s({onConnected:()=>i})})})}Oe.createClientPipeTransport=OA;function LA(t,e="utf-8"){let n=(0,qc.createConnection)(t);return[new Oi(n,e),new Li(n,e)]}Oe.createServerPipeTransport=LA;function BA(t,e="utf-8"){let n,i=new Promise((s,c)=>{n=s});return new Promise((s,c)=>{let u=(0,qc.createServer)(d=>{u.close(),n([new Oi(d,e),new Li(d,e)])});u.on("error",c),u.listen(t,"127.0.0.1",()=>{u.removeListener("error",c),s({onConnected:()=>i})})})}Oe.createClientSocketTransport=BA;function UA(t,e="utf-8"){let n=(0,qc.createConnection)(t,"127.0.0.1");return[new Oi(n,e),new Li(n,e)]}Oe.createServerSocketTransport=UA;function $A(t){let e=t;return e.read!==void 0&&e.addListener!==void 0}function WA(t){let e=t;return e.write!==void 0&&e.addListener!==void 0}function jA(t,e,n,i){n||(n=Rn.NullLogger);let s=$A(t)?new Ac(t):t,c=WA(e)?new Nc(e):e;return Rn.ConnectionStrategy.is(i)&&(i={connectionStrategy:i}),(0,Rn.createMessageConnection)(s,c,n,i)}Oe.createMessageConnection=jA});var mh=I((w2,W_)=>{"use strict";W_.exports=Bi()});var Lc=I((j_,Oc)=>{"use strict";(function(t){if(typeof Oc=="object"&&typeof Oc.exports=="object"){var e=t(require,j_);e!==void 0&&(Oc.exports=e)}else typeof define=="function"&&define.amd&&define(["require","exports"],t)})(function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TextDocument=e.EOL=e.WorkspaceFolder=e.InlineCompletionContext=e.SelectedCompletionInfo=e.InlineCompletionTriggerKind=e.InlineCompletionList=e.InlineCompletionItem=e.StringValue=e.InlayHint=e.InlayHintLabelPart=e.InlayHintKind=e.InlineValueContext=e.InlineValueEvaluatableExpression=e.InlineValueVariableLookup=e.InlineValueText=e.SemanticTokens=e.SemanticTokenModifiers=e.SemanticTokenTypes=e.SelectionRange=e.DocumentLink=e.FormattingOptions=e.CodeLens=e.CodeAction=e.CodeActionContext=e.CodeActionTriggerKind=e.CodeActionKind=e.DocumentSymbol=e.WorkspaceSymbol=e.SymbolInformation=e.SymbolTag=e.SymbolKind=e.DocumentHighlight=e.DocumentHighlightKind=e.SignatureInformation=e.ParameterInformation=e.Hover=e.MarkedString=e.CompletionList=e.CompletionItem=e.CompletionItemLabelDetails=e.InsertTextMode=e.InsertReplaceEdit=e.CompletionItemTag=e.InsertTextFormat=e.CompletionItemKind=e.MarkupContent=e.MarkupKind=e.TextDocumentItem=e.OptionalVersionedTextDocumentIdentifier=e.VersionedTextDocumentIdentifier=e.TextDocumentIdentifier=e.WorkspaceChange=e.WorkspaceEdit=e.DeleteFile=e.RenameFile=e.CreateFile=e.TextDocumentEdit=e.AnnotatedTextEdit=e.ChangeAnnotationIdentifier=e.ChangeAnnotation=e.TextEdit=e.Command=e.Diagnostic=e.CodeDescription=e.DiagnosticTag=e.DiagnosticSeverity=e.DiagnosticRelatedInformation=e.FoldingRange=e.FoldingRangeKind=e.ColorPresentation=e.ColorInformation=e.Color=e.LocationLink=e.Location=e.Range=e.Position=e.uinteger=e.integer=e.URI=e.DocumentUri=void 0;var n;(function(p){function k(R){return typeof R=="string"}p.is=k})(n||(e.DocumentUri=n={}));var i;(function(p){function k(R){return typeof R=="string"}p.is=k})(i||(e.URI=i={}));var s;(function(p){p.MIN_VALUE=-2147483648,p.MAX_VALUE=2147483647;function k(R){return typeof R=="number"&&p.MIN_VALUE<=R&&R<=p.MAX_VALUE}p.is=k})(s||(e.integer=s={}));var c;(function(p){p.MIN_VALUE=0,p.MAX_VALUE=2147483647;function k(R){return typeof R=="number"&&p.MIN_VALUE<=R&&R<=p.MAX_VALUE}p.is=k})(c||(e.uinteger=c={}));var u;(function(p){function k(v,g){return v===Number.MAX_VALUE&&(v=c.MAX_VALUE),g===Number.MAX_VALUE&&(g=c.MAX_VALUE),{line:v,character:g}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&q.uinteger(g.line)&&q.uinteger(g.character)}p.is=R})(u||(e.Position=u={}));var d;(function(p){function k(v,g,O,G){if(q.uinteger(v)&&q.uinteger(g)&&q.uinteger(O)&&q.uinteger(G))return{start:u.create(v,g),end:u.create(O,G)};if(u.is(v)&&u.is(g))return{start:v,end:g};throw new Error("Range#create called with invalid arguments[".concat(v,", ").concat(g,", ").concat(O,", ").concat(G,"]"))}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&u.is(g.start)&&u.is(g.end)}p.is=R})(d||(e.Range=d={}));var f;(function(p){function k(v,g){return{uri:v,range:g}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&d.is(g.range)&&(q.string(g.uri)||q.undefined(g.uri))}p.is=R})(f||(e.Location=f={}));var m;(function(p){function k(v,g,O,G){return{targetUri:v,targetRange:g,targetSelectionRange:O,originSelectionRange:G}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&d.is(g.targetRange)&&q.string(g.targetUri)&&d.is(g.targetSelectionRange)&&(d.is(g.originSelectionRange)||q.undefined(g.originSelectionRange))}p.is=R})(m||(e.LocationLink=m={}));var y;(function(p){function k(v,g,O,G){return{red:v,green:g,blue:O,alpha:G}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&q.numberRange(g.red,0,1)&&q.numberRange(g.green,0,1)&&q.numberRange(g.blue,0,1)&&q.numberRange(g.alpha,0,1)}p.is=R})(y||(e.Color=y={}));var x;(function(p){function k(v,g){return{range:v,color:g}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&d.is(g.range)&&y.is(g.color)}p.is=R})(x||(e.ColorInformation=x={}));var D;(function(p){function k(v,g,O){return{label:v,textEdit:g,additionalTextEdits:O}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&q.string(g.label)&&(q.undefined(g.textEdit)||te.is(g))&&(q.undefined(g.additionalTextEdits)||q.typedArray(g.additionalTextEdits,te.is))}p.is=R})(D||(e.ColorPresentation=D={}));var N;(function(p){p.Comment="comment",p.Imports="imports",p.Region="region"})(N||(e.FoldingRangeKind=N={}));var A;(function(p){function k(v,g,O,G,_e,ht){var Xe={startLine:v,endLine:g};return q.defined(O)&&(Xe.startCharacter=O),q.defined(G)&&(Xe.endCharacter=G),q.defined(_e)&&(Xe.kind=_e),q.defined(ht)&&(Xe.collapsedText=ht),Xe}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&q.uinteger(g.startLine)&&q.uinteger(g.startLine)&&(q.undefined(g.startCharacter)||q.uinteger(g.startCharacter))&&(q.undefined(g.endCharacter)||q.uinteger(g.endCharacter))&&(q.undefined(g.kind)||q.string(g.kind))}p.is=R})(A||(e.FoldingRange=A={}));var L;(function(p){function k(v,g){return{location:v,message:g}}p.create=k;function R(v){var g=v;return q.defined(g)&&f.is(g.location)&&q.string(g.message)}p.is=R})(L||(e.DiagnosticRelatedInformation=L={}));var B;(function(p){p.Error=1,p.Warning=2,p.Information=3,p.Hint=4})(B||(e.DiagnosticSeverity=B={}));var K;(function(p){p.Unnecessary=1,p.Deprecated=2})(K||(e.DiagnosticTag=K={}));var F;(function(p){function k(R){var v=R;return q.objectLiteral(v)&&q.string(v.href)}p.is=k})(F||(e.CodeDescription=F={}));var P;(function(p){function k(v,g,O,G,_e,ht){var Xe={range:v,message:g};return q.defined(O)&&(Xe.severity=O),q.defined(G)&&(Xe.code=G),q.defined(_e)&&(Xe.source=_e),q.defined(ht)&&(Xe.relatedInformation=ht),Xe}p.create=k;function R(v){var g,O=v;return q.defined(O)&&d.is(O.range)&&q.string(O.message)&&(q.number(O.severity)||q.undefined(O.severity))&&(q.integer(O.code)||q.string(O.code)||q.undefined(O.code))&&(q.undefined(O.codeDescription)||q.string((g=O.codeDescription)===null||g===void 0?void 0:g.href))&&(q.string(O.source)||q.undefined(O.source))&&(q.undefined(O.relatedInformation)||q.typedArray(O.relatedInformation,L.is))}p.is=R})(P||(e.Diagnostic=P={}));var U;(function(p){function k(v,g){for(var O=[],G=2;G<arguments.length;G++)O[G-2]=arguments[G];var _e={title:v,command:g};return q.defined(O)&&O.length>0&&(_e.arguments=O),_e}p.create=k;function R(v){var g=v;return q.defined(g)&&q.string(g.title)&&q.string(g.command)}p.is=R})(U||(e.Command=U={}));var te;(function(p){function k(O,G){return{range:O,newText:G}}p.replace=k;function R(O,G){return{range:{start:O,end:O},newText:G}}p.insert=R;function v(O){return{range:O,newText:""}}p.del=v;function g(O){var G=O;return q.objectLiteral(G)&&q.string(G.newText)&&d.is(G.range)}p.is=g})(te||(e.TextEdit=te={}));var ye;(function(p){function k(v,g,O){var G={label:v};return g!==void 0&&(G.needsConfirmation=g),O!==void 0&&(G.description=O),G}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&q.string(g.label)&&(q.boolean(g.needsConfirmation)||g.needsConfirmation===void 0)&&(q.string(g.description)||g.description===void 0)}p.is=R})(ye||(e.ChangeAnnotation=ye={}));var Be;(function(p){function k(R){var v=R;return q.string(v)}p.is=k})(Be||(e.ChangeAnnotationIdentifier=Be={}));var Je;(function(p){function k(O,G,_e){return{range:O,newText:G,annotationId:_e}}p.replace=k;function R(O,G,_e){return{range:{start:O,end:O},newText:G,annotationId:_e}}p.insert=R;function v(O,G){return{range:O,newText:"",annotationId:G}}p.del=v;function g(O){var G=O;return te.is(G)&&(ye.is(G.annotationId)||Be.is(G.annotationId))}p.is=g})(Je||(e.AnnotatedTextEdit=Je={}));var Dt;(function(p){function k(v,g){return{textDocument:v,edits:g}}p.create=k;function R(v){var g=v;return q.defined(g)&&We.is(g.textDocument)&&Array.isArray(g.edits)}p.is=R})(Dt||(e.TextDocumentEdit=Dt={}));var xe;(function(p){function k(v,g,O){var G={kind:"create",uri:v};return g!==void 0&&(g.overwrite!==void 0||g.ignoreIfExists!==void 0)&&(G.options=g),O!==void 0&&(G.annotationId=O),G}p.create=k;function R(v){var g=v;return g&&g.kind==="create"&&q.string(g.uri)&&(g.options===void 0||(g.options.overwrite===void 0||q.boolean(g.options.overwrite))&&(g.options.ignoreIfExists===void 0||q.boolean(g.options.ignoreIfExists)))&&(g.annotationId===void 0||Be.is(g.annotationId))}p.is=R})(xe||(e.CreateFile=xe={}));var X;(function(p){function k(v,g,O,G){var _e={kind:"rename",oldUri:v,newUri:g};return O!==void 0&&(O.overwrite!==void 0||O.ignoreIfExists!==void 0)&&(_e.options=O),G!==void 0&&(_e.annotationId=G),_e}p.create=k;function R(v){var g=v;return g&&g.kind==="rename"&&q.string(g.oldUri)&&q.string(g.newUri)&&(g.options===void 0||(g.options.overwrite===void 0||q.boolean(g.options.overwrite))&&(g.options.ignoreIfExists===void 0||q.boolean(g.options.ignoreIfExists)))&&(g.annotationId===void 0||Be.is(g.annotationId))}p.is=R})(X||(e.RenameFile=X={}));var z;(function(p){function k(v,g,O){var G={kind:"delete",uri:v};return g!==void 0&&(g.recursive!==void 0||g.ignoreIfNotExists!==void 0)&&(G.options=g),O!==void 0&&(G.annotationId=O),G}p.create=k;function R(v){var g=v;return g&&g.kind==="delete"&&q.string(g.uri)&&(g.options===void 0||(g.options.recursive===void 0||q.boolean(g.options.recursive))&&(g.options.ignoreIfNotExists===void 0||q.boolean(g.options.ignoreIfNotExists)))&&(g.annotationId===void 0||Be.is(g.annotationId))}p.is=R})(z||(e.DeleteFile=z={}));var se;(function(p){function k(R){var v=R;return v&&(v.changes!==void 0||v.documentChanges!==void 0)&&(v.documentChanges===void 0||v.documentChanges.every(function(g){return q.string(g.kind)?xe.is(g)||X.is(g)||z.is(g):Dt.is(g)}))}p.is=k})(se||(e.WorkspaceEdit=se={}));var re=function(){function p(k,R){this.edits=k,this.changeAnnotations=R}return p.prototype.insert=function(k,R,v){var g,O;if(v===void 0?g=te.insert(k,R):Be.is(v)?(O=v,g=Je.insert(k,R,v)):(this.assertChangeAnnotations(this.changeAnnotations),O=this.changeAnnotations.manage(v),g=Je.insert(k,R,O)),this.edits.push(g),O!==void 0)return O},p.prototype.replace=function(k,R,v){var g,O;if(v===void 0?g=te.replace(k,R):Be.is(v)?(O=v,g=Je.replace(k,R,v)):(this.assertChangeAnnotations(this.changeAnnotations),O=this.changeAnnotations.manage(v),g=Je.replace(k,R,O)),this.edits.push(g),O!==void 0)return O},p.prototype.delete=function(k,R){var v,g;if(R===void 0?v=te.del(k):Be.is(R)?(g=R,v=Je.del(k,R)):(this.assertChangeAnnotations(this.changeAnnotations),g=this.changeAnnotations.manage(R),v=Je.del(k,g)),this.edits.push(v),g!==void 0)return g},p.prototype.add=function(k){this.edits.push(k)},p.prototype.all=function(){return this.edits},p.prototype.clear=function(){this.edits.splice(0,this.edits.length)},p.prototype.assertChangeAnnotations=function(k){if(k===void 0)throw new Error("Text edit change is not configured to manage change annotations.")},p}(),De=function(){function p(k){this._annotations=k===void 0?Object.create(null):k,this._counter=0,this._size=0}return p.prototype.all=function(){return this._annotations},Object.defineProperty(p.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),p.prototype.manage=function(k,R){var v;if(Be.is(k)?v=k:(v=this.nextId(),R=k),this._annotations[v]!==void 0)throw new Error("Id ".concat(v," is already in use."));if(R===void 0)throw new Error("No annotation provided for id ".concat(v));return this._annotations[v]=R,this._size++,v},p.prototype.nextId=function(){return this._counter++,this._counter.toString()},p}(),ge=function(){function p(k){var R=this;this._textEditChanges=Object.create(null),k!==void 0?(this._workspaceEdit=k,k.documentChanges?(this._changeAnnotations=new De(k.changeAnnotations),k.changeAnnotations=this._changeAnnotations.all(),k.documentChanges.forEach(function(v){if(Dt.is(v)){var g=new re(v.edits,R._changeAnnotations);R._textEditChanges[v.textDocument.uri]=g}})):k.changes&&Object.keys(k.changes).forEach(function(v){var g=new re(k.changes[v]);R._textEditChanges[v]=g})):this._workspaceEdit={}}return Object.defineProperty(p.prototype,"edit",{get:function(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),p.prototype.getTextEditChange=function(k){if(We.is(k)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var R={uri:k.uri,version:k.version},v=this._textEditChanges[R.uri];if(!v){var g=[],O={textDocument:R,edits:g};this._workspaceEdit.documentChanges.push(O),v=new re(g,this._changeAnnotations),this._textEditChanges[R.uri]=v}return v}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");var v=this._textEditChanges[k];if(!v){var g=[];this._workspaceEdit.changes[k]=g,v=new re(g),this._textEditChanges[k]=v}return v}},p.prototype.initDocumentChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new De,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},p.prototype.initChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))},p.prototype.createFile=function(k,R,v){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var g;ye.is(R)||Be.is(R)?g=R:v=R;var O,G;if(g===void 0?O=xe.create(k,v):(G=Be.is(g)?g:this._changeAnnotations.manage(g),O=xe.create(k,v,G)),this._workspaceEdit.documentChanges.push(O),G!==void 0)return G},p.prototype.renameFile=function(k,R,v,g){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var O;ye.is(v)||Be.is(v)?O=v:g=v;var G,_e;if(O===void 0?G=X.create(k,R,g):(_e=Be.is(O)?O:this._changeAnnotations.manage(O),G=X.create(k,R,g,_e)),this._workspaceEdit.documentChanges.push(G),_e!==void 0)return _e},p.prototype.deleteFile=function(k,R,v){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var g;ye.is(R)||Be.is(R)?g=R:v=R;var O,G;if(g===void 0?O=z.create(k,v):(G=Be.is(g)?g:this._changeAnnotations.manage(g),O=z.create(k,v,G)),this._workspaceEdit.documentChanges.push(O),G!==void 0)return G},p}();e.WorkspaceChange=ge;var me;(function(p){function k(v){return{uri:v}}p.create=k;function R(v){var g=v;return q.defined(g)&&q.string(g.uri)}p.is=R})(me||(e.TextDocumentIdentifier=me={}));var St;(function(p){function k(v,g){return{uri:v,version:g}}p.create=k;function R(v){var g=v;return q.defined(g)&&q.string(g.uri)&&q.integer(g.version)}p.is=R})(St||(e.VersionedTextDocumentIdentifier=St={}));var We;(function(p){function k(v,g){return{uri:v,version:g}}p.create=k;function R(v){var g=v;return q.defined(g)&&q.string(g.uri)&&(g.version===null||q.integer(g.version))}p.is=R})(We||(e.OptionalVersionedTextDocumentIdentifier=We={}));var je;(function(p){function k(v,g,O,G){return{uri:v,languageId:g,version:O,text:G}}p.create=k;function R(v){var g=v;return q.defined(g)&&q.string(g.uri)&&q.string(g.languageId)&&q.integer(g.version)&&q.string(g.text)}p.is=R})(je||(e.TextDocumentItem=je={}));var bn;(function(p){p.PlainText="plaintext",p.Markdown="markdown";function k(R){var v=R;return v===p.PlainText||v===p.Markdown}p.is=k})(bn||(e.MarkupKind=bn={}));var yr;(function(p){function k(R){var v=R;return q.objectLiteral(R)&&bn.is(v.kind)&&q.string(v.value)}p.is=k})(yr||(e.MarkupContent=yr={}));var Vn;(function(p){p.Text=1,p.Method=2,p.Function=3,p.Constructor=4,p.Field=5,p.Variable=6,p.Class=7,p.Interface=8,p.Module=9,p.Property=10,p.Unit=11,p.Value=12,p.Enum=13,p.Keyword=14,p.Snippet=15,p.Color=16,p.File=17,p.Reference=18,p.Folder=19,p.EnumMember=20,p.Constant=21,p.Struct=22,p.Event=23,p.Operator=24,p.TypeParameter=25})(Vn||(e.CompletionItemKind=Vn={}));var ur;(function(p){p.PlainText=1,p.Snippet=2})(ur||(e.InsertTextFormat=ur={}));var xi;(function(p){p.Deprecated=1})(xi||(e.CompletionItemTag=xi={}));var lr;(function(p){function k(v,g,O){return{newText:v,insert:g,replace:O}}p.create=k;function R(v){var g=v;return g&&q.string(g.newText)&&d.is(g.insert)&&d.is(g.replace)}p.is=R})(lr||(e.InsertReplaceEdit=lr={}));var dr;(function(p){p.asIs=1,p.adjustIndentation=2})(dr||(e.InsertTextMode=dr={}));var Ki;(function(p){function k(R){var v=R;return v&&(q.string(v.detail)||v.detail===void 0)&&(q.string(v.description)||v.description===void 0)}p.is=k})(Ki||(e.CompletionItemLabelDetails=Ki={}));var wr;(function(p){function k(R){return{label:R}}p.create=k})(wr||(e.CompletionItem=wr={}));var Sr;(function(p){function k(R,v){return{items:R||[],isIncomplete:!!v}}p.create=k})(Sr||(e.CompletionList=Sr={}));var xr;(function(p){function k(v){return v.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}p.fromPlainText=k;function R(v){var g=v;return q.string(g)||q.objectLiteral(g)&&q.string(g.language)&&q.string(g.value)}p.is=R})(xr||(e.MarkedString=xr={}));var mt;(function(p){function k(R){var v=R;return!!v&&q.objectLiteral(v)&&(yr.is(v.contents)||xr.is(v.contents)||q.typedArray(v.contents,xr.is))&&(R.range===void 0||d.is(R.range))}p.is=k})(mt||(e.Hover=mt={}));var fr;(function(p){function k(R,v){return v?{label:R,documentation:v}:{label:R}}p.create=k})(fr||(e.ParameterInformation=fr={}));var Ji;(function(p){function k(R,v){for(var g=[],O=2;O<arguments.length;O++)g[O-2]=arguments[O];var G={label:R};return q.defined(v)&&(G.documentation=v),q.defined(g)?G.parameters=g:G.parameters=[],G}p.create=k})(Ji||(e.SignatureInformation=Ji={}));var Bt;(function(p){p.Text=1,p.Read=2,p.Write=3})(Bt||(e.DocumentHighlightKind=Bt={}));var Pi;(function(p){function k(R,v){var g={range:R};return q.number(v)&&(g.kind=v),g}p.create=k})(Pi||(e.DocumentHighlight=Pi={}));var zr;(function(p){p.File=1,p.Module=2,p.Namespace=3,p.Package=4,p.Class=5,p.Method=6,p.Property=7,p.Field=8,p.Constructor=9,p.Enum=10,p.Interface=11,p.Function=12,p.Variable=13,p.Constant=14,p.String=15,p.Number=16,p.Boolean=17,p.Array=18,p.Object=19,p.Key=20,p.Null=21,p.EnumMember=22,p.Struct=23,p.Event=24,p.Operator=25,p.TypeParameter=26})(zr||(e.SymbolKind=zr={}));var hr;(function(p){p.Deprecated=1})(hr||(e.SymbolTag=hr={}));var xt;(function(p){function k(R,v,g,O,G){var _e={name:R,kind:v,location:{uri:O,range:g}};return G&&(_e.containerName=G),_e}p.create=k})(xt||(e.SymbolInformation=xt={}));var sn;(function(p){function k(R,v,g,O){return O!==void 0?{name:R,kind:v,location:{uri:g,range:O}}:{name:R,kind:v,location:{uri:g}}}p.create=k})(sn||(e.WorkspaceSymbol=sn={}));var Pr;(function(p){function k(v,g,O,G,_e,ht){var Xe={name:v,detail:g,kind:O,range:G,selectionRange:_e};return ht!==void 0&&(Xe.children=ht),Xe}p.create=k;function R(v){var g=v;return g&&q.string(g.name)&&q.number(g.kind)&&d.is(g.range)&&d.is(g.selectionRange)&&(g.detail===void 0||q.string(g.detail))&&(g.deprecated===void 0||q.boolean(g.deprecated))&&(g.children===void 0||Array.isArray(g.children))&&(g.tags===void 0||Array.isArray(g.tags))}p.is=R})(Pr||(e.DocumentSymbol=Pr={}));var Xi;(function(p){p.Empty="",p.QuickFix="quickfix",p.Refactor="refactor",p.RefactorExtract="refactor.extract",p.RefactorInline="refactor.inline",p.RefactorRewrite="refactor.rewrite",p.Source="source",p.SourceOrganizeImports="source.organizeImports",p.SourceFixAll="source.fixAll"})(Xi||(e.CodeActionKind=Xi={}));var jt;(function(p){p.Invoked=1,p.Automatic=2})(jt||(e.CodeActionTriggerKind=jt={}));var Ci;(function(p){function k(v,g,O){var G={diagnostics:v};return g!=null&&(G.only=g),O!=null&&(G.triggerKind=O),G}p.create=k;function R(v){var g=v;return q.defined(g)&&q.typedArray(g.diagnostics,P.is)&&(g.only===void 0||q.typedArray(g.only,q.string))&&(g.triggerKind===void 0||g.triggerKind===jt.Invoked||g.triggerKind===jt.Automatic)}p.is=R})(Ci||(e.CodeActionContext=Ci={}));var Gn;(function(p){function k(v,g,O){var G={title:v},_e=!0;return typeof g=="string"?(_e=!1,G.kind=g):U.is(g)?G.command=g:G.edit=g,_e&&O!==void 0&&(G.kind=O),G}p.create=k;function R(v){var g=v;return g&&q.string(g.title)&&(g.diagnostics===void 0||q.typedArray(g.diagnostics,P.is))&&(g.kind===void 0||q.string(g.kind))&&(g.edit!==void 0||g.command!==void 0)&&(g.command===void 0||U.is(g.command))&&(g.isPreferred===void 0||q.boolean(g.isPreferred))&&(g.edit===void 0||se.is(g.edit))}p.is=R})(Gn||(e.CodeAction=Gn={}));var _n;(function(p){function k(v,g){var O={range:v};return q.defined(g)&&(O.data=g),O}p.create=k;function R(v){var g=v;return q.defined(g)&&d.is(g.range)&&(q.undefined(g.command)||U.is(g.command))}p.is=R})(_n||(e.CodeLens=_n={}));var Cr;(function(p){function k(v,g){return{tabSize:v,insertSpaces:g}}p.create=k;function R(v){var g=v;return q.defined(g)&&q.uinteger(g.tabSize)&&q.boolean(g.insertSpaces)}p.is=R})(Cr||(e.FormattingOptions=Cr={}));var vn;(function(p){function k(v,g,O){return{range:v,target:g,data:O}}p.create=k;function R(v){var g=v;return q.defined(g)&&d.is(g.range)&&(q.undefined(g.target)||q.string(g.target))}p.is=R})(vn||(e.DocumentLink=vn={}));var C;(function(p){function k(v,g){return{range:v,parent:g}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&d.is(g.range)&&(g.parent===void 0||p.is(g.parent))}p.is=R})(C||(e.SelectionRange=C={}));var H;(function(p){p.namespace="namespace",p.type="type",p.class="class",p.enum="enum",p.interface="interface",p.struct="struct",p.typeParameter="typeParameter",p.parameter="parameter",p.variable="variable",p.property="property",p.enumMember="enumMember",p.event="event",p.function="function",p.method="method",p.macro="macro",p.keyword="keyword",p.modifier="modifier",p.comment="comment",p.string="string",p.number="number",p.regexp="regexp",p.operator="operator",p.decorator="decorator"})(H||(e.SemanticTokenTypes=H={}));var Z;(function(p){p.declaration="declaration",p.definition="definition",p.readonly="readonly",p.static="static",p.deprecated="deprecated",p.abstract="abstract",p.async="async",p.modification="modification",p.documentation="documentation",p.defaultLibrary="defaultLibrary"})(Z||(e.SemanticTokenModifiers=Z={}));var ie;(function(p){function k(R){var v=R;return q.objectLiteral(v)&&(v.resultId===void 0||typeof v.resultId=="string")&&Array.isArray(v.data)&&(v.data.length===0||typeof v.data[0]=="number")}p.is=k})(ie||(e.SemanticTokens=ie={}));var Ue;(function(p){function k(v,g){return{range:v,text:g}}p.create=k;function R(v){var g=v;return g!=null&&d.is(g.range)&&q.string(g.text)}p.is=R})(Ue||(e.InlineValueText=Ue={}));var Le;(function(p){function k(v,g,O){return{range:v,variableName:g,caseSensitiveLookup:O}}p.create=k;function R(v){var g=v;return g!=null&&d.is(g.range)&&q.boolean(g.caseSensitiveLookup)&&(q.string(g.variableName)||g.variableName===void 0)}p.is=R})(Le||(e.InlineValueVariableLookup=Le={}));var Ge;(function(p){function k(v,g){return{range:v,expression:g}}p.create=k;function R(v){var g=v;return g!=null&&d.is(g.range)&&(q.string(g.expression)||g.expression===void 0)}p.is=R})(Ge||(e.InlineValueEvaluatableExpression=Ge={}));var tt;(function(p){function k(v,g){return{frameId:v,stoppedLocation:g}}p.create=k;function R(v){var g=v;return q.defined(g)&&d.is(v.stoppedLocation)}p.is=R})(tt||(e.InlineValueContext=tt={}));var Fe;(function(p){p.Type=1,p.Parameter=2;function k(R){return R===1||R===2}p.is=k})(Fe||(e.InlayHintKind=Fe={}));var Qe;(function(p){function k(v){return{value:v}}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&(g.tooltip===void 0||q.string(g.tooltip)||yr.is(g.tooltip))&&(g.location===void 0||f.is(g.location))&&(g.command===void 0||U.is(g.command))}p.is=R})(Qe||(e.InlayHintLabelPart=Qe={}));var Ne;(function(p){function k(v,g,O){var G={position:v,label:g};return O!==void 0&&(G.kind=O),G}p.create=k;function R(v){var g=v;return q.objectLiteral(g)&&u.is(g.position)&&(q.string(g.label)||q.typedArray(g.label,Qe.is))&&(g.kind===void 0||Fe.is(g.kind))&&g.textEdits===void 0||q.typedArray(g.textEdits,te.is)&&(g.tooltip===void 0||q.string(g.tooltip)||yr.is(g.tooltip))&&(g.paddingLeft===void 0||q.boolean(g.paddingLeft))&&(g.paddingRight===void 0||q.boolean(g.paddingRight))}p.is=R})(Ne||(e.InlayHint=Ne={}));var ct;(function(p){function k(R){return{kind:"snippet",value:R}}p.createSnippet=k})(ct||(e.StringValue=ct={}));var Ft;(function(p){function k(R,v,g,O){return{insertText:R,filterText:v,range:g,command:O}}p.create=k})(Ft||(e.InlineCompletionItem=Ft={}));var Zt;(function(p){function k(R){return{items:R}}p.create=k})(Zt||(e.InlineCompletionList=Zt={}));var Yi;(function(p){p.Invoked=0,p.Automatic=1})(Yi||(e.InlineCompletionTriggerKind=Yi={}));var Sa;(function(p){function k(R,v){return{range:R,text:v}}p.create=k})(Sa||(e.SelectedCompletionInfo=Sa={}));var ds;(function(p){function k(R,v){return{triggerKind:R,selectedCompletionInfo:v}}p.create=k})(ds||(e.InlineCompletionContext=ds={}));var xa;(function(p){function k(R){var v=R;return q.objectLiteral(v)&&i.is(v.uri)&&q.string(v.name)}p.is=k})(xa||(e.WorkspaceFolder=xa={})),e.EOL=[`
`,`\r
`,"\r"];var Pa;(function(p){function k(O,G,_e,ht){return new Vl(O,G,_e,ht)}p.create=k;function R(O){var G=O;return!!(q.defined(G)&&q.string(G.uri)&&(q.undefined(G.languageId)||q.string(G.languageId))&&q.uinteger(G.lineCount)&&q.func(G.getText)&&q.func(G.positionAt)&&q.func(G.offsetAt))}p.is=R;function v(O,G){for(var _e=O.getText(),ht=g(G,function(Hr,Zi){var Ca=Hr.range.start.line-Zi.range.start.line;return Ca===0?Hr.range.start.character-Zi.range.start.character:Ca}),Xe=_e.length,an=ht.length-1;an>=0;an--){var yn=ht[an],Qn=O.offsetAt(yn.range.start),Ie=O.offsetAt(yn.range.end);if(Ie<=Xe)_e=_e.substring(0,Qn)+yn.newText+_e.substring(Ie,_e.length);else throw new Error("Overlapping edit");Xe=Qn}return _e}p.applyEdits=v;function g(O,G){if(O.length<=1)return O;var _e=O.length/2|0,ht=O.slice(0,_e),Xe=O.slice(_e);g(ht,G),g(Xe,G);for(var an=0,yn=0,Qn=0;an<ht.length&&yn<Xe.length;){var Ie=G(ht[an],Xe[yn]);Ie<=0?O[Qn++]=ht[an++]:O[Qn++]=Xe[yn++]}for(;an<ht.length;)O[Qn++]=ht[an++];for(;yn<Xe.length;)O[Qn++]=Xe[yn++];return O}})(Pa||(e.TextDocument=Pa={}));var Vl=function(){function p(k,R,v,g){this._uri=k,this._languageId=R,this._version=v,this._content=g,this._lineOffsets=void 0}return Object.defineProperty(p.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(p.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),p.prototype.getText=function(k){if(k){var R=this.offsetAt(k.start),v=this.offsetAt(k.end);return this._content.substring(R,v)}return this._content},p.prototype.update=function(k,R){this._content=k.text,this._version=R,this._lineOffsets=void 0},p.prototype.getLineOffsets=function(){if(this._lineOffsets===void 0){for(var k=[],R=this._content,v=!0,g=0;g<R.length;g++){v&&(k.push(g),v=!1);var O=R.charAt(g);v=O==="\r"||O===`
`,O==="\r"&&g+1<R.length&&R.charAt(g+1)===`
`&&g++}v&&R.length>0&&k.push(R.length),this._lineOffsets=k}return this._lineOffsets},p.prototype.positionAt=function(k){k=Math.max(Math.min(k,this._content.length),0);var R=this.getLineOffsets(),v=0,g=R.length;if(g===0)return u.create(0,k);for(;v<g;){var O=Math.floor((v+g)/2);R[O]>k?g=O:v=O+1}var G=v-1;return u.create(G,k-R[G])},p.prototype.offsetAt=function(k){var R=this.getLineOffsets();if(k.line>=R.length)return this._content.length;if(k.line<0)return 0;var v=R[k.line],g=k.line+1<R.length?R[k.line+1]:this._content.length;return Math.max(Math.min(v+k.character,g),v)},Object.defineProperty(p.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),p}(),q;(function(p){var k=Object.prototype.toString;function R(Ie){return typeof Ie<"u"}p.defined=R;function v(Ie){return typeof Ie>"u"}p.undefined=v;function g(Ie){return Ie===!0||Ie===!1}p.boolean=g;function O(Ie){return k.call(Ie)==="[object String]"}p.string=O;function G(Ie){return k.call(Ie)==="[object Number]"}p.number=G;function _e(Ie,Hr,Zi){return k.call(Ie)==="[object Number]"&&Hr<=Ie&&Ie<=Zi}p.numberRange=_e;function ht(Ie){return k.call(Ie)==="[object Number]"&&-2147483648<=Ie&&Ie<=2147483647}p.integer=ht;function Xe(Ie){return k.call(Ie)==="[object Number]"&&0<=Ie&&Ie<=2147483647}p.uinteger=Xe;function an(Ie){return k.call(Ie)==="[object Function]"}p.func=an;function yn(Ie){return Ie!==null&&typeof Ie=="object"}p.objectLiteral=yn;function Qn(Ie,Hr){return Array.isArray(Ie)&&Ie.every(Hr)}p.typedArray=Qn})(q||(q={}))})});var pt=I(hn=>{"use strict";Object.defineProperty(hn,"__esModule",{value:!0});hn.ProtocolNotificationType=hn.ProtocolNotificationType0=hn.ProtocolRequestType=hn.ProtocolRequestType0=hn.RegistrationType=hn.MessageDirection=void 0;var So=Bi(),z_;(function(t){t.clientToServer="clientToServer",t.serverToClient="serverToClient",t.both="both"})(z_||(hn.MessageDirection=z_={}));var bh=class{constructor(e){this.method=e}};hn.RegistrationType=bh;var _h=class extends So.RequestType0{constructor(e){super(e)}};hn.ProtocolRequestType0=_h;var vh=class extends So.RequestType{constructor(e){super(e,So.ParameterStructures.byName)}};hn.ProtocolRequestType=vh;var yh=class extends So.NotificationType0{constructor(e){super(e)}};hn.ProtocolNotificationType0=yh;var wh=class extends So.NotificationType{constructor(e){super(e,So.ParameterStructures.byName)}};hn.ProtocolNotificationType=wh});var Bc=I(It=>{"use strict";Object.defineProperty(It,"__esModule",{value:!0});It.objectLiteral=It.typedArray=It.stringArray=It.array=It.func=It.error=It.number=It.string=It.boolean=void 0;function zA(t){return t===!0||t===!1}It.boolean=zA;function H_(t){return typeof t=="string"||t instanceof String}It.string=H_;function HA(t){return typeof t=="number"||t instanceof Number}It.number=HA;function VA(t){return t instanceof Error}It.error=VA;function GA(t){return typeof t=="function"}It.func=GA;function V_(t){return Array.isArray(t)}It.array=V_;function QA(t){return V_(t)&&t.every(e=>H_(e))}It.stringArray=QA;function KA(t,e){return Array.isArray(t)&&t.every(e)}It.typedArray=KA;function JA(t){return t!==null&&typeof t=="object"}It.objectLiteral=JA});var K_=I(Uc=>{"use strict";Object.defineProperty(Uc,"__esModule",{value:!0});Uc.ImplementationRequest=void 0;var G_=pt(),Q_;(function(t){t.method="textDocument/implementation",t.messageDirection=G_.MessageDirection.clientToServer,t.type=new G_.ProtocolRequestType(t.method)})(Q_||(Uc.ImplementationRequest=Q_={}))});var Y_=I($c=>{"use strict";Object.defineProperty($c,"__esModule",{value:!0});$c.TypeDefinitionRequest=void 0;var J_=pt(),X_;(function(t){t.method="textDocument/typeDefinition",t.messageDirection=J_.MessageDirection.clientToServer,t.type=new J_.ProtocolRequestType(t.method)})(X_||($c.TypeDefinitionRequest=X_={}))});var tv=I(xo=>{"use strict";Object.defineProperty(xo,"__esModule",{value:!0});xo.DidChangeWorkspaceFoldersNotification=xo.WorkspaceFoldersRequest=void 0;var Wc=pt(),Z_;(function(t){t.method="workspace/workspaceFolders",t.messageDirection=Wc.MessageDirection.serverToClient,t.type=new Wc.ProtocolRequestType0(t.method)})(Z_||(xo.WorkspaceFoldersRequest=Z_={}));var ev;(function(t){t.method="workspace/didChangeWorkspaceFolders",t.messageDirection=Wc.MessageDirection.clientToServer,t.type=new Wc.ProtocolNotificationType(t.method)})(ev||(xo.DidChangeWorkspaceFoldersNotification=ev={}))});var iv=I(jc=>{"use strict";Object.defineProperty(jc,"__esModule",{value:!0});jc.ConfigurationRequest=void 0;var nv=pt(),rv;(function(t){t.method="workspace/configuration",t.messageDirection=nv.MessageDirection.serverToClient,t.type=new nv.ProtocolRequestType(t.method)})(rv||(jc.ConfigurationRequest=rv={}))});var av=I(Po=>{"use strict";Object.defineProperty(Po,"__esModule",{value:!0});Po.ColorPresentationRequest=Po.DocumentColorRequest=void 0;var zc=pt(),ov;(function(t){t.method="textDocument/documentColor",t.messageDirection=zc.MessageDirection.clientToServer,t.type=new zc.ProtocolRequestType(t.method)})(ov||(Po.DocumentColorRequest=ov={}));var sv;(function(t){t.method="textDocument/colorPresentation",t.messageDirection=zc.MessageDirection.clientToServer,t.type=new zc.ProtocolRequestType(t.method)})(sv||(Po.ColorPresentationRequest=sv={}))});var lv=I(Co=>{"use strict";Object.defineProperty(Co,"__esModule",{value:!0});Co.FoldingRangeRefreshRequest=Co.FoldingRangeRequest=void 0;var Hc=pt(),cv;(function(t){t.method="textDocument/foldingRange",t.messageDirection=Hc.MessageDirection.clientToServer,t.type=new Hc.ProtocolRequestType(t.method)})(cv||(Co.FoldingRangeRequest=cv={}));var uv;(function(t){t.method="workspace/foldingRange/refresh",t.messageDirection=Hc.MessageDirection.serverToClient,t.type=new Hc.ProtocolRequestType0(t.method)})(uv||(Co.FoldingRangeRefreshRequest=uv={}))});var hv=I(Vc=>{"use strict";Object.defineProperty(Vc,"__esModule",{value:!0});Vc.DeclarationRequest=void 0;var dv=pt(),fv;(function(t){t.method="textDocument/declaration",t.messageDirection=dv.MessageDirection.clientToServer,t.type=new dv.ProtocolRequestType(t.method)})(fv||(Vc.DeclarationRequest=fv={}))});var mv=I(Gc=>{"use strict";Object.defineProperty(Gc,"__esModule",{value:!0});Gc.SelectionRangeRequest=void 0;var gv=pt(),pv;(function(t){t.method="textDocument/selectionRange",t.messageDirection=gv.MessageDirection.clientToServer,t.type=new gv.ProtocolRequestType(t.method)})(pv||(Gc.SelectionRangeRequest=pv={}))});var yv=I(si=>{"use strict";Object.defineProperty(si,"__esModule",{value:!0});si.WorkDoneProgressCancelNotification=si.WorkDoneProgressCreateRequest=si.WorkDoneProgress=void 0;var XA=Bi(),Qc=pt(),bv;(function(t){t.type=new XA.ProgressType;function e(n){return n===t.type}t.is=e})(bv||(si.WorkDoneProgress=bv={}));var _v;(function(t){t.method="window/workDoneProgress/create",t.messageDirection=Qc.MessageDirection.serverToClient,t.type=new Qc.ProtocolRequestType(t.method)})(_v||(si.WorkDoneProgressCreateRequest=_v={}));var vv;(function(t){t.method="window/workDoneProgress/cancel",t.messageDirection=Qc.MessageDirection.clientToServer,t.type=new Qc.ProtocolNotificationType(t.method)})(vv||(si.WorkDoneProgressCancelNotification=vv={}))});var Pv=I(ai=>{"use strict";Object.defineProperty(ai,"__esModule",{value:!0});ai.CallHierarchyOutgoingCallsRequest=ai.CallHierarchyIncomingCallsRequest=ai.CallHierarchyPrepareRequest=void 0;var ko=pt(),wv;(function(t){t.method="textDocument/prepareCallHierarchy",t.messageDirection=ko.MessageDirection.clientToServer,t.type=new ko.ProtocolRequestType(t.method)})(wv||(ai.CallHierarchyPrepareRequest=wv={}));var Sv;(function(t){t.method="callHierarchy/incomingCalls",t.messageDirection=ko.MessageDirection.clientToServer,t.type=new ko.ProtocolRequestType(t.method)})(Sv||(ai.CallHierarchyIncomingCallsRequest=Sv={}));var xv;(function(t){t.method="callHierarchy/outgoingCalls",t.messageDirection=ko.MessageDirection.clientToServer,t.type=new ko.ProtocolRequestType(t.method)})(xv||(ai.CallHierarchyOutgoingCallsRequest=xv={}))});var Fv=I(gn=>{"use strict";Object.defineProperty(gn,"__esModule",{value:!0});gn.SemanticTokensRefreshRequest=gn.SemanticTokensRangeRequest=gn.SemanticTokensDeltaRequest=gn.SemanticTokensRequest=gn.SemanticTokensRegistrationType=gn.TokenFormat=void 0;var qr=pt(),Cv;(function(t){t.Relative="relative"})(Cv||(gn.TokenFormat=Cv={}));var Ms;(function(t){t.method="textDocument/semanticTokens",t.type=new qr.RegistrationType(t.method)})(Ms||(gn.SemanticTokensRegistrationType=Ms={}));var kv;(function(t){t.method="textDocument/semanticTokens/full",t.messageDirection=qr.MessageDirection.clientToServer,t.type=new qr.ProtocolRequestType(t.method),t.registrationMethod=Ms.method})(kv||(gn.SemanticTokensRequest=kv={}));var Tv;(function(t){t.method="textDocument/semanticTokens/full/delta",t.messageDirection=qr.MessageDirection.clientToServer,t.type=new qr.ProtocolRequestType(t.method),t.registrationMethod=Ms.method})(Tv||(gn.SemanticTokensDeltaRequest=Tv={}));var Rv;(function(t){t.method="textDocument/semanticTokens/range",t.messageDirection=qr.MessageDirection.clientToServer,t.type=new qr.ProtocolRequestType(t.method),t.registrationMethod=Ms.method})(Rv||(gn.SemanticTokensRangeRequest=Rv={}));var Dv;(function(t){t.method="workspace/semanticTokens/refresh",t.messageDirection=qr.MessageDirection.serverToClient,t.type=new qr.ProtocolRequestType0(t.method)})(Dv||(gn.SemanticTokensRefreshRequest=Dv={}))});var Mv=I(Kc=>{"use strict";Object.defineProperty(Kc,"__esModule",{value:!0});Kc.ShowDocumentRequest=void 0;var Ev=pt(),Iv;(function(t){t.method="window/showDocument",t.messageDirection=Ev.MessageDirection.serverToClient,t.type=new Ev.ProtocolRequestType(t.method)})(Iv||(Kc.ShowDocumentRequest=Iv={}))});var qv=I(Jc=>{"use strict";Object.defineProperty(Jc,"__esModule",{value:!0});Jc.LinkedEditingRangeRequest=void 0;var Av=pt(),Nv;(function(t){t.method="textDocument/linkedEditingRange",t.messageDirection=Av.MessageDirection.clientToServer,t.type=new Av.ProtocolRequestType(t.method)})(Nv||(Jc.LinkedEditingRangeRequest=Nv={}))});var zv=I(Qt=>{"use strict";Object.defineProperty(Qt,"__esModule",{value:!0});Qt.WillDeleteFilesRequest=Qt.DidDeleteFilesNotification=Qt.DidRenameFilesNotification=Qt.WillRenameFilesRequest=Qt.DidCreateFilesNotification=Qt.WillCreateFilesRequest=Qt.FileOperationPatternKind=void 0;var Bn=pt(),Ov;(function(t){t.file="file",t.folder="folder"})(Ov||(Qt.FileOperationPatternKind=Ov={}));var Lv;(function(t){t.method="workspace/willCreateFiles",t.messageDirection=Bn.MessageDirection.clientToServer,t.type=new Bn.ProtocolRequestType(t.method)})(Lv||(Qt.WillCreateFilesRequest=Lv={}));var Bv;(function(t){t.method="workspace/didCreateFiles",t.messageDirection=Bn.MessageDirection.clientToServer,t.type=new Bn.ProtocolNotificationType(t.method)})(Bv||(Qt.DidCreateFilesNotification=Bv={}));var Uv;(function(t){t.method="workspace/willRenameFiles",t.messageDirection=Bn.MessageDirection.clientToServer,t.type=new Bn.ProtocolRequestType(t.method)})(Uv||(Qt.WillRenameFilesRequest=Uv={}));var $v;(function(t){t.method="workspace/didRenameFiles",t.messageDirection=Bn.MessageDirection.clientToServer,t.type=new Bn.ProtocolNotificationType(t.method)})($v||(Qt.DidRenameFilesNotification=$v={}));var Wv;(function(t){t.method="workspace/didDeleteFiles",t.messageDirection=Bn.MessageDirection.clientToServer,t.type=new Bn.ProtocolNotificationType(t.method)})(Wv||(Qt.DidDeleteFilesNotification=Wv={}));var jv;(function(t){t.method="workspace/willDeleteFiles",t.messageDirection=Bn.MessageDirection.clientToServer,t.type=new Bn.ProtocolRequestType(t.method)})(jv||(Qt.WillDeleteFilesRequest=jv={}))});var Kv=I(ci=>{"use strict";Object.defineProperty(ci,"__esModule",{value:!0});ci.MonikerRequest=ci.MonikerKind=ci.UniquenessLevel=void 0;var Hv=pt(),Vv;(function(t){t.document="document",t.project="project",t.group="group",t.scheme="scheme",t.global="global"})(Vv||(ci.UniquenessLevel=Vv={}));var Gv;(function(t){t.$import="import",t.$export="export",t.local="local"})(Gv||(ci.MonikerKind=Gv={}));var Qv;(function(t){t.method="textDocument/moniker",t.messageDirection=Hv.MessageDirection.clientToServer,t.type=new Hv.ProtocolRequestType(t.method)})(Qv||(ci.MonikerRequest=Qv={}))});var Zv=I(ui=>{"use strict";Object.defineProperty(ui,"__esModule",{value:!0});ui.TypeHierarchySubtypesRequest=ui.TypeHierarchySupertypesRequest=ui.TypeHierarchyPrepareRequest=void 0;var To=pt(),Jv;(function(t){t.method="textDocument/prepareTypeHierarchy",t.messageDirection=To.MessageDirection.clientToServer,t.type=new To.ProtocolRequestType(t.method)})(Jv||(ui.TypeHierarchyPrepareRequest=Jv={}));var Xv;(function(t){t.method="typeHierarchy/supertypes",t.messageDirection=To.MessageDirection.clientToServer,t.type=new To.ProtocolRequestType(t.method)})(Xv||(ui.TypeHierarchySupertypesRequest=Xv={}));var Yv;(function(t){t.method="typeHierarchy/subtypes",t.messageDirection=To.MessageDirection.clientToServer,t.type=new To.ProtocolRequestType(t.method)})(Yv||(ui.TypeHierarchySubtypesRequest=Yv={}))});var ny=I(Ro=>{"use strict";Object.defineProperty(Ro,"__esModule",{value:!0});Ro.InlineValueRefreshRequest=Ro.InlineValueRequest=void 0;var Xc=pt(),ey;(function(t){t.method="textDocument/inlineValue",t.messageDirection=Xc.MessageDirection.clientToServer,t.type=new Xc.ProtocolRequestType(t.method)})(ey||(Ro.InlineValueRequest=ey={}));var ty;(function(t){t.method="workspace/inlineValue/refresh",t.messageDirection=Xc.MessageDirection.serverToClient,t.type=new Xc.ProtocolRequestType0(t.method)})(ty||(Ro.InlineValueRefreshRequest=ty={}))});var sy=I(li=>{"use strict";Object.defineProperty(li,"__esModule",{value:!0});li.InlayHintRefreshRequest=li.InlayHintResolveRequest=li.InlayHintRequest=void 0;var Do=pt(),ry;(function(t){t.method="textDocument/inlayHint",t.messageDirection=Do.MessageDirection.clientToServer,t.type=new Do.ProtocolRequestType(t.method)})(ry||(li.InlayHintRequest=ry={}));var iy;(function(t){t.method="inlayHint/resolve",t.messageDirection=Do.MessageDirection.clientToServer,t.type=new Do.ProtocolRequestType(t.method)})(iy||(li.InlayHintResolveRequest=iy={}));var oy;(function(t){t.method="workspace/inlayHint/refresh",t.messageDirection=Do.MessageDirection.serverToClient,t.type=new Do.ProtocolRequestType0(t.method)})(oy||(li.InlayHintRefreshRequest=oy={}))});var hy=I(Un=>{"use strict";Object.defineProperty(Un,"__esModule",{value:!0});Un.DiagnosticRefreshRequest=Un.WorkspaceDiagnosticRequest=Un.DocumentDiagnosticRequest=Un.DocumentDiagnosticReportKind=Un.DiagnosticServerCancellationData=void 0;var fy=Bi(),YA=Bc(),Fo=pt(),ay;(function(t){function e(n){let i=n;return i&&YA.boolean(i.retriggerRequest)}t.is=e})(ay||(Un.DiagnosticServerCancellationData=ay={}));var cy;(function(t){t.Full="full",t.Unchanged="unchanged"})(cy||(Un.DocumentDiagnosticReportKind=cy={}));var uy;(function(t){t.method="textDocument/diagnostic",t.messageDirection=Fo.MessageDirection.clientToServer,t.type=new Fo.ProtocolRequestType(t.method),t.partialResult=new fy.ProgressType})(uy||(Un.DocumentDiagnosticRequest=uy={}));var ly;(function(t){t.method="workspace/diagnostic",t.messageDirection=Fo.MessageDirection.clientToServer,t.type=new Fo.ProtocolRequestType(t.method),t.partialResult=new fy.ProgressType})(ly||(Un.WorkspaceDiagnosticRequest=ly={}));var dy;(function(t){t.method="workspace/diagnostic/refresh",t.messageDirection=Fo.MessageDirection.serverToClient,t.type=new Fo.ProtocolRequestType0(t.method)})(dy||(Un.DiagnosticRefreshRequest=dy={}))});var yy=I(wt=>{"use strict";Object.defineProperty(wt,"__esModule",{value:!0});wt.DidCloseNotebookDocumentNotification=wt.DidSaveNotebookDocumentNotification=wt.DidChangeNotebookDocumentNotification=wt.NotebookCellArrayChange=wt.DidOpenNotebookDocumentNotification=wt.NotebookDocumentSyncRegistrationType=wt.NotebookDocument=wt.NotebookCell=wt.ExecutionSummary=wt.NotebookCellKind=void 0;var As=Lc(),er=Bc(),br=pt(),Sh;(function(t){t.Markup=1,t.Code=2;function e(n){return n===1||n===2}t.is=e})(Sh||(wt.NotebookCellKind=Sh={}));var xh;(function(t){function e(s,c){let u={executionOrder:s};return(c===!0||c===!1)&&(u.success=c),u}t.create=e;function n(s){let c=s;return er.objectLiteral(c)&&As.uinteger.is(c.executionOrder)&&(c.success===void 0||er.boolean(c.success))}t.is=n;function i(s,c){return s===c?!0:s==null||c===null||c===void 0?!1:s.executionOrder===c.executionOrder&&s.success===c.success}t.equals=i})(xh||(wt.ExecutionSummary=xh={}));var Yc;(function(t){function e(c,u){return{kind:c,document:u}}t.create=e;function n(c){let u=c;return er.objectLiteral(u)&&Sh.is(u.kind)&&As.DocumentUri.is(u.document)&&(u.metadata===void 0||er.objectLiteral(u.metadata))}t.is=n;function i(c,u){let d=new Set;return c.document!==u.document&&d.add("document"),c.kind!==u.kind&&d.add("kind"),c.executionSummary!==u.executionSummary&&d.add("executionSummary"),(c.metadata!==void 0||u.metadata!==void 0)&&!s(c.metadata,u.metadata)&&d.add("metadata"),(c.executionSummary!==void 0||u.executionSummary!==void 0)&&!xh.equals(c.executionSummary,u.executionSummary)&&d.add("executionSummary"),d}t.diff=i;function s(c,u){if(c===u)return!0;if(c==null||u===null||u===void 0||typeof c!=typeof u||typeof c!="object")return!1;let d=Array.isArray(c),f=Array.isArray(u);if(d!==f)return!1;if(d&&f){if(c.length!==u.length)return!1;for(let m=0;m<c.length;m++)if(!s(c[m],u[m]))return!1}if(er.objectLiteral(c)&&er.objectLiteral(u)){let m=Object.keys(c),y=Object.keys(u);if(m.length!==y.length||(m.sort(),y.sort(),!s(m,y)))return!1;for(let x=0;x<m.length;x++){let D=m[x];if(!s(c[D],u[D]))return!1}}return!0}})(Yc||(wt.NotebookCell=Yc={}));var gy;(function(t){function e(i,s,c,u){return{uri:i,notebookType:s,version:c,cells:u}}t.create=e;function n(i){let s=i;return er.objectLiteral(s)&&er.string(s.uri)&&As.integer.is(s.version)&&er.typedArray(s.cells,Yc.is)}t.is=n})(gy||(wt.NotebookDocument=gy={}));var Eo;(function(t){t.method="notebookDocument/sync",t.messageDirection=br.MessageDirection.clientToServer,t.type=new br.RegistrationType(t.method)})(Eo||(wt.NotebookDocumentSyncRegistrationType=Eo={}));var py;(function(t){t.method="notebookDocument/didOpen",t.messageDirection=br.MessageDirection.clientToServer,t.type=new br.ProtocolNotificationType(t.method),t.registrationMethod=Eo.method})(py||(wt.DidOpenNotebookDocumentNotification=py={}));var my;(function(t){function e(i){let s=i;return er.objectLiteral(s)&&As.uinteger.is(s.start)&&As.uinteger.is(s.deleteCount)&&(s.cells===void 0||er.typedArray(s.cells,Yc.is))}t.is=e;function n(i,s,c){let u={start:i,deleteCount:s};return c!==void 0&&(u.cells=c),u}t.create=n})(my||(wt.NotebookCellArrayChange=my={}));var by;(function(t){t.method="notebookDocument/didChange",t.messageDirection=br.MessageDirection.clientToServer,t.type=new br.ProtocolNotificationType(t.method),t.registrationMethod=Eo.method})(by||(wt.DidChangeNotebookDocumentNotification=by={}));var _y;(function(t){t.method="notebookDocument/didSave",t.messageDirection=br.MessageDirection.clientToServer,t.type=new br.ProtocolNotificationType(t.method),t.registrationMethod=Eo.method})(_y||(wt.DidSaveNotebookDocumentNotification=_y={}));var vy;(function(t){t.method="notebookDocument/didClose",t.messageDirection=br.MessageDirection.clientToServer,t.type=new br.ProtocolNotificationType(t.method),t.registrationMethod=Eo.method})(vy||(wt.DidCloseNotebookDocumentNotification=vy={}))});var xy=I(Zc=>{"use strict";Object.defineProperty(Zc,"__esModule",{value:!0});Zc.InlineCompletionRequest=void 0;var wy=pt(),Sy;(function(t){t.method="textDocument/inlineCompletion",t.messageDirection=wy.MessageDirection.clientToServer,t.type=new wy.ProtocolRequestType(t.method)})(Sy||(Zc.InlineCompletionRequest=Sy={}))});var qw=I(w=>{"use strict";Object.defineProperty(w,"__esModule",{value:!0});w.WorkspaceSymbolRequest=w.CodeActionResolveRequest=w.CodeActionRequest=w.DocumentSymbolRequest=w.DocumentHighlightRequest=w.ReferencesRequest=w.DefinitionRequest=w.SignatureHelpRequest=w.SignatureHelpTriggerKind=w.HoverRequest=w.CompletionResolveRequest=w.CompletionRequest=w.CompletionTriggerKind=w.PublishDiagnosticsNotification=w.WatchKind=w.RelativePattern=w.FileChangeType=w.DidChangeWatchedFilesNotification=w.WillSaveTextDocumentWaitUntilRequest=w.WillSaveTextDocumentNotification=w.TextDocumentSaveReason=w.DidSaveTextDocumentNotification=w.DidCloseTextDocumentNotification=w.DidChangeTextDocumentNotification=w.TextDocumentContentChangeEvent=w.DidOpenTextDocumentNotification=w.TextDocumentSyncKind=w.TelemetryEventNotification=w.LogMessageNotification=w.ShowMessageRequest=w.ShowMessageNotification=w.MessageType=w.DidChangeConfigurationNotification=w.ExitNotification=w.ShutdownRequest=w.InitializedNotification=w.InitializeErrorCodes=w.InitializeRequest=w.WorkDoneProgressOptions=w.TextDocumentRegistrationOptions=w.StaticRegistrationOptions=w.PositionEncodingKind=w.FailureHandlingKind=w.ResourceOperationKind=w.UnregistrationRequest=w.RegistrationRequest=w.DocumentSelector=w.NotebookCellTextDocumentFilter=w.NotebookDocumentFilter=w.TextDocumentFilter=void 0;w.MonikerRequest=w.MonikerKind=w.UniquenessLevel=w.WillDeleteFilesRequest=w.DidDeleteFilesNotification=w.WillRenameFilesRequest=w.DidRenameFilesNotification=w.WillCreateFilesRequest=w.DidCreateFilesNotification=w.FileOperationPatternKind=w.LinkedEditingRangeRequest=w.ShowDocumentRequest=w.SemanticTokensRegistrationType=w.SemanticTokensRefreshRequest=w.SemanticTokensRangeRequest=w.SemanticTokensDeltaRequest=w.SemanticTokensRequest=w.TokenFormat=w.CallHierarchyPrepareRequest=w.CallHierarchyOutgoingCallsRequest=w.CallHierarchyIncomingCallsRequest=w.WorkDoneProgressCancelNotification=w.WorkDoneProgressCreateRequest=w.WorkDoneProgress=w.SelectionRangeRequest=w.DeclarationRequest=w.FoldingRangeRefreshRequest=w.FoldingRangeRequest=w.ColorPresentationRequest=w.DocumentColorRequest=w.ConfigurationRequest=w.DidChangeWorkspaceFoldersNotification=w.WorkspaceFoldersRequest=w.TypeDefinitionRequest=w.ImplementationRequest=w.ApplyWorkspaceEditRequest=w.ExecuteCommandRequest=w.PrepareRenameRequest=w.RenameRequest=w.PrepareSupportDefaultBehavior=w.DocumentOnTypeFormattingRequest=w.DocumentRangesFormattingRequest=w.DocumentRangeFormattingRequest=w.DocumentFormattingRequest=w.DocumentLinkResolveRequest=w.DocumentLinkRequest=w.CodeLensRefreshRequest=w.CodeLensResolveRequest=w.CodeLensRequest=w.WorkspaceSymbolResolveRequest=void 0;w.InlineCompletionRequest=w.DidCloseNotebookDocumentNotification=w.DidSaveNotebookDocumentNotification=w.DidChangeNotebookDocumentNotification=w.NotebookCellArrayChange=w.DidOpenNotebookDocumentNotification=w.NotebookDocumentSyncRegistrationType=w.NotebookDocument=w.NotebookCell=w.ExecutionSummary=w.NotebookCellKind=w.DiagnosticRefreshRequest=w.WorkspaceDiagnosticRequest=w.DocumentDiagnosticRequest=w.DocumentDiagnosticReportKind=w.DiagnosticServerCancellationData=w.InlayHintRefreshRequest=w.InlayHintResolveRequest=w.InlayHintRequest=w.InlineValueRefreshRequest=w.InlineValueRequest=w.TypeHierarchySupertypesRequest=w.TypeHierarchySubtypesRequest=w.TypeHierarchyPrepareRequest=void 0;var Y=pt(),Py=Lc(),Lt=Bc(),ZA=K_();Object.defineProperty(w,"ImplementationRequest",{enumerable:!0,get:function(){return ZA.ImplementationRequest}});var eN=Y_();Object.defineProperty(w,"TypeDefinitionRequest",{enumerable:!0,get:function(){return eN.TypeDefinitionRequest}});var Iw=tv();Object.defineProperty(w,"WorkspaceFoldersRequest",{enumerable:!0,get:function(){return Iw.WorkspaceFoldersRequest}});Object.defineProperty(w,"DidChangeWorkspaceFoldersNotification",{enumerable:!0,get:function(){return Iw.DidChangeWorkspaceFoldersNotification}});var tN=iv();Object.defineProperty(w,"ConfigurationRequest",{enumerable:!0,get:function(){return tN.ConfigurationRequest}});var Mw=av();Object.defineProperty(w,"DocumentColorRequest",{enumerable:!0,get:function(){return Mw.DocumentColorRequest}});Object.defineProperty(w,"ColorPresentationRequest",{enumerable:!0,get:function(){return Mw.ColorPresentationRequest}});var Aw=lv();Object.defineProperty(w,"FoldingRangeRequest",{enumerable:!0,get:function(){return Aw.FoldingRangeRequest}});Object.defineProperty(w,"FoldingRangeRefreshRequest",{enumerable:!0,get:function(){return Aw.FoldingRangeRefreshRequest}});var nN=hv();Object.defineProperty(w,"DeclarationRequest",{enumerable:!0,get:function(){return nN.DeclarationRequest}});var rN=mv();Object.defineProperty(w,"SelectionRangeRequest",{enumerable:!0,get:function(){return rN.SelectionRangeRequest}});var Rh=yv();Object.defineProperty(w,"WorkDoneProgress",{enumerable:!0,get:function(){return Rh.WorkDoneProgress}});Object.defineProperty(w,"WorkDoneProgressCreateRequest",{enumerable:!0,get:function(){return Rh.WorkDoneProgressCreateRequest}});Object.defineProperty(w,"WorkDoneProgressCancelNotification",{enumerable:!0,get:function(){return Rh.WorkDoneProgressCancelNotification}});var Dh=Pv();Object.defineProperty(w,"CallHierarchyIncomingCallsRequest",{enumerable:!0,get:function(){return Dh.CallHierarchyIncomingCallsRequest}});Object.defineProperty(w,"CallHierarchyOutgoingCallsRequest",{enumerable:!0,get:function(){return Dh.CallHierarchyOutgoingCallsRequest}});Object.defineProperty(w,"CallHierarchyPrepareRequest",{enumerable:!0,get:function(){return Dh.CallHierarchyPrepareRequest}});var Io=Fv();Object.defineProperty(w,"TokenFormat",{enumerable:!0,get:function(){return Io.TokenFormat}});Object.defineProperty(w,"SemanticTokensRequest",{enumerable:!0,get:function(){return Io.SemanticTokensRequest}});Object.defineProperty(w,"SemanticTokensDeltaRequest",{enumerable:!0,get:function(){return Io.SemanticTokensDeltaRequest}});Object.defineProperty(w,"SemanticTokensRangeRequest",{enumerable:!0,get:function(){return Io.SemanticTokensRangeRequest}});Object.defineProperty(w,"SemanticTokensRefreshRequest",{enumerable:!0,get:function(){return Io.SemanticTokensRefreshRequest}});Object.defineProperty(w,"SemanticTokensRegistrationType",{enumerable:!0,get:function(){return Io.SemanticTokensRegistrationType}});var iN=Mv();Object.defineProperty(w,"ShowDocumentRequest",{enumerable:!0,get:function(){return iN.ShowDocumentRequest}});var oN=qv();Object.defineProperty(w,"LinkedEditingRangeRequest",{enumerable:!0,get:function(){return oN.LinkedEditingRangeRequest}});var Ui=zv();Object.defineProperty(w,"FileOperationPatternKind",{enumerable:!0,get:function(){return Ui.FileOperationPatternKind}});Object.defineProperty(w,"DidCreateFilesNotification",{enumerable:!0,get:function(){return Ui.DidCreateFilesNotification}});Object.defineProperty(w,"WillCreateFilesRequest",{enumerable:!0,get:function(){return Ui.WillCreateFilesRequest}});Object.defineProperty(w,"DidRenameFilesNotification",{enumerable:!0,get:function(){return Ui.DidRenameFilesNotification}});Object.defineProperty(w,"WillRenameFilesRequest",{enumerable:!0,get:function(){return Ui.WillRenameFilesRequest}});Object.defineProperty(w,"DidDeleteFilesNotification",{enumerable:!0,get:function(){return Ui.DidDeleteFilesNotification}});Object.defineProperty(w,"WillDeleteFilesRequest",{enumerable:!0,get:function(){return Ui.WillDeleteFilesRequest}});var Fh=Kv();Object.defineProperty(w,"UniquenessLevel",{enumerable:!0,get:function(){return Fh.UniquenessLevel}});Object.defineProperty(w,"MonikerKind",{enumerable:!0,get:function(){return Fh.MonikerKind}});Object.defineProperty(w,"MonikerRequest",{enumerable:!0,get:function(){return Fh.MonikerRequest}});var Eh=Zv();Object.defineProperty(w,"TypeHierarchyPrepareRequest",{enumerable:!0,get:function(){return Eh.TypeHierarchyPrepareRequest}});Object.defineProperty(w,"TypeHierarchySubtypesRequest",{enumerable:!0,get:function(){return Eh.TypeHierarchySubtypesRequest}});Object.defineProperty(w,"TypeHierarchySupertypesRequest",{enumerable:!0,get:function(){return Eh.TypeHierarchySupertypesRequest}});var Nw=ny();Object.defineProperty(w,"InlineValueRequest",{enumerable:!0,get:function(){return Nw.InlineValueRequest}});Object.defineProperty(w,"InlineValueRefreshRequest",{enumerable:!0,get:function(){return Nw.InlineValueRefreshRequest}});var Ih=sy();Object.defineProperty(w,"InlayHintRequest",{enumerable:!0,get:function(){return Ih.InlayHintRequest}});Object.defineProperty(w,"InlayHintResolveRequest",{enumerable:!0,get:function(){return Ih.InlayHintResolveRequest}});Object.defineProperty(w,"InlayHintRefreshRequest",{enumerable:!0,get:function(){return Ih.InlayHintRefreshRequest}});var Ns=hy();Object.defineProperty(w,"DiagnosticServerCancellationData",{enumerable:!0,get:function(){return Ns.DiagnosticServerCancellationData}});Object.defineProperty(w,"DocumentDiagnosticReportKind",{enumerable:!0,get:function(){return Ns.DocumentDiagnosticReportKind}});Object.defineProperty(w,"DocumentDiagnosticRequest",{enumerable:!0,get:function(){return Ns.DocumentDiagnosticRequest}});Object.defineProperty(w,"WorkspaceDiagnosticRequest",{enumerable:!0,get:function(){return Ns.WorkspaceDiagnosticRequest}});Object.defineProperty(w,"DiagnosticRefreshRequest",{enumerable:!0,get:function(){return Ns.DiagnosticRefreshRequest}});var _r=yy();Object.defineProperty(w,"NotebookCellKind",{enumerable:!0,get:function(){return _r.NotebookCellKind}});Object.defineProperty(w,"ExecutionSummary",{enumerable:!0,get:function(){return _r.ExecutionSummary}});Object.defineProperty(w,"NotebookCell",{enumerable:!0,get:function(){return _r.NotebookCell}});Object.defineProperty(w,"NotebookDocument",{enumerable:!0,get:function(){return _r.NotebookDocument}});Object.defineProperty(w,"NotebookDocumentSyncRegistrationType",{enumerable:!0,get:function(){return _r.NotebookDocumentSyncRegistrationType}});Object.defineProperty(w,"DidOpenNotebookDocumentNotification",{enumerable:!0,get:function(){return _r.DidOpenNotebookDocumentNotification}});Object.defineProperty(w,"NotebookCellArrayChange",{enumerable:!0,get:function(){return _r.NotebookCellArrayChange}});Object.defineProperty(w,"DidChangeNotebookDocumentNotification",{enumerable:!0,get:function(){return _r.DidChangeNotebookDocumentNotification}});Object.defineProperty(w,"DidSaveNotebookDocumentNotification",{enumerable:!0,get:function(){return _r.DidSaveNotebookDocumentNotification}});Object.defineProperty(w,"DidCloseNotebookDocumentNotification",{enumerable:!0,get:function(){return _r.DidCloseNotebookDocumentNotification}});var sN=xy();Object.defineProperty(w,"InlineCompletionRequest",{enumerable:!0,get:function(){return sN.InlineCompletionRequest}});var Ph;(function(t){function e(n){let i=n;return Lt.string(i)||Lt.string(i.language)||Lt.string(i.scheme)||Lt.string(i.pattern)}t.is=e})(Ph||(w.TextDocumentFilter=Ph={}));var Ch;(function(t){function e(n){let i=n;return Lt.objectLiteral(i)&&(Lt.string(i.notebookType)||Lt.string(i.scheme)||Lt.string(i.pattern))}t.is=e})(Ch||(w.NotebookDocumentFilter=Ch={}));var kh;(function(t){function e(n){let i=n;return Lt.objectLiteral(i)&&(Lt.string(i.notebook)||Ch.is(i.notebook))&&(i.language===void 0||Lt.string(i.language))}t.is=e})(kh||(w.NotebookCellTextDocumentFilter=kh={}));var Th;(function(t){function e(n){if(!Array.isArray(n))return!1;for(let i of n)if(!Lt.string(i)&&!Ph.is(i)&&!kh.is(i))return!1;return!0}t.is=e})(Th||(w.DocumentSelector=Th={}));var Cy;(function(t){t.method="client/registerCapability",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolRequestType(t.method)})(Cy||(w.RegistrationRequest=Cy={}));var ky;(function(t){t.method="client/unregisterCapability",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolRequestType(t.method)})(ky||(w.UnregistrationRequest=ky={}));var Ty;(function(t){t.Create="create",t.Rename="rename",t.Delete="delete"})(Ty||(w.ResourceOperationKind=Ty={}));var Ry;(function(t){t.Abort="abort",t.Transactional="transactional",t.TextOnlyTransactional="textOnlyTransactional",t.Undo="undo"})(Ry||(w.FailureHandlingKind=Ry={}));var Dy;(function(t){t.UTF8="utf-8",t.UTF16="utf-16",t.UTF32="utf-32"})(Dy||(w.PositionEncodingKind=Dy={}));var Fy;(function(t){function e(n){let i=n;return i&&Lt.string(i.id)&&i.id.length>0}t.hasId=e})(Fy||(w.StaticRegistrationOptions=Fy={}));var Ey;(function(t){function e(n){let i=n;return i&&(i.documentSelector===null||Th.is(i.documentSelector))}t.is=e})(Ey||(w.TextDocumentRegistrationOptions=Ey={}));var Iy;(function(t){function e(i){let s=i;return Lt.objectLiteral(s)&&(s.workDoneProgress===void 0||Lt.boolean(s.workDoneProgress))}t.is=e;function n(i){let s=i;return s&&Lt.boolean(s.workDoneProgress)}t.hasWorkDoneProgress=n})(Iy||(w.WorkDoneProgressOptions=Iy={}));var My;(function(t){t.method="initialize",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(My||(w.InitializeRequest=My={}));var Ay;(function(t){t.unknownProtocolVersion=1})(Ay||(w.InitializeErrorCodes=Ay={}));var Ny;(function(t){t.method="initialized",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Ny||(w.InitializedNotification=Ny={}));var qy;(function(t){t.method="shutdown",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType0(t.method)})(qy||(w.ShutdownRequest=qy={}));var Oy;(function(t){t.method="exit",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType0(t.method)})(Oy||(w.ExitNotification=Oy={}));var Ly;(function(t){t.method="workspace/didChangeConfiguration",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Ly||(w.DidChangeConfigurationNotification=Ly={}));var By;(function(t){t.Error=1,t.Warning=2,t.Info=3,t.Log=4,t.Debug=5})(By||(w.MessageType=By={}));var Uy;(function(t){t.method="window/showMessage",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolNotificationType(t.method)})(Uy||(w.ShowMessageNotification=Uy={}));var $y;(function(t){t.method="window/showMessageRequest",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolRequestType(t.method)})($y||(w.ShowMessageRequest=$y={}));var Wy;(function(t){t.method="window/logMessage",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolNotificationType(t.method)})(Wy||(w.LogMessageNotification=Wy={}));var jy;(function(t){t.method="telemetry/event",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolNotificationType(t.method)})(jy||(w.TelemetryEventNotification=jy={}));var zy;(function(t){t.None=0,t.Full=1,t.Incremental=2})(zy||(w.TextDocumentSyncKind=zy={}));var Hy;(function(t){t.method="textDocument/didOpen",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Hy||(w.DidOpenTextDocumentNotification=Hy={}));var Vy;(function(t){function e(i){let s=i;return s!=null&&typeof s.text=="string"&&s.range!==void 0&&(s.rangeLength===void 0||typeof s.rangeLength=="number")}t.isIncremental=e;function n(i){let s=i;return s!=null&&typeof s.text=="string"&&s.range===void 0&&s.rangeLength===void 0}t.isFull=n})(Vy||(w.TextDocumentContentChangeEvent=Vy={}));var Gy;(function(t){t.method="textDocument/didChange",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Gy||(w.DidChangeTextDocumentNotification=Gy={}));var Qy;(function(t){t.method="textDocument/didClose",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Qy||(w.DidCloseTextDocumentNotification=Qy={}));var Ky;(function(t){t.method="textDocument/didSave",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Ky||(w.DidSaveTextDocumentNotification=Ky={}));var Jy;(function(t){t.Manual=1,t.AfterDelay=2,t.FocusOut=3})(Jy||(w.TextDocumentSaveReason=Jy={}));var Xy;(function(t){t.method="textDocument/willSave",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Xy||(w.WillSaveTextDocumentNotification=Xy={}));var Yy;(function(t){t.method="textDocument/willSaveWaitUntil",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Yy||(w.WillSaveTextDocumentWaitUntilRequest=Yy={}));var Zy;(function(t){t.method="workspace/didChangeWatchedFiles",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolNotificationType(t.method)})(Zy||(w.DidChangeWatchedFilesNotification=Zy={}));var ew;(function(t){t.Created=1,t.Changed=2,t.Deleted=3})(ew||(w.FileChangeType=ew={}));var tw;(function(t){function e(n){let i=n;return Lt.objectLiteral(i)&&(Py.URI.is(i.baseUri)||Py.WorkspaceFolder.is(i.baseUri))&&Lt.string(i.pattern)}t.is=e})(tw||(w.RelativePattern=tw={}));var nw;(function(t){t.Create=1,t.Change=2,t.Delete=4})(nw||(w.WatchKind=nw={}));var rw;(function(t){t.method="textDocument/publishDiagnostics",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolNotificationType(t.method)})(rw||(w.PublishDiagnosticsNotification=rw={}));var iw;(function(t){t.Invoked=1,t.TriggerCharacter=2,t.TriggerForIncompleteCompletions=3})(iw||(w.CompletionTriggerKind=iw={}));var ow;(function(t){t.method="textDocument/completion",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(ow||(w.CompletionRequest=ow={}));var sw;(function(t){t.method="completionItem/resolve",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(sw||(w.CompletionResolveRequest=sw={}));var aw;(function(t){t.method="textDocument/hover",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(aw||(w.HoverRequest=aw={}));var cw;(function(t){t.Invoked=1,t.TriggerCharacter=2,t.ContentChange=3})(cw||(w.SignatureHelpTriggerKind=cw={}));var uw;(function(t){t.method="textDocument/signatureHelp",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(uw||(w.SignatureHelpRequest=uw={}));var lw;(function(t){t.method="textDocument/definition",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(lw||(w.DefinitionRequest=lw={}));var dw;(function(t){t.method="textDocument/references",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(dw||(w.ReferencesRequest=dw={}));var fw;(function(t){t.method="textDocument/documentHighlight",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(fw||(w.DocumentHighlightRequest=fw={}));var hw;(function(t){t.method="textDocument/documentSymbol",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(hw||(w.DocumentSymbolRequest=hw={}));var gw;(function(t){t.method="textDocument/codeAction",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(gw||(w.CodeActionRequest=gw={}));var pw;(function(t){t.method="codeAction/resolve",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(pw||(w.CodeActionResolveRequest=pw={}));var mw;(function(t){t.method="workspace/symbol",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(mw||(w.WorkspaceSymbolRequest=mw={}));var bw;(function(t){t.method="workspaceSymbol/resolve",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(bw||(w.WorkspaceSymbolResolveRequest=bw={}));var _w;(function(t){t.method="textDocument/codeLens",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(_w||(w.CodeLensRequest=_w={}));var vw;(function(t){t.method="codeLens/resolve",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(vw||(w.CodeLensResolveRequest=vw={}));var yw;(function(t){t.method="workspace/codeLens/refresh",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolRequestType0(t.method)})(yw||(w.CodeLensRefreshRequest=yw={}));var ww;(function(t){t.method="textDocument/documentLink",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(ww||(w.DocumentLinkRequest=ww={}));var Sw;(function(t){t.method="documentLink/resolve",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Sw||(w.DocumentLinkResolveRequest=Sw={}));var xw;(function(t){t.method="textDocument/formatting",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(xw||(w.DocumentFormattingRequest=xw={}));var Pw;(function(t){t.method="textDocument/rangeFormatting",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Pw||(w.DocumentRangeFormattingRequest=Pw={}));var Cw;(function(t){t.method="textDocument/rangesFormatting",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Cw||(w.DocumentRangesFormattingRequest=Cw={}));var kw;(function(t){t.method="textDocument/onTypeFormatting",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(kw||(w.DocumentOnTypeFormattingRequest=kw={}));var Tw;(function(t){t.Identifier=1})(Tw||(w.PrepareSupportDefaultBehavior=Tw={}));var Rw;(function(t){t.method="textDocument/rename",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Rw||(w.RenameRequest=Rw={}));var Dw;(function(t){t.method="textDocument/prepareRename",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Dw||(w.PrepareRenameRequest=Dw={}));var Fw;(function(t){t.method="workspace/executeCommand",t.messageDirection=Y.MessageDirection.clientToServer,t.type=new Y.ProtocolRequestType(t.method)})(Fw||(w.ExecuteCommandRequest=Fw={}));var Ew;(function(t){t.method="workspace/applyEdit",t.messageDirection=Y.MessageDirection.serverToClient,t.type=new Y.ProtocolRequestType("workspace/applyEdit")})(Ew||(w.ApplyWorkspaceEditRequest=Ew={}))});var Lw=I(eu=>{"use strict";Object.defineProperty(eu,"__esModule",{value:!0});eu.createProtocolConnection=void 0;var Ow=Bi();function aN(t,e,n,i){return Ow.ConnectionStrategy.is(i)&&(i={connectionStrategy:i}),(0,Ow.createMessageConnection)(t,e,n,i)}eu.createProtocolConnection=aN});var Uw=I(pn=>{"use strict";var cN=pn&&pn.__createBinding||(Object.create?function(t,e,n,i){i===void 0&&(i=n);var s=Object.getOwnPropertyDescriptor(e,n);(!s||("get"in s?!e.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,i,s)}:function(t,e,n,i){i===void 0&&(i=n),t[i]=e[n]}),tu=pn&&pn.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&cN(e,t,n)};Object.defineProperty(pn,"__esModule",{value:!0});pn.LSPErrorCodes=pn.createProtocolConnection=void 0;tu(Bi(),pn);tu(Lc(),pn);tu(pt(),pn);tu(qw(),pn);var uN=Lw();Object.defineProperty(pn,"createProtocolConnection",{enumerable:!0,get:function(){return uN.createProtocolConnection}});var Bw;(function(t){t.lspReservedErrorRangeStart=-32899,t.RequestFailed=-32803,t.ServerCancelled=-32802,t.ContentModified=-32801,t.RequestCancelled=-32800,t.lspReservedErrorRangeEnd=-32800})(Bw||(pn.LSPErrorCodes=Bw={}))});var kt=I(vr=>{"use strict";var lN=vr&&vr.__createBinding||(Object.create?function(t,e,n,i){i===void 0&&(i=n);var s=Object.getOwnPropertyDescriptor(e,n);(!s||("get"in s?!e.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,i,s)}:function(t,e,n,i){i===void 0&&(i=n),t[i]=e[n]}),$w=vr&&vr.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&lN(e,t,n)};Object.defineProperty(vr,"__esModule",{value:!0});vr.createProtocolConnection=void 0;var dN=mh();$w(mh(),vr);$w(Uw(),vr);function fN(t,e,n,i){return(0,dN.createMessageConnection)(t,e,n,i)}vr.createProtocolConnection=fN});var Mh=I($n=>{"use strict";Object.defineProperty($n,"__esModule",{value:!0});$n.generateUuid=$n.parse=$n.isUUID=$n.v4=$n.empty=void 0;var qs=class{constructor(e){this._value=e}asHex(){return this._value}equals(e){return this.asHex()===e.asHex()}},Os=class t extends qs{static _oneOf(e){return e[Math.floor(e.length*Math.random())]}static _randomHex(){return t._oneOf(t._chars)}constructor(){super([t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),"-",t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),"-","4",t._randomHex(),t._randomHex(),t._randomHex(),"-",t._oneOf(t._timeHighBits),t._randomHex(),t._randomHex(),t._randomHex(),"-",t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex(),t._randomHex()].join(""))}};Os._chars=["0","1","2","3","4","5","6","6","7","8","9","a","b","c","d","e","f"];Os._timeHighBits=["8","9","a","b"];$n.empty=new qs("00000000-0000-0000-0000-000000000000");function Ww(){return new Os}$n.v4=Ww;var hN=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function jw(t){return hN.test(t)}$n.isUUID=jw;function gN(t){if(!jw(t))throw new Error("invalid uuid");return new qs(t)}$n.parse=gN;function pN(){return Ww().asHex()}$n.generateUuid=pN});var zw=I(fi=>{"use strict";Object.defineProperty(fi,"__esModule",{value:!0});fi.attachPartialResult=fi.ProgressFeature=fi.attachWorkDone=void 0;var di=kt(),mN=Mh(),$i=class t{constructor(e,n){this._connection=e,this._token=n,t.Instances.set(this._token,this)}begin(e,n,i,s){let c={kind:"begin",title:e,percentage:n,message:i,cancellable:s};this._connection.sendProgress(di.WorkDoneProgress.type,this._token,c)}report(e,n){let i={kind:"report"};typeof e=="number"?(i.percentage=e,n!==void 0&&(i.message=n)):i.message=e,this._connection.sendProgress(di.WorkDoneProgress.type,this._token,i)}done(){t.Instances.delete(this._token),this._connection.sendProgress(di.WorkDoneProgress.type,this._token,{kind:"end"})}};$i.Instances=new Map;var nu=class extends $i{constructor(e,n){super(e,n),this._source=new di.CancellationTokenSource}get token(){return this._source.token}done(){this._source.dispose(),super.done()}cancel(){this._source.cancel()}},Ls=class{constructor(){}begin(){}report(){}done(){}},ru=class extends Ls{constructor(){super(),this._source=new di.CancellationTokenSource}get token(){return this._source.token}done(){this._source.dispose()}cancel(){this._source.cancel()}};function bN(t,e){if(e===void 0||e.workDoneToken===void 0)return new Ls;let n=e.workDoneToken;return delete e.workDoneToken,new $i(t,n)}fi.attachWorkDone=bN;var _N=t=>class extends t{constructor(){super(),this._progressSupported=!1}initialize(e){super.initialize(e),e?.window?.workDoneProgress===!0&&(this._progressSupported=!0,this.connection.onNotification(di.WorkDoneProgressCancelNotification.type,n=>{let i=$i.Instances.get(n.token);(i instanceof nu||i instanceof ru)&&i.cancel()}))}attachWorkDoneProgress(e){return e===void 0?new Ls:new $i(this.connection,e)}createWorkDoneProgress(){if(this._progressSupported){let e=(0,mN.generateUuid)();return this.connection.sendRequest(di.WorkDoneProgressCreateRequest.type,{token:e}).then(()=>new nu(this.connection,e))}else return Promise.resolve(new ru)}};fi.ProgressFeature=_N;var Ah;(function(t){t.type=new di.ProgressType})(Ah||(Ah={}));var Nh=class{constructor(e,n){this._connection=e,this._token=n}report(e){this._connection.sendProgress(Ah.type,this._token,e)}};function vN(t,e){if(e===void 0||e.partialResultToken===void 0)return;let n=e.partialResultToken;return delete e.partialResultToken,new Nh(t,n)}fi.attachPartialResult=vN});var Hw=I(iu=>{"use strict";Object.defineProperty(iu,"__esModule",{value:!0});iu.ConfigurationFeature=void 0;var yN=kt(),wN=pc(),SN=t=>class extends t{getConfiguration(e){return e?wN.string(e)?this._getConfiguration({section:e}):this._getConfiguration(e):this._getConfiguration({})}_getConfiguration(e){let n={items:Array.isArray(e)?e:[e]};return this.connection.sendRequest(yN.ConfigurationRequest.type,n).then(i=>Array.isArray(i)?Array.isArray(e)?i:i[0]:Array.isArray(e)?[]:null)}};iu.ConfigurationFeature=SN});var Vw=I(su=>{"use strict";Object.defineProperty(su,"__esModule",{value:!0});su.WorkspaceFoldersFeature=void 0;var ou=kt(),xN=t=>class extends t{constructor(){super(),this._notificationIsAutoRegistered=!1}initialize(e){super.initialize(e);let n=e.workspace;n&&n.workspaceFolders&&(this._onDidChangeWorkspaceFolders=new ou.Emitter,this.connection.onNotification(ou.DidChangeWorkspaceFoldersNotification.type,i=>{this._onDidChangeWorkspaceFolders.fire(i.event)}))}fillServerCapabilities(e){super.fillServerCapabilities(e);let n=e.workspace?.workspaceFolders?.changeNotifications;this._notificationIsAutoRegistered=n===!0||typeof n=="string"}getWorkspaceFolders(){return this.connection.sendRequest(ou.WorkspaceFoldersRequest.type)}get onDidChangeWorkspaceFolders(){if(!this._onDidChangeWorkspaceFolders)throw new Error("Client doesn't support sending workspace folder change events.");return!this._notificationIsAutoRegistered&&!this._unregistration&&(this._unregistration=this.connection.client.register(ou.DidChangeWorkspaceFoldersNotification.type)),this._onDidChangeWorkspaceFolders.event}};su.WorkspaceFoldersFeature=xN});var Gw=I(au=>{"use strict";Object.defineProperty(au,"__esModule",{value:!0});au.CallHierarchyFeature=void 0;var qh=kt(),PN=t=>class extends t{get callHierarchy(){return{onPrepare:e=>this.connection.onRequest(qh.CallHierarchyPrepareRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n),void 0)),onIncomingCalls:e=>{let n=qh.CallHierarchyIncomingCallsRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))},onOutgoingCalls:e=>{let n=qh.CallHierarchyOutgoingCallsRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))}}}};au.CallHierarchyFeature=PN});var Lh=I(hi=>{"use strict";Object.defineProperty(hi,"__esModule",{value:!0});hi.SemanticTokensBuilder=hi.SemanticTokensDiff=hi.SemanticTokensFeature=void 0;var cu=kt(),CN=t=>class extends t{get semanticTokens(){return{refresh:()=>this.connection.sendRequest(cu.SemanticTokensRefreshRequest.type),on:e=>{let n=cu.SemanticTokensRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))},onDelta:e=>{let n=cu.SemanticTokensDeltaRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))},onRange:e=>{let n=cu.SemanticTokensRangeRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))}}}};hi.SemanticTokensFeature=CN;var uu=class{constructor(e,n){this.originalSequence=e,this.modifiedSequence=n}computeDiff(){let e=this.originalSequence.length,n=this.modifiedSequence.length,i=0;for(;i<n&&i<e&&this.originalSequence[i]===this.modifiedSequence[i];)i++;if(i<n&&i<e){let s=e-1,c=n-1;for(;s>=i&&c>=i&&this.originalSequence[s]===this.modifiedSequence[c];)s--,c--;(s<i||c<i)&&(s++,c++);let u=s-i+1,d=this.modifiedSequence.slice(i,c+1);return d.length===1&&d[0]===this.originalSequence[s]?[{start:i,deleteCount:u-1}]:[{start:i,deleteCount:u,data:d}]}else return i<n?[{start:i,deleteCount:0,data:this.modifiedSequence.slice(i)}]:i<e?[{start:i,deleteCount:e-i}]:[]}};hi.SemanticTokensDiff=uu;var Oh=class{constructor(){this._prevData=void 0,this.initialize()}initialize(){this._id=Date.now(),this._prevLine=0,this._prevChar=0,this._data=[],this._dataLen=0}push(e,n,i,s,c){let u=e,d=n;this._dataLen>0&&(u-=this._prevLine,u===0&&(d-=this._prevChar)),this._data[this._dataLen++]=u,this._data[this._dataLen++]=d,this._data[this._dataLen++]=i,this._data[this._dataLen++]=s,this._data[this._dataLen++]=c,this._prevLine=e,this._prevChar=n}get id(){return this._id.toString()}previousResult(e){this.id===e&&(this._prevData=this._data),this.initialize()}build(){return this._prevData=void 0,{resultId:this.id,data:this._data}}canBuildEdits(){return this._prevData!==void 0}buildEdits(){return this._prevData!==void 0?{resultId:this.id,edits:new uu(this._prevData,this._data).computeDiff()}:this.build()}};hi.SemanticTokensBuilder=Oh});var Qw=I(lu=>{"use strict";Object.defineProperty(lu,"__esModule",{value:!0});lu.ShowDocumentFeature=void 0;var kN=kt(),TN=t=>class extends t{showDocument(e){return this.connection.sendRequest(kN.ShowDocumentRequest.type,e)}};lu.ShowDocumentFeature=TN});var Kw=I(du=>{"use strict";Object.defineProperty(du,"__esModule",{value:!0});du.FileOperationsFeature=void 0;var Mo=kt(),RN=t=>class extends t{onDidCreateFiles(e){return this.connection.onNotification(Mo.DidCreateFilesNotification.type,n=>{e(n)})}onDidRenameFiles(e){return this.connection.onNotification(Mo.DidRenameFilesNotification.type,n=>{e(n)})}onDidDeleteFiles(e){return this.connection.onNotification(Mo.DidDeleteFilesNotification.type,n=>{e(n)})}onWillCreateFiles(e){return this.connection.onRequest(Mo.WillCreateFilesRequest.type,(n,i)=>e(n,i))}onWillRenameFiles(e){return this.connection.onRequest(Mo.WillRenameFilesRequest.type,(n,i)=>e(n,i))}onWillDeleteFiles(e){return this.connection.onRequest(Mo.WillDeleteFilesRequest.type,(n,i)=>e(n,i))}};du.FileOperationsFeature=RN});var Jw=I(fu=>{"use strict";Object.defineProperty(fu,"__esModule",{value:!0});fu.LinkedEditingRangeFeature=void 0;var DN=kt(),FN=t=>class extends t{onLinkedEditingRange(e){return this.connection.onRequest(DN.LinkedEditingRangeRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n),void 0))}};fu.LinkedEditingRangeFeature=FN});var Xw=I(hu=>{"use strict";Object.defineProperty(hu,"__esModule",{value:!0});hu.TypeHierarchyFeature=void 0;var Bh=kt(),EN=t=>class extends t{get typeHierarchy(){return{onPrepare:e=>this.connection.onRequest(Bh.TypeHierarchyPrepareRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n),void 0)),onSupertypes:e=>{let n=Bh.TypeHierarchySupertypesRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))},onSubtypes:e=>{let n=Bh.TypeHierarchySubtypesRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))}}}};hu.TypeHierarchyFeature=EN});var Zw=I(gu=>{"use strict";Object.defineProperty(gu,"__esModule",{value:!0});gu.InlineValueFeature=void 0;var Yw=kt(),IN=t=>class extends t{get inlineValue(){return{refresh:()=>this.connection.sendRequest(Yw.InlineValueRefreshRequest.type),on:e=>this.connection.onRequest(Yw.InlineValueRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n)))}}};gu.InlineValueFeature=IN});var tS=I(pu=>{"use strict";Object.defineProperty(pu,"__esModule",{value:!0});pu.FoldingRangeFeature=void 0;var eS=kt(),MN=t=>class extends t{get foldingRange(){return{refresh:()=>this.connection.sendRequest(eS.FoldingRangeRefreshRequest.type),on:e=>{let n=eS.FoldingRangeRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))}}}};pu.FoldingRangeFeature=MN});var nS=I(mu=>{"use strict";Object.defineProperty(mu,"__esModule",{value:!0});mu.InlayHintFeature=void 0;var Uh=kt(),AN=t=>class extends t{get inlayHint(){return{refresh:()=>this.connection.sendRequest(Uh.InlayHintRefreshRequest.type),on:e=>this.connection.onRequest(Uh.InlayHintRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n))),resolve:e=>this.connection.onRequest(Uh.InlayHintResolveRequest.type,(n,i)=>e(n,i))}}};mu.InlayHintFeature=AN});var rS=I(bu=>{"use strict";Object.defineProperty(bu,"__esModule",{value:!0});bu.DiagnosticFeature=void 0;var Bs=kt(),NN=t=>class extends t{get diagnostics(){return{refresh:()=>this.connection.sendRequest(Bs.DiagnosticRefreshRequest.type),on:e=>this.connection.onRequest(Bs.DocumentDiagnosticRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(Bs.DocumentDiagnosticRequest.partialResult,n))),onWorkspace:e=>this.connection.onRequest(Bs.WorkspaceDiagnosticRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(Bs.WorkspaceDiagnosticRequest.partialResult,n)))}}};bu.DiagnosticFeature=NN});var Wh=I(_u=>{"use strict";Object.defineProperty(_u,"__esModule",{value:!0});_u.TextDocuments=void 0;var Wi=kt(),$h=class{constructor(e){this._configuration=e,this._syncedDocuments=new Map,this._onDidChangeContent=new Wi.Emitter,this._onDidOpen=new Wi.Emitter,this._onDidClose=new Wi.Emitter,this._onDidSave=new Wi.Emitter,this._onWillSave=new Wi.Emitter}get onDidOpen(){return this._onDidOpen.event}get onDidChangeContent(){return this._onDidChangeContent.event}get onWillSave(){return this._onWillSave.event}onWillSaveWaitUntil(e){this._willSaveWaitUntil=e}get onDidSave(){return this._onDidSave.event}get onDidClose(){return this._onDidClose.event}get(e){return this._syncedDocuments.get(e)}all(){return Array.from(this._syncedDocuments.values())}keys(){return Array.from(this._syncedDocuments.keys())}listen(e){e.__textDocumentSync=Wi.TextDocumentSyncKind.Incremental;let n=[];return n.push(e.onDidOpenTextDocument(i=>{let s=i.textDocument,c=this._configuration.create(s.uri,s.languageId,s.version,s.text);this._syncedDocuments.set(s.uri,c);let u=Object.freeze({document:c});this._onDidOpen.fire(u),this._onDidChangeContent.fire(u)})),n.push(e.onDidChangeTextDocument(i=>{let s=i.textDocument,c=i.contentChanges;if(c.length===0)return;let{version:u}=s;if(u==null)throw new Error(`Received document change event for ${s.uri} without valid version identifier`);let d=this._syncedDocuments.get(s.uri);d!==void 0&&(d=this._configuration.update(d,c,u),this._syncedDocuments.set(s.uri,d),this._onDidChangeContent.fire(Object.freeze({document:d})))})),n.push(e.onDidCloseTextDocument(i=>{let s=this._syncedDocuments.get(i.textDocument.uri);s!==void 0&&(this._syncedDocuments.delete(i.textDocument.uri),this._onDidClose.fire(Object.freeze({document:s})))})),n.push(e.onWillSaveTextDocument(i=>{let s=this._syncedDocuments.get(i.textDocument.uri);s!==void 0&&this._onWillSave.fire(Object.freeze({document:s,reason:i.reason}))})),n.push(e.onWillSaveTextDocumentWaitUntil((i,s)=>{let c=this._syncedDocuments.get(i.textDocument.uri);return c!==void 0&&this._willSaveWaitUntil?this._willSaveWaitUntil(Object.freeze({document:c,reason:i.reason}),s):[]})),n.push(e.onDidSaveTextDocument(i=>{let s=this._syncedDocuments.get(i.textDocument.uri);s!==void 0&&this._onDidSave.fire(Object.freeze({document:s}))})),Wi.Disposable.create(()=>{n.forEach(i=>i.dispose())})}};_u.TextDocuments=$h});var zh=I(Ao=>{"use strict";Object.defineProperty(Ao,"__esModule",{value:!0});Ao.NotebookDocuments=Ao.NotebookSyncFeature=void 0;var Wn=kt(),iS=Wh(),qN=t=>class extends t{get synchronization(){return{onDidOpenNotebookDocument:e=>this.connection.onNotification(Wn.DidOpenNotebookDocumentNotification.type,n=>{e(n)}),onDidChangeNotebookDocument:e=>this.connection.onNotification(Wn.DidChangeNotebookDocumentNotification.type,n=>{e(n)}),onDidSaveNotebookDocument:e=>this.connection.onNotification(Wn.DidSaveNotebookDocumentNotification.type,n=>{e(n)}),onDidCloseNotebookDocument:e=>this.connection.onNotification(Wn.DidCloseNotebookDocumentNotification.type,n=>{e(n)})}}};Ao.NotebookSyncFeature=qN;var vu=class t{onDidOpenTextDocument(e){return this.openHandler=e,Wn.Disposable.create(()=>{this.openHandler=void 0})}openTextDocument(e){this.openHandler&&this.openHandler(e)}onDidChangeTextDocument(e){return this.changeHandler=e,Wn.Disposable.create(()=>{this.changeHandler=e})}changeTextDocument(e){this.changeHandler&&this.changeHandler(e)}onDidCloseTextDocument(e){return this.closeHandler=e,Wn.Disposable.create(()=>{this.closeHandler=void 0})}closeTextDocument(e){this.closeHandler&&this.closeHandler(e)}onWillSaveTextDocument(){return t.NULL_DISPOSE}onWillSaveTextDocumentWaitUntil(){return t.NULL_DISPOSE}onDidSaveTextDocument(){return t.NULL_DISPOSE}};vu.NULL_DISPOSE=Object.freeze({dispose:()=>{}});var jh=class{constructor(e){e instanceof iS.TextDocuments?this._cellTextDocuments=e:this._cellTextDocuments=new iS.TextDocuments(e),this.notebookDocuments=new Map,this.notebookCellMap=new Map,this._onDidOpen=new Wn.Emitter,this._onDidChange=new Wn.Emitter,this._onDidSave=new Wn.Emitter,this._onDidClose=new Wn.Emitter}get cellTextDocuments(){return this._cellTextDocuments}getCellTextDocument(e){return this._cellTextDocuments.get(e.document)}getNotebookDocument(e){return this.notebookDocuments.get(e)}getNotebookCell(e){let n=this.notebookCellMap.get(e);return n&&n[0]}findNotebookDocumentForCell(e){let n=typeof e=="string"?e:e.document,i=this.notebookCellMap.get(n);return i&&i[1]}get onDidOpen(){return this._onDidOpen.event}get onDidSave(){return this._onDidSave.event}get onDidChange(){return this._onDidChange.event}get onDidClose(){return this._onDidClose.event}listen(e){let n=new vu,i=[];return i.push(this.cellTextDocuments.listen(n)),i.push(e.notebooks.synchronization.onDidOpenNotebookDocument(s=>{this.notebookDocuments.set(s.notebookDocument.uri,s.notebookDocument);for(let c of s.cellTextDocuments)n.openTextDocument({textDocument:c});this.updateCellMap(s.notebookDocument),this._onDidOpen.fire(s.notebookDocument)})),i.push(e.notebooks.synchronization.onDidChangeNotebookDocument(s=>{let c=this.notebookDocuments.get(s.notebookDocument.uri);if(c===void 0)return;c.version=s.notebookDocument.version;let u=c.metadata,d=!1,f=s.change;f.metadata!==void 0&&(d=!0,c.metadata=f.metadata);let m=[],y=[],x=[],D=[];if(f.cells!==void 0){let K=f.cells;if(K.structure!==void 0){let F=K.structure.array;if(c.cells.splice(F.start,F.deleteCount,...F.cells!==void 0?F.cells:[]),K.structure.didOpen!==void 0)for(let P of K.structure.didOpen)n.openTextDocument({textDocument:P}),m.push(P.uri);if(K.structure.didClose)for(let P of K.structure.didClose)n.closeTextDocument({textDocument:P}),y.push(P.uri)}if(K.data!==void 0){let F=new Map(K.data.map(P=>[P.document,P]));for(let P=0;P<=c.cells.length;P++){let U=F.get(c.cells[P].document);if(U!==void 0){let te=c.cells.splice(P,1,U);if(x.push({old:te[0],new:U}),F.delete(U.document),F.size===0)break}}}if(K.textContent!==void 0)for(let F of K.textContent)n.changeTextDocument({textDocument:F.document,contentChanges:F.changes}),D.push(F.document.uri)}this.updateCellMap(c);let N={notebookDocument:c};d&&(N.metadata={old:u,new:c.metadata});let A=[];for(let K of m)A.push(this.getNotebookCell(K));let L=[];for(let K of y)L.push(this.getNotebookCell(K));let B=[];for(let K of D)B.push(this.getNotebookCell(K));(A.length>0||L.length>0||x.length>0||B.length>0)&&(N.cells={added:A,removed:L,changed:{data:x,textContent:B}}),(N.metadata!==void 0||N.cells!==void 0)&&this._onDidChange.fire(N)})),i.push(e.notebooks.synchronization.onDidSaveNotebookDocument(s=>{let c=this.notebookDocuments.get(s.notebookDocument.uri);c!==void 0&&this._onDidSave.fire(c)})),i.push(e.notebooks.synchronization.onDidCloseNotebookDocument(s=>{let c=this.notebookDocuments.get(s.notebookDocument.uri);if(c!==void 0){this._onDidClose.fire(c);for(let u of s.cellTextDocuments)n.closeTextDocument({textDocument:u});this.notebookDocuments.delete(s.notebookDocument.uri);for(let u of c.cells)this.notebookCellMap.delete(u.document)}})),Wn.Disposable.create(()=>{i.forEach(s=>s.dispose())})}updateCellMap(e){for(let n of e.cells)this.notebookCellMap.set(n.document,[n,e])}};Ao.NotebookDocuments=jh});var oS=I(yu=>{"use strict";Object.defineProperty(yu,"__esModule",{value:!0});yu.MonikerFeature=void 0;var ON=kt(),LN=t=>class extends t{get moniker(){return{on:e=>{let n=ON.MonikerRequest.type;return this.connection.onRequest(n,(i,s)=>e(i,s,this.attachWorkDoneProgress(i),this.attachPartialResultProgress(n,i)))}}}};yu.MonikerFeature=LN});var Jh=I($e=>{"use strict";Object.defineProperty($e,"__esModule",{value:!0});$e.createConnection=$e.combineFeatures=$e.combineNotebooksFeatures=$e.combineLanguagesFeatures=$e.combineWorkspaceFeatures=$e.combineWindowFeatures=$e.combineClientFeatures=$e.combineTracerFeatures=$e.combineTelemetryFeatures=$e.combineConsoleFeatures=$e._NotebooksImpl=$e._LanguagesImpl=$e.BulkUnregistration=$e.BulkRegistration=$e.ErrorMessageTracker=void 0;var ee=kt(),jn=pc(),Vh=Mh(),Se=zw(),BN=Hw(),UN=Vw(),$N=Gw(),WN=Lh(),jN=Qw(),zN=Kw(),HN=Jw(),VN=Xw(),GN=Zw(),QN=tS(),KN=nS(),JN=rS(),XN=zh(),YN=oS();function Hh(t){if(t!==null)return t}var Gh=class{constructor(){this._messages=Object.create(null)}add(e){let n=this._messages[e];n||(n=0),n++,this._messages[e]=n}sendErrors(e){Object.keys(this._messages).forEach(n=>{e.window.showErrorMessage(n)})}};$e.ErrorMessageTracker=Gh;var wu=class{constructor(){}rawAttach(e){this._rawConnection=e}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}fillServerCapabilities(e){}initialize(e){}error(e){this.send(ee.MessageType.Error,e)}warn(e){this.send(ee.MessageType.Warning,e)}info(e){this.send(ee.MessageType.Info,e)}log(e){this.send(ee.MessageType.Log,e)}debug(e){this.send(ee.MessageType.Debug,e)}send(e,n){this._rawConnection&&this._rawConnection.sendNotification(ee.LogMessageNotification.type,{type:e,message:n}).catch(()=>{(0,ee.RAL)().console.error("Sending log message failed")})}},Qh=class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}showErrorMessage(e,...n){let i={type:ee.MessageType.Error,message:e,actions:n};return this.connection.sendRequest(ee.ShowMessageRequest.type,i).then(Hh)}showWarningMessage(e,...n){let i={type:ee.MessageType.Warning,message:e,actions:n};return this.connection.sendRequest(ee.ShowMessageRequest.type,i).then(Hh)}showInformationMessage(e,...n){let i={type:ee.MessageType.Info,message:e,actions:n};return this.connection.sendRequest(ee.ShowMessageRequest.type,i).then(Hh)}},sS=(0,jN.ShowDocumentFeature)((0,Se.ProgressFeature)(Qh)),aS;(function(t){function e(){return new Su}t.create=e})(aS||($e.BulkRegistration=aS={}));var Su=class{constructor(){this._registrations=[],this._registered=new Set}add(e,n){let i=jn.string(e)?e:e.method;if(this._registered.has(i))throw new Error(`${i} is already added to this registration`);let s=Vh.generateUuid();this._registrations.push({id:s,method:i,registerOptions:n||{}}),this._registered.add(i)}asRegistrationParams(){return{registrations:this._registrations}}},cS;(function(t){function e(){return new Us(void 0,[])}t.create=e})(cS||($e.BulkUnregistration=cS={}));var Us=class{constructor(e,n){this._connection=e,this._unregistrations=new Map,n.forEach(i=>{this._unregistrations.set(i.method,i)})}get isAttached(){return!!this._connection}attach(e){this._connection=e}add(e){this._unregistrations.set(e.method,e)}dispose(){let e=[];for(let i of this._unregistrations.values())e.push(i);let n={unregisterations:e};this._connection.sendRequest(ee.UnregistrationRequest.type,n).catch(()=>{this._connection.console.info("Bulk unregistration failed.")})}disposeSingle(e){let n=jn.string(e)?e:e.method,i=this._unregistrations.get(n);if(!i)return!1;let s={unregisterations:[i]};return this._connection.sendRequest(ee.UnregistrationRequest.type,s).then(()=>{this._unregistrations.delete(n)},c=>{this._connection.console.info(`Un-registering request handler for ${i.id} failed.`)}),!0}},xu=class{attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}register(e,n,i){return e instanceof Su?this.registerMany(e):e instanceof Us?this.registerSingle1(e,n,i):this.registerSingle2(e,n)}registerSingle1(e,n,i){let s=jn.string(n)?n:n.method,c=Vh.generateUuid(),u={registrations:[{id:c,method:s,registerOptions:i||{}}]};return e.isAttached||e.attach(this.connection),this.connection.sendRequest(ee.RegistrationRequest.type,u).then(d=>(e.add({id:c,method:s}),e),d=>(this.connection.console.info(`Registering request handler for ${s} failed.`),Promise.reject(d)))}registerSingle2(e,n){let i=jn.string(e)?e:e.method,s=Vh.generateUuid(),c={registrations:[{id:s,method:i,registerOptions:n||{}}]};return this.connection.sendRequest(ee.RegistrationRequest.type,c).then(u=>ee.Disposable.create(()=>{this.unregisterSingle(s,i).catch(()=>{this.connection.console.info(`Un-registering capability with id ${s} failed.`)})}),u=>(this.connection.console.info(`Registering request handler for ${i} failed.`),Promise.reject(u)))}unregisterSingle(e,n){let i={unregisterations:[{id:e,method:n}]};return this.connection.sendRequest(ee.UnregistrationRequest.type,i).catch(()=>{this.connection.console.info(`Un-registering request handler for ${e} failed.`)})}registerMany(e){let n=e.asRegistrationParams();return this.connection.sendRequest(ee.RegistrationRequest.type,n).then(()=>new Us(this._connection,n.registrations.map(i=>({id:i.id,method:i.method}))),i=>(this.connection.console.info("Bulk registration failed."),Promise.reject(i)))}},Kh=class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}applyEdit(e){function n(s){return s&&!!s.edit}let i=n(e)?e:{edit:e};return this.connection.sendRequest(ee.ApplyWorkspaceEditRequest.type,i)}},uS=(0,zN.FileOperationsFeature)((0,UN.WorkspaceFoldersFeature)((0,BN.ConfigurationFeature)(Kh))),Pu=class{constructor(){this._trace=ee.Trace.Off}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}set trace(e){this._trace=e}log(e,n){this._trace!==ee.Trace.Off&&this.connection.sendNotification(ee.LogTraceNotification.type,{message:e,verbose:this._trace===ee.Trace.Verbose?n:void 0}).catch(()=>{})}},Cu=class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}logEvent(e){this.connection.sendNotification(ee.TelemetryEventNotification.type,e).catch(()=>{this.connection.console.log("Sending TelemetryEventNotification failed")})}},ku=class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}attachWorkDoneProgress(e){return(0,Se.attachWorkDone)(this.connection,e)}attachPartialResultProgress(e,n){return(0,Se.attachPartialResult)(this.connection,n)}};$e._LanguagesImpl=ku;var lS=(0,QN.FoldingRangeFeature)((0,YN.MonikerFeature)((0,JN.DiagnosticFeature)((0,KN.InlayHintFeature)((0,GN.InlineValueFeature)((0,VN.TypeHierarchyFeature)((0,HN.LinkedEditingRangeFeature)((0,WN.SemanticTokensFeature)((0,$N.CallHierarchyFeature)(ku))))))))),Tu=class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}attachWorkDoneProgress(e){return(0,Se.attachWorkDone)(this.connection,e)}attachPartialResultProgress(e,n){return(0,Se.attachPartialResult)(this.connection,n)}};$e._NotebooksImpl=Tu;var dS=(0,XN.NotebookSyncFeature)(Tu);function fS(t,e){return function(n){return e(t(n))}}$e.combineConsoleFeatures=fS;function hS(t,e){return function(n){return e(t(n))}}$e.combineTelemetryFeatures=hS;function gS(t,e){return function(n){return e(t(n))}}$e.combineTracerFeatures=gS;function pS(t,e){return function(n){return e(t(n))}}$e.combineClientFeatures=pS;function mS(t,e){return function(n){return e(t(n))}}$e.combineWindowFeatures=mS;function bS(t,e){return function(n){return e(t(n))}}$e.combineWorkspaceFeatures=bS;function _S(t,e){return function(n){return e(t(n))}}$e.combineLanguagesFeatures=_S;function vS(t,e){return function(n){return e(t(n))}}$e.combineNotebooksFeatures=vS;function ZN(t,e){function n(s,c,u){return s&&c?u(s,c):s||c}return{__brand:"features",console:n(t.console,e.console,fS),tracer:n(t.tracer,e.tracer,gS),telemetry:n(t.telemetry,e.telemetry,hS),client:n(t.client,e.client,pS),window:n(t.window,e.window,mS),workspace:n(t.workspace,e.workspace,bS),languages:n(t.languages,e.languages,_S),notebooks:n(t.notebooks,e.notebooks,vS)}}$e.combineFeatures=ZN;function eq(t,e,n){let i=n&&n.console?new(n.console(wu)):new wu,s=t(i);i.rawAttach(s);let c=n&&n.tracer?new(n.tracer(Pu)):new Pu,u=n&&n.telemetry?new(n.telemetry(Cu)):new Cu,d=n&&n.client?new(n.client(xu)):new xu,f=n&&n.window?new(n.window(sS)):new sS,m=n&&n.workspace?new(n.workspace(uS)):new uS,y=n&&n.languages?new(n.languages(lS)):new lS,x=n&&n.notebooks?new(n.notebooks(dS)):new dS,D=[i,c,u,d,f,m,y,x];function N(F){return F instanceof Promise?F:jn.thenable(F)?new Promise((P,U)=>{F.then(te=>P(te),te=>U(te))}):Promise.resolve(F)}let A,L,B,K={listen:()=>s.listen(),sendRequest:(F,...P)=>s.sendRequest(jn.string(F)?F:F.method,...P),onRequest:(F,P)=>s.onRequest(F,P),sendNotification:(F,P)=>{let U=jn.string(F)?F:F.method;return s.sendNotification(U,P)},onNotification:(F,P)=>s.onNotification(F,P),onProgress:s.onProgress,sendProgress:s.sendProgress,onInitialize:F=>(L=F,{dispose:()=>{L=void 0}}),onInitialized:F=>s.onNotification(ee.InitializedNotification.type,F),onShutdown:F=>(A=F,{dispose:()=>{A=void 0}}),onExit:F=>(B=F,{dispose:()=>{B=void 0}}),get console(){return i},get telemetry(){return u},get tracer(){return c},get client(){return d},get window(){return f},get workspace(){return m},get languages(){return y},get notebooks(){return x},onDidChangeConfiguration:F=>s.onNotification(ee.DidChangeConfigurationNotification.type,F),onDidChangeWatchedFiles:F=>s.onNotification(ee.DidChangeWatchedFilesNotification.type,F),__textDocumentSync:void 0,onDidOpenTextDocument:F=>s.onNotification(ee.DidOpenTextDocumentNotification.type,F),onDidChangeTextDocument:F=>s.onNotification(ee.DidChangeTextDocumentNotification.type,F),onDidCloseTextDocument:F=>s.onNotification(ee.DidCloseTextDocumentNotification.type,F),onWillSaveTextDocument:F=>s.onNotification(ee.WillSaveTextDocumentNotification.type,F),onWillSaveTextDocumentWaitUntil:F=>s.onRequest(ee.WillSaveTextDocumentWaitUntilRequest.type,F),onDidSaveTextDocument:F=>s.onNotification(ee.DidSaveTextDocumentNotification.type,F),sendDiagnostics:F=>s.sendNotification(ee.PublishDiagnosticsNotification.type,F),onHover:F=>s.onRequest(ee.HoverRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),void 0)),onCompletion:F=>s.onRequest(ee.CompletionRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onCompletionResolve:F=>s.onRequest(ee.CompletionResolveRequest.type,F),onSignatureHelp:F=>s.onRequest(ee.SignatureHelpRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),void 0)),onDeclaration:F=>s.onRequest(ee.DeclarationRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onDefinition:F=>s.onRequest(ee.DefinitionRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onTypeDefinition:F=>s.onRequest(ee.TypeDefinitionRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onImplementation:F=>s.onRequest(ee.ImplementationRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onReferences:F=>s.onRequest(ee.ReferencesRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onDocumentHighlight:F=>s.onRequest(ee.DocumentHighlightRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onDocumentSymbol:F=>s.onRequest(ee.DocumentSymbolRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onWorkspaceSymbol:F=>s.onRequest(ee.WorkspaceSymbolRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onWorkspaceSymbolResolve:F=>s.onRequest(ee.WorkspaceSymbolResolveRequest.type,F),onCodeAction:F=>s.onRequest(ee.CodeActionRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onCodeActionResolve:F=>s.onRequest(ee.CodeActionResolveRequest.type,(P,U)=>F(P,U)),onCodeLens:F=>s.onRequest(ee.CodeLensRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onCodeLensResolve:F=>s.onRequest(ee.CodeLensResolveRequest.type,(P,U)=>F(P,U)),onDocumentFormatting:F=>s.onRequest(ee.DocumentFormattingRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),void 0)),onDocumentRangeFormatting:F=>s.onRequest(ee.DocumentRangeFormattingRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),void 0)),onDocumentOnTypeFormatting:F=>s.onRequest(ee.DocumentOnTypeFormattingRequest.type,(P,U)=>F(P,U)),onRenameRequest:F=>s.onRequest(ee.RenameRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),void 0)),onPrepareRename:F=>s.onRequest(ee.PrepareRenameRequest.type,(P,U)=>F(P,U)),onDocumentLinks:F=>s.onRequest(ee.DocumentLinkRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onDocumentLinkResolve:F=>s.onRequest(ee.DocumentLinkResolveRequest.type,(P,U)=>F(P,U)),onDocumentColor:F=>s.onRequest(ee.DocumentColorRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onColorPresentation:F=>s.onRequest(ee.ColorPresentationRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onFoldingRanges:F=>s.onRequest(ee.FoldingRangeRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onSelectionRanges:F=>s.onRequest(ee.SelectionRangeRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),(0,Se.attachPartialResult)(s,P))),onExecuteCommand:F=>s.onRequest(ee.ExecuteCommandRequest.type,(P,U)=>F(P,U,(0,Se.attachWorkDone)(s,P),void 0)),dispose:()=>s.dispose()};for(let F of D)F.attach(K);return s.onRequest(ee.InitializeRequest.type,F=>{e.initialize(F),jn.string(F.trace)&&(c.trace=ee.Trace.fromString(F.trace));for(let P of D)P.initialize(F.capabilities);if(L){let P=L(F,new ee.CancellationTokenSource().token,(0,Se.attachWorkDone)(s,F),void 0);return N(P).then(U=>{if(U instanceof ee.ResponseError)return U;let te=U;te||(te={capabilities:{}});let ye=te.capabilities;ye||(ye={},te.capabilities=ye),ye.textDocumentSync===void 0||ye.textDocumentSync===null?ye.textDocumentSync=jn.number(K.__textDocumentSync)?K.__textDocumentSync:ee.TextDocumentSyncKind.None:!jn.number(ye.textDocumentSync)&&!jn.number(ye.textDocumentSync.change)&&(ye.textDocumentSync.change=jn.number(K.__textDocumentSync)?K.__textDocumentSync:ee.TextDocumentSyncKind.None);for(let Be of D)Be.fillServerCapabilities(ye);return te})}else{let P={capabilities:{textDocumentSync:ee.TextDocumentSyncKind.None}};for(let U of D)U.fillServerCapabilities(P.capabilities);return P}}),s.onRequest(ee.ShutdownRequest.type,()=>{if(e.shutdownReceived=!0,A)return A(new ee.CancellationTokenSource().token)}),s.onNotification(ee.ExitNotification.type,()=>{try{B&&B()}finally{e.shutdownReceived?e.exit(0):e.exit(1)}}),s.onNotification(ee.SetTraceNotification.type,F=>{c.trace=ee.Trace.fromString(F.value)}),K}$e.createConnection=eq});var yS=I(mn=>{"use strict";Object.defineProperty(mn,"__esModule",{value:!0});mn.resolveModulePath=mn.FileSystem=mn.resolveGlobalYarnPath=mn.resolveGlobalNodePath=mn.resolve=mn.uriToFilePath=void 0;var tq=require("url"),tr=require("path"),Xh=require("fs"),tg=require("child_process");function nq(t){let e=tq.parse(t);if(e.protocol!=="file:"||!e.path)return;let n=e.path.split("/");for(var i=0,s=n.length;i<s;i++)n[i]=decodeURIComponent(n[i]);if(process.platform==="win32"&&n.length>1){let c=n[0],u=n[1];c.length===0&&u.length>1&&u[1]===":"&&n.shift()}return tr.normalize(n.join("/"))}mn.uriToFilePath=nq;function Yh(){return process.platform==="win32"}function Ru(t,e,n,i){let s="NODE_PATH",c=["var p = process;","p.on('message',function(m){","if(m.c==='e'){","p.exit(0);","}","else if(m.c==='rs'){","try{","var r=require.resolve(m.a);","p.send({c:'r',s:true,r:r});","}","catch(err){","p.send({c:'r',s:false});","}","}","});"].join("");return new Promise((u,d)=>{let f=process.env,m=Object.create(null);Object.keys(f).forEach(y=>m[y]=f[y]),e&&Xh.existsSync(e)&&(m[s]?m[s]=e+tr.delimiter+m[s]:m[s]=e,i&&i(`NODE_PATH value is: ${m[s]}`)),m.ELECTRON_RUN_AS_NODE="1";try{let y=(0,tg.fork)("",[],{cwd:n,env:m,execArgv:["-e",c]});if(y.pid===void 0){d(new Error(`Starting process to resolve node module  ${t} failed`));return}y.on("error",D=>{d(D)}),y.on("message",D=>{D.c==="r"&&(y.send({c:"e"}),D.s?u(D.r):d(new Error(`Failed to resolve module: ${t}`)))});let x={c:"rs",a:t};y.send(x)}catch(y){d(y)}})}mn.resolve=Ru;function Zh(t){let e="npm",n=Object.create(null);Object.keys(process.env).forEach(c=>n[c]=process.env[c]),n.NO_UPDATE_NOTIFIER="true";let i={encoding:"utf8",env:n};Yh()&&(e="npm.cmd",i.shell=!0);let s=()=>{};try{process.on("SIGPIPE",s);let c=(0,tg.spawnSync)(e,["config","get","prefix"],i).stdout;if(!c){t&&t("'npm config get prefix' didn't return a value.");return}let u=c.trim();return t&&t(`'npm config get prefix' value is: ${u}`),u.length>0?Yh()?tr.join(u,"node_modules"):tr.join(u,"lib","node_modules"):void 0}catch{return}finally{process.removeListener("SIGPIPE",s)}}mn.resolveGlobalNodePath=Zh;function rq(t){let e="yarn",n={encoding:"utf8"};Yh()&&(e="yarn.cmd",n.shell=!0);let i=()=>{};try{process.on("SIGPIPE",i);let s=(0,tg.spawnSync)(e,["global","dir","--json"],n),c=s.stdout;if(!c){t&&(t("'yarn global dir' didn't return a value."),s.stderr&&t(s.stderr));return}let u=c.trim().split(/\r?\n/);for(let d of u)try{let f=JSON.parse(d);if(f.type==="log")return tr.join(f.data,"node_modules")}catch{}return}catch{return}finally{process.removeListener("SIGPIPE",i)}}mn.resolveGlobalYarnPath=rq;var eg;(function(t){let e;function n(){return e!==void 0||(process.platform==="win32"?e=!1:e=!Xh.existsSync(__filename.toUpperCase())||!Xh.existsSync(__filename.toLowerCase())),e}t.isCaseSensitive=n;function i(s,c){return n()?tr.normalize(c).indexOf(tr.normalize(s))===0:tr.normalize(c).toLowerCase().indexOf(tr.normalize(s).toLowerCase())===0}t.isParent=i})(eg||(mn.FileSystem=eg={}));function iq(t,e,n,i){return n?(tr.isAbsolute(n)||(n=tr.join(t,n)),Ru(e,n,n,i).then(s=>eg.isParent(n,s)?s:Promise.reject(new Error(`Failed to load ${e} from node path location.`))).then(void 0,s=>Ru(e,Zh(i),t,i))):Ru(e,Zh(i),t,i)}mn.resolveModulePath=iq});var ng=I((Dz,wS)=>{"use strict";wS.exports=kt()});var SS=I(Du=>{"use strict";Object.defineProperty(Du,"__esModule",{value:!0});Du.InlineCompletionFeature=void 0;var oq=kt(),sq=t=>class extends t{get inlineCompletion(){return{on:e=>this.connection.onRequest(oq.InlineCompletionRequest.type,(n,i)=>e(n,i,this.attachWorkDoneProgress(n)))}}};Du.InlineCompletionFeature=sq});var CS=I(Kt=>{"use strict";var aq=Kt&&Kt.__createBinding||(Object.create?function(t,e,n,i){i===void 0&&(i=n);var s=Object.getOwnPropertyDescriptor(e,n);(!s||("get"in s?!e.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,i,s)}:function(t,e,n,i){i===void 0&&(i=n),t[i]=e[n]}),PS=Kt&&Kt.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&aq(e,t,n)};Object.defineProperty(Kt,"__esModule",{value:!0});Kt.ProposedFeatures=Kt.NotebookDocuments=Kt.TextDocuments=Kt.SemanticTokensBuilder=void 0;var cq=Lh();Object.defineProperty(Kt,"SemanticTokensBuilder",{enumerable:!0,get:function(){return cq.SemanticTokensBuilder}});var uq=SS();PS(kt(),Kt);var lq=Wh();Object.defineProperty(Kt,"TextDocuments",{enumerable:!0,get:function(){return lq.TextDocuments}});var dq=zh();Object.defineProperty(Kt,"NotebookDocuments",{enumerable:!0,get:function(){return dq.NotebookDocuments}});PS(Jh(),Kt);var xS;(function(t){t.all={__brand:"features",languages:uq.InlineCompletionFeature}})(xS||(Kt.ProposedFeatures=xS={}))});var ig=I(zn=>{"use strict";var fq=zn&&zn.__createBinding||(Object.create?function(t,e,n,i){i===void 0&&(i=n);var s=Object.getOwnPropertyDescriptor(e,n);(!s||("get"in s?!e.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,i,s)}:function(t,e,n,i){i===void 0&&(i=n),t[i]=e[n]}),DS=zn&&zn.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&fq(e,t,n)};Object.defineProperty(zn,"__esModule",{value:!0});zn.createConnection=zn.Files=void 0;var kS=require("node:util"),rg=pc(),hq=Jh(),$s=yS(),ji=ng();DS(ng(),zn);DS(CS(),zn);var TS;(function(t){t.uriToFilePath=$s.uriToFilePath,t.resolveGlobalNodePath=$s.resolveGlobalNodePath,t.resolveGlobalYarnPath=$s.resolveGlobalYarnPath,t.resolve=$s.resolve,t.resolveModulePath=$s.resolveModulePath})(TS||(zn.Files=TS={}));var RS;function Fu(){if(RS!==void 0)try{RS.end()}catch{}}var No=!1,FS;function gq(){let t="--clientProcessId";function e(n){try{let i=parseInt(n);isNaN(i)||(FS=setInterval(()=>{try{process.kill(i,0)}catch{Fu(),process.exit(No?0:1)}},3e3))}catch{}}for(let n=2;n<process.argv.length;n++){let i=process.argv[n];if(i===t&&n+1<process.argv.length){e(process.argv[n+1]);return}else{let s=i.split("=");s[0]===t&&e(s[1])}}}gq();var pq={initialize:t=>{let e=t.processId;rg.number(e)&&FS===void 0&&setInterval(()=>{try{process.kill(e,0)}catch{process.exit(No?0:1)}},3e3)},get shutdownReceived(){return No},set shutdownReceived(t){No=t},exit:t=>{Fu(),process.exit(t)}};function mq(t,e,n,i){let s,c,u,d;return t!==void 0&&t.__brand==="features"&&(s=t,t=e,e=n,n=i),ji.ConnectionStrategy.is(t)||ji.ConnectionOptions.is(t)?d=t:(c=t,u=e,d=n),bq(c,u,d,s)}zn.createConnection=mq;function bq(t,e,n,i){let s=!1;if(!t&&!e&&process.argv.length>2){let f,m,y=process.argv.slice(2);for(let x=0;x<y.length;x++){let D=y[x];if(D==="--node-ipc"){t=new ji.IPCMessageReader(process),e=new ji.IPCMessageWriter(process);break}else if(D==="--stdio"){s=!0,t=process.stdin,e=process.stdout;break}else if(D==="--socket"){f=parseInt(y[x+1]);break}else if(D==="--pipe"){m=y[x+1];break}else{var c=D.split("=");if(c[0]==="--socket"){f=parseInt(c[1]);break}else if(c[0]==="--pipe"){m=c[1];break}}}if(f){let x=(0,ji.createServerSocketTransport)(f);t=x[0],e=x[1]}else if(m){let x=(0,ji.createServerPipeTransport)(m);t=x[0],e=x[1]}}var u="Use arguments of createConnection or set command line parameters: '--node-ipc', '--stdio' or '--socket={number}'";if(!t)throw new Error("Connection input stream is not set. "+u);if(!e)throw new Error("Connection output stream is not set. "+u);if(rg.func(t.read)&&rg.func(t.on)){let f=t;f.on("end",()=>{Fu(),process.exit(No?0:1)}),f.on("close",()=>{Fu(),process.exit(No?0:1)})}let d=f=>{let m=(0,ji.createProtocolConnection)(t,e,f,n);return s&&_q(f),m};return(0,hq.createConnection)(d,pq,i)}function _q(t){function e(i){return i.map(s=>typeof s=="string"?s:(0,kS.inspect)(s)).join(" ")}let n=new Map;console.assert=function(s,...c){if(!s)if(c.length===0)t.error("Assertion failed");else{let[u,...d]=c;t.error(`Assertion failed: ${u} ${e(d)}`)}},console.count=function(s="default"){let c=String(s),u=n.get(c)??0;u+=1,n.set(c,u),t.log(`${c}: ${c}`)},console.countReset=function(s){s===void 0?n.clear():n.delete(String(s))},console.debug=function(...s){t.log(e(s))},console.dir=function(s,c){t.log((0,kS.inspect)(s,c))},console.log=function(...s){t.log(e(s))},console.error=function(...s){t.error(e(s))},console.trace=function(...s){let c=new Error().stack.replace(/(.+\n){2}/,""),u="Trace";s.length!==0&&(u+=`: ${e(s)}`),t.log(`${u}
${c}`)},console.warn=function(...s){t.warn(e(s))}}});var og=I((Az,ES)=>{"use strict";ES.exports=ig()});var OS=I((Kz,qS)=>{"use strict";function Pq(){this.__data__=[],this.size=0}qS.exports=Pq});var ug=I((Jz,LS)=>{"use strict";function Cq(t,e){return t===e||t!==t&&e!==e}LS.exports=Cq});var js=I((Xz,BS)=>{"use strict";var kq=ug();function Tq(t,e){for(var n=t.length;n--;)if(kq(t[n][0],e))return n;return-1}BS.exports=Tq});var $S=I((Yz,US)=>{"use strict";var Rq=js(),Dq=Array.prototype,Fq=Dq.splice;function Eq(t){var e=this.__data__,n=Rq(e,t);if(n<0)return!1;var i=e.length-1;return n==i?e.pop():Fq.call(e,n,1),--this.size,!0}US.exports=Eq});var jS=I((Zz,WS)=>{"use strict";var Iq=js();function Mq(t){var e=this.__data__,n=Iq(e,t);return n<0?void 0:e[n][1]}WS.exports=Mq});var HS=I((eH,zS)=>{"use strict";var Aq=js();function Nq(t){return Aq(this.__data__,t)>-1}zS.exports=Nq});var GS=I((tH,VS)=>{"use strict";var qq=js();function Oq(t,e){var n=this.__data__,i=qq(n,t);return i<0?(++this.size,n.push([t,e])):n[i][1]=e,this}VS.exports=Oq});var zs=I((nH,QS)=>{"use strict";var Lq=OS(),Bq=$S(),Uq=jS(),$q=HS(),Wq=GS();function qo(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var i=t[e];this.set(i[0],i[1])}}qo.prototype.clear=Lq;qo.prototype.delete=Bq;qo.prototype.get=Uq;qo.prototype.has=$q;qo.prototype.set=Wq;QS.exports=qo});var JS=I((rH,KS)=>{"use strict";var jq=zs();function zq(){this.__data__=new jq,this.size=0}KS.exports=zq});var YS=I((iH,XS)=>{"use strict";function Hq(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}XS.exports=Hq});var ex=I((oH,ZS)=>{"use strict";function Vq(t){return this.__data__.get(t)}ZS.exports=Vq});var nx=I((sH,tx)=>{"use strict";function Gq(t){return this.__data__.has(t)}tx.exports=Gq});var lg=I((aH,rx)=>{"use strict";var Qq=typeof global=="object"&&global&&global.Object===Object&&global;rx.exports=Qq});var nr=I((cH,ix)=>{"use strict";var Kq=lg(),Jq=typeof self=="object"&&self&&self.Object===Object&&self,Xq=Kq||Jq||Function("return this")();ix.exports=Xq});var Nu=I((uH,ox)=>{"use strict";var Yq=nr(),Zq=Yq.Symbol;ox.exports=Zq});var ux=I((lH,cx)=>{"use strict";var sx=Nu(),ax=Object.prototype,e1=ax.hasOwnProperty,t1=ax.toString,Hs=sx?sx.toStringTag:void 0;function n1(t){var e=e1.call(t,Hs),n=t[Hs];try{t[Hs]=void 0;var i=!0}catch{}var s=t1.call(t);return i&&(e?t[Hs]=n:delete t[Hs]),s}cx.exports=n1});var dx=I((dH,lx)=>{"use strict";var r1=Object.prototype,i1=r1.toString;function o1(t){return i1.call(t)}lx.exports=o1});var Vs=I((fH,gx)=>{"use strict";var fx=Nu(),s1=ux(),a1=dx(),c1="[object Null]",u1="[object Undefined]",hx=fx?fx.toStringTag:void 0;function l1(t){return t==null?t===void 0?u1:c1:hx&&hx in Object(t)?s1(t):a1(t)}gx.exports=l1});var Oo=I((hH,px)=>{"use strict";function d1(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}px.exports=d1});var dg=I((gH,mx)=>{"use strict";var f1=Vs(),h1=Oo(),g1="[object AsyncFunction]",p1="[object Function]",m1="[object GeneratorFunction]",b1="[object Proxy]";function _1(t){if(!h1(t))return!1;var e=f1(t);return e==p1||e==m1||e==g1||e==b1}mx.exports=_1});var _x=I((pH,bx)=>{"use strict";var v1=nr(),y1=v1["__core-js_shared__"];bx.exports=y1});var wx=I((mH,yx)=>{"use strict";var fg=_x(),vx=function(){var t=/[^.]+$/.exec(fg&&fg.keys&&fg.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function w1(t){return!!vx&&vx in t}yx.exports=w1});var hg=I((bH,Sx)=>{"use strict";var S1=Function.prototype,x1=S1.toString;function P1(t){if(t!=null){try{return x1.call(t)}catch{}try{return t+""}catch{}}return""}Sx.exports=P1});var Px=I((_H,xx)=>{"use strict";var C1=dg(),k1=wx(),T1=Oo(),R1=hg(),D1=/[\\^$.*+?()[\]{}|]/g,F1=/^\[object .+?Constructor\]$/,E1=Function.prototype,I1=Object.prototype,M1=E1.toString,A1=I1.hasOwnProperty,N1=RegExp("^"+M1.call(A1).replace(D1,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function q1(t){if(!T1(t)||k1(t))return!1;var e=C1(t)?N1:F1;return e.test(R1(t))}xx.exports=q1});var kx=I((vH,Cx)=>{"use strict";function O1(t,e){return t?.[e]}Cx.exports=O1});var pi=I((yH,Tx)=>{"use strict";var L1=Px(),B1=kx();function U1(t,e){var n=B1(t,e);return L1(n)?n:void 0}Tx.exports=U1});var qu=I((wH,Rx)=>{"use strict";var $1=pi(),W1=nr(),j1=$1(W1,"Map");Rx.exports=j1});var Gs=I((SH,Dx)=>{"use strict";var z1=pi(),H1=z1(Object,"create");Dx.exports=H1});var Ix=I((xH,Ex)=>{"use strict";var Fx=Gs();function V1(){this.__data__=Fx?Fx(null):{},this.size=0}Ex.exports=V1});var Ax=I((PH,Mx)=>{"use strict";function G1(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}Mx.exports=G1});var qx=I((CH,Nx)=>{"use strict";var Q1=Gs(),K1="__lodash_hash_undefined__",J1=Object.prototype,X1=J1.hasOwnProperty;function Y1(t){var e=this.__data__;if(Q1){var n=e[t];return n===K1?void 0:n}return X1.call(e,t)?e[t]:void 0}Nx.exports=Y1});var Lx=I((kH,Ox)=>{"use strict";var Z1=Gs(),eO=Object.prototype,tO=eO.hasOwnProperty;function nO(t){var e=this.__data__;return Z1?e[t]!==void 0:tO.call(e,t)}Ox.exports=nO});var Ux=I((TH,Bx)=>{"use strict";var rO=Gs(),iO="__lodash_hash_undefined__";function oO(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=rO&&e===void 0?iO:e,this}Bx.exports=oO});var Wx=I((RH,$x)=>{"use strict";var sO=Ix(),aO=Ax(),cO=qx(),uO=Lx(),lO=Ux();function Lo(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var i=t[e];this.set(i[0],i[1])}}Lo.prototype.clear=sO;Lo.prototype.delete=aO;Lo.prototype.get=cO;Lo.prototype.has=uO;Lo.prototype.set=lO;$x.exports=Lo});var Hx=I((DH,zx)=>{"use strict";var jx=Wx(),dO=zs(),fO=qu();function hO(){this.size=0,this.__data__={hash:new jx,map:new(fO||dO),string:new jx}}zx.exports=hO});var Gx=I((FH,Vx)=>{"use strict";function gO(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}Vx.exports=gO});var Qs=I((EH,Qx)=>{"use strict";var pO=Gx();function mO(t,e){var n=t.__data__;return pO(e)?n[typeof e=="string"?"string":"hash"]:n.map}Qx.exports=mO});var Jx=I((IH,Kx)=>{"use strict";var bO=Qs();function _O(t){var e=bO(this,t).delete(t);return this.size-=e?1:0,e}Kx.exports=_O});var Yx=I((MH,Xx)=>{"use strict";var vO=Qs();function yO(t){return vO(this,t).get(t)}Xx.exports=yO});var eP=I((AH,Zx)=>{"use strict";var wO=Qs();function SO(t){return wO(this,t).has(t)}Zx.exports=SO});var nP=I((NH,tP)=>{"use strict";var xO=Qs();function PO(t,e){var n=xO(this,t),i=n.size;return n.set(t,e),this.size+=n.size==i?0:1,this}tP.exports=PO});var iP=I((qH,rP)=>{"use strict";var CO=Hx(),kO=Jx(),TO=Yx(),RO=eP(),DO=nP();function Bo(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var i=t[e];this.set(i[0],i[1])}}Bo.prototype.clear=CO;Bo.prototype.delete=kO;Bo.prototype.get=TO;Bo.prototype.has=RO;Bo.prototype.set=DO;rP.exports=Bo});var sP=I((OH,oP)=>{"use strict";var FO=zs(),EO=qu(),IO=iP(),MO=200;function AO(t,e){var n=this.__data__;if(n instanceof FO){var i=n.__data__;if(!EO||i.length<MO-1)return i.push([t,e]),this.size=++n.size,this;n=this.__data__=new IO(i)}return n.set(t,e),this.size=n.size,this}oP.exports=AO});var cP=I((LH,aP)=>{"use strict";var NO=zs(),qO=JS(),OO=YS(),LO=ex(),BO=nx(),UO=sP();function Uo(t){var e=this.__data__=new NO(t);this.size=e.size}Uo.prototype.clear=qO;Uo.prototype.delete=OO;Uo.prototype.get=LO;Uo.prototype.has=BO;Uo.prototype.set=UO;aP.exports=Uo});var lP=I((BH,uP)=>{"use strict";function $O(t,e){for(var n=-1,i=t==null?0:t.length;++n<i&&e(t[n],n,t)!==!1;);return t}uP.exports=$O});var fP=I((UH,dP)=>{"use strict";var WO=pi(),jO=function(){try{var t=WO(Object,"defineProperty");return t({},"",{}),t}catch{}}();dP.exports=jO});var gg=I(($H,gP)=>{"use strict";var hP=fP();function zO(t,e,n){e=="__proto__"&&hP?hP(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}gP.exports=zO});var pg=I((WH,pP)=>{"use strict";var HO=gg(),VO=ug(),GO=Object.prototype,QO=GO.hasOwnProperty;function KO(t,e,n){var i=t[e];(!(QO.call(t,e)&&VO(i,n))||n===void 0&&!(e in t))&&HO(t,e,n)}pP.exports=KO});var Ks=I((jH,mP)=>{"use strict";var JO=pg(),XO=gg();function YO(t,e,n,i){var s=!n;n||(n={});for(var c=-1,u=e.length;++c<u;){var d=e[c],f=i?i(n[d],t[d],d,n,t):void 0;f===void 0&&(f=t[d]),s?XO(n,d,f):JO(n,d,f)}return n}mP.exports=YO});var _P=I((zH,bP)=>{"use strict";function ZO(t,e){for(var n=-1,i=Array(t);++n<t;)i[n]=e(n);return i}bP.exports=ZO});var $o=I((HH,vP)=>{"use strict";function eL(t){return t!=null&&typeof t=="object"}vP.exports=eL});var wP=I((VH,yP)=>{"use strict";var tL=Vs(),nL=$o(),rL="[object Arguments]";function iL(t){return nL(t)&&tL(t)==rL}yP.exports=iL});var CP=I((GH,PP)=>{"use strict";var SP=wP(),oL=$o(),xP=Object.prototype,sL=xP.hasOwnProperty,aL=xP.propertyIsEnumerable,cL=SP(function(){return arguments}())?SP:function(t){return oL(t)&&sL.call(t,"callee")&&!aL.call(t,"callee")};PP.exports=cL});var Ou=I((QH,kP)=>{"use strict";var uL=Array.isArray;kP.exports=uL});var RP=I((KH,TP)=>{"use strict";function lL(){return!1}TP.exports=lL});var mg=I((Js,Wo)=>{"use strict";var dL=nr(),fL=RP(),EP=typeof Js=="object"&&Js&&!Js.nodeType&&Js,DP=EP&&typeof Wo=="object"&&Wo&&!Wo.nodeType&&Wo,hL=DP&&DP.exports===EP,FP=hL?dL.Buffer:void 0,gL=FP?FP.isBuffer:void 0,pL=gL||fL;Wo.exports=pL});var MP=I((JH,IP)=>{"use strict";var mL=9007199254740991,bL=/^(?:0|[1-9]\d*)$/;function _L(t,e){var n=typeof t;return e=e??mL,!!e&&(n=="number"||n!="symbol"&&bL.test(t))&&t>-1&&t%1==0&&t<e}IP.exports=_L});var bg=I((XH,AP)=>{"use strict";var vL=9007199254740991;function yL(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=vL}AP.exports=yL});var qP=I((YH,NP)=>{"use strict";var wL=Vs(),SL=bg(),xL=$o(),PL="[object Arguments]",CL="[object Array]",kL="[object Boolean]",TL="[object Date]",RL="[object Error]",DL="[object Function]",FL="[object Map]",EL="[object Number]",IL="[object Object]",ML="[object RegExp]",AL="[object Set]",NL="[object String]",qL="[object WeakMap]",OL="[object ArrayBuffer]",LL="[object DataView]",BL="[object Float32Array]",UL="[object Float64Array]",$L="[object Int8Array]",WL="[object Int16Array]",jL="[object Int32Array]",zL="[object Uint8Array]",HL="[object Uint8ClampedArray]",VL="[object Uint16Array]",GL="[object Uint32Array]",dt={};dt[BL]=dt[UL]=dt[$L]=dt[WL]=dt[jL]=dt[zL]=dt[HL]=dt[VL]=dt[GL]=!0;dt[PL]=dt[CL]=dt[OL]=dt[kL]=dt[LL]=dt[TL]=dt[RL]=dt[DL]=dt[FL]=dt[EL]=dt[IL]=dt[ML]=dt[AL]=dt[NL]=dt[qL]=!1;function QL(t){return xL(t)&&SL(t.length)&&!!dt[wL(t)]}NP.exports=QL});var Lu=I((ZH,OP)=>{"use strict";function KL(t){return function(e){return t(e)}}OP.exports=KL});var Bu=I((Xs,jo)=>{"use strict";var JL=lg(),LP=typeof Xs=="object"&&Xs&&!Xs.nodeType&&Xs,Ys=LP&&typeof jo=="object"&&jo&&!jo.nodeType&&jo,XL=Ys&&Ys.exports===LP,_g=XL&&JL.process,YL=function(){try{var t=Ys&&Ys.require&&Ys.require("util").types;return t||_g&&_g.binding&&_g.binding("util")}catch{}}();jo.exports=YL});var WP=I((eV,$P)=>{"use strict";var ZL=qP(),eB=Lu(),BP=Bu(),UP=BP&&BP.isTypedArray,tB=UP?eB(UP):ZL;$P.exports=tB});var vg=I((tV,jP)=>{"use strict";var nB=_P(),rB=CP(),iB=Ou(),oB=mg(),sB=MP(),aB=WP(),cB=Object.prototype,uB=cB.hasOwnProperty;function lB(t,e){var n=iB(t),i=!n&&rB(t),s=!n&&!i&&oB(t),c=!n&&!i&&!s&&aB(t),u=n||i||s||c,d=u?nB(t.length,String):[],f=d.length;for(var m in t)(e||uB.call(t,m))&&!(u&&(m=="length"||s&&(m=="offset"||m=="parent")||c&&(m=="buffer"||m=="byteLength"||m=="byteOffset")||sB(m,f)))&&d.push(m);return d}jP.exports=lB});var Uu=I((nV,zP)=>{"use strict";var dB=Object.prototype;function fB(t){var e=t&&t.constructor,n=typeof e=="function"&&e.prototype||dB;return t===n}zP.exports=fB});var yg=I((rV,HP)=>{"use strict";function hB(t,e){return function(n){return t(e(n))}}HP.exports=hB});var GP=I((iV,VP)=>{"use strict";var gB=yg(),pB=gB(Object.keys,Object);VP.exports=pB});var KP=I((oV,QP)=>{"use strict";var mB=Uu(),bB=GP(),_B=Object.prototype,vB=_B.hasOwnProperty;function yB(t){if(!mB(t))return bB(t);var e=[];for(var n in Object(t))vB.call(t,n)&&n!="constructor"&&e.push(n);return e}QP.exports=yB});var wg=I((sV,JP)=>{"use strict";var wB=dg(),SB=bg();function xB(t){return t!=null&&SB(t.length)&&!wB(t)}JP.exports=xB});var $u=I((aV,XP)=>{"use strict";var PB=vg(),CB=KP(),kB=wg();function TB(t){return kB(t)?PB(t):CB(t)}XP.exports=TB});var ZP=I((cV,YP)=>{"use strict";var RB=Ks(),DB=$u();function FB(t,e){return t&&RB(e,DB(e),t)}YP.exports=FB});var tC=I((uV,eC)=>{"use strict";function EB(t){var e=[];if(t!=null)for(var n in Object(t))e.push(n);return e}eC.exports=EB});var rC=I((lV,nC)=>{"use strict";var IB=Oo(),MB=Uu(),AB=tC(),NB=Object.prototype,qB=NB.hasOwnProperty;function OB(t){if(!IB(t))return AB(t);var e=MB(t),n=[];for(var i in t)i=="constructor"&&(e||!qB.call(t,i))||n.push(i);return n}nC.exports=OB});var Wu=I((dV,iC)=>{"use strict";var LB=vg(),BB=rC(),UB=wg();function $B(t){return UB(t)?LB(t,!0):BB(t)}iC.exports=$B});var sC=I((fV,oC)=>{"use strict";var WB=Ks(),jB=Wu();function zB(t,e){return t&&WB(e,jB(e),t)}oC.exports=zB});var dC=I((Zs,zo)=>{"use strict";var HB=nr(),lC=typeof Zs=="object"&&Zs&&!Zs.nodeType&&Zs,aC=lC&&typeof zo=="object"&&zo&&!zo.nodeType&&zo,VB=aC&&aC.exports===lC,cC=VB?HB.Buffer:void 0,uC=cC?cC.allocUnsafe:void 0;function GB(t,e){if(e)return t.slice();var n=t.length,i=uC?uC(n):new t.constructor(n);return t.copy(i),i}zo.exports=GB});var hC=I((hV,fC)=>{"use strict";function QB(t,e){var n=-1,i=t.length;for(e||(e=Array(i));++n<i;)e[n]=t[n];return e}fC.exports=QB});var pC=I((gV,gC)=>{"use strict";function KB(t,e){for(var n=-1,i=t==null?0:t.length,s=0,c=[];++n<i;){var u=t[n];e(u,n,t)&&(c[s++]=u)}return c}gC.exports=KB});var Sg=I((pV,mC)=>{"use strict";function JB(){return[]}mC.exports=JB});var ju=I((mV,_C)=>{"use strict";var XB=pC(),YB=Sg(),ZB=Object.prototype,eU=ZB.propertyIsEnumerable,bC=Object.getOwnPropertySymbols,tU=bC?function(t){return t==null?[]:(t=Object(t),XB(bC(t),function(e){return eU.call(t,e)}))}:YB;_C.exports=tU});var yC=I((bV,vC)=>{"use strict";var nU=Ks(),rU=ju();function iU(t,e){return nU(t,rU(t),e)}vC.exports=iU});var xg=I((_V,wC)=>{"use strict";function oU(t,e){for(var n=-1,i=e.length,s=t.length;++n<i;)t[s+n]=e[n];return t}wC.exports=oU});var Pg=I((vV,SC)=>{"use strict";var sU=yg(),aU=sU(Object.getPrototypeOf,Object);SC.exports=aU});var Cg=I((yV,xC)=>{"use strict";var cU=xg(),uU=Pg(),lU=ju(),dU=Sg(),fU=Object.getOwnPropertySymbols,hU=fU?function(t){for(var e=[];t;)cU(e,lU(t)),t=uU(t);return e}:dU;xC.exports=hU});var CC=I((wV,PC)=>{"use strict";var gU=Ks(),pU=Cg();function mU(t,e){return gU(t,pU(t),e)}PC.exports=mU});var kg=I((SV,kC)=>{"use strict";var bU=xg(),_U=Ou();function vU(t,e,n){var i=e(t);return _U(t)?i:bU(i,n(t))}kC.exports=vU});var RC=I((xV,TC)=>{"use strict";var yU=kg(),wU=ju(),SU=$u();function xU(t){return yU(t,SU,wU)}TC.exports=xU});var FC=I((PV,DC)=>{"use strict";var PU=kg(),CU=Cg(),kU=Wu();function TU(t){return PU(t,kU,CU)}DC.exports=TU});var IC=I((CV,EC)=>{"use strict";var RU=pi(),DU=nr(),FU=RU(DU,"DataView");EC.exports=FU});var AC=I((kV,MC)=>{"use strict";var EU=pi(),IU=nr(),MU=EU(IU,"Promise");MC.exports=MU});var qC=I((TV,NC)=>{"use strict";var AU=pi(),NU=nr(),qU=AU(NU,"Set");NC.exports=qU});var LC=I((RV,OC)=>{"use strict";var OU=pi(),LU=nr(),BU=OU(LU,"WeakMap");OC.exports=BU});var zu=I((DV,HC)=>{"use strict";var Tg=IC(),Rg=qu(),Dg=AC(),Fg=qC(),Eg=LC(),zC=Vs(),Ho=hg(),BC="[object Map]",UU="[object Object]",UC="[object Promise]",$C="[object Set]",WC="[object WeakMap]",jC="[object DataView]",$U=Ho(Tg),WU=Ho(Rg),jU=Ho(Dg),zU=Ho(Fg),HU=Ho(Eg),zi=zC;(Tg&&zi(new Tg(new ArrayBuffer(1)))!=jC||Rg&&zi(new Rg)!=BC||Dg&&zi(Dg.resolve())!=UC||Fg&&zi(new Fg)!=$C||Eg&&zi(new Eg)!=WC)&&(zi=function(t){var e=zC(t),n=e==UU?t.constructor:void 0,i=n?Ho(n):"";if(i)switch(i){case $U:return jC;case WU:return BC;case jU:return UC;case zU:return $C;case HU:return WC}return e});HC.exports=zi});var GC=I((FV,VC)=>{"use strict";var VU=Object.prototype,GU=VU.hasOwnProperty;function QU(t){var e=t.length,n=new t.constructor(e);return e&&typeof t[0]=="string"&&GU.call(t,"index")&&(n.index=t.index,n.input=t.input),n}VC.exports=QU});var KC=I((EV,QC)=>{"use strict";var KU=nr(),JU=KU.Uint8Array;QC.exports=JU});var Hu=I((IV,XC)=>{"use strict";var JC=KC();function XU(t){var e=new t.constructor(t.byteLength);return new JC(e).set(new JC(t)),e}XC.exports=XU});var ZC=I((MV,YC)=>{"use strict";var YU=Hu();function ZU(t,e){var n=e?YU(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}YC.exports=ZU});var tk=I((AV,ek)=>{"use strict";var e$=/\w*$/;function t$(t){var e=new t.constructor(t.source,e$.exec(t));return e.lastIndex=t.lastIndex,e}ek.exports=t$});var sk=I((NV,ok)=>{"use strict";var nk=Nu(),rk=nk?nk.prototype:void 0,ik=rk?rk.valueOf:void 0;function n$(t){return ik?Object(ik.call(t)):{}}ok.exports=n$});var ck=I((qV,ak)=>{"use strict";var r$=Hu();function i$(t,e){var n=e?r$(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}ak.exports=i$});var lk=I((OV,uk)=>{"use strict";var o$=Hu(),s$=ZC(),a$=tk(),c$=sk(),u$=ck(),l$="[object Boolean]",d$="[object Date]",f$="[object Map]",h$="[object Number]",g$="[object RegExp]",p$="[object Set]",m$="[object String]",b$="[object Symbol]",_$="[object ArrayBuffer]",v$="[object DataView]",y$="[object Float32Array]",w$="[object Float64Array]",S$="[object Int8Array]",x$="[object Int16Array]",P$="[object Int32Array]",C$="[object Uint8Array]",k$="[object Uint8ClampedArray]",T$="[object Uint16Array]",R$="[object Uint32Array]";function D$(t,e,n){var i=t.constructor;switch(e){case _$:return o$(t);case l$:case d$:return new i(+t);case v$:return s$(t,n);case y$:case w$:case S$:case x$:case P$:case C$:case k$:case T$:case R$:return u$(t,n);case f$:return new i;case h$:case m$:return new i(t);case g$:return a$(t);case p$:return new i;case b$:return c$(t)}}uk.exports=D$});var hk=I((LV,fk)=>{"use strict";var F$=Oo(),dk=Object.create,E$=function(){function t(){}return function(e){if(!F$(e))return{};if(dk)return dk(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();fk.exports=E$});var pk=I((BV,gk)=>{"use strict";var I$=hk(),M$=Pg(),A$=Uu();function N$(t){return typeof t.constructor=="function"&&!A$(t)?I$(M$(t)):{}}gk.exports=N$});var bk=I((UV,mk)=>{"use strict";var q$=zu(),O$=$o(),L$="[object Map]";function B$(t){return O$(t)&&q$(t)==L$}mk.exports=B$});var wk=I(($V,yk)=>{"use strict";var U$=bk(),$$=Lu(),_k=Bu(),vk=_k&&_k.isMap,W$=vk?$$(vk):U$;yk.exports=W$});var xk=I((WV,Sk)=>{"use strict";var j$=zu(),z$=$o(),H$="[object Set]";function V$(t){return z$(t)&&j$(t)==H$}Sk.exports=V$});var Tk=I((jV,kk)=>{"use strict";var G$=xk(),Q$=Lu(),Pk=Bu(),Ck=Pk&&Pk.isSet,K$=Ck?Q$(Ck):G$;kk.exports=K$});var Ik=I((zV,Ek)=>{"use strict";var J$=cP(),X$=lP(),Y$=pg(),Z$=ZP(),eW=sC(),tW=dC(),nW=hC(),rW=yC(),iW=CC(),oW=RC(),sW=FC(),aW=zu(),cW=GC(),uW=lk(),lW=pk(),dW=Ou(),fW=mg(),hW=wk(),gW=Oo(),pW=Tk(),mW=$u(),bW=Wu(),_W=1,vW=2,yW=4,Rk="[object Arguments]",wW="[object Array]",SW="[object Boolean]",xW="[object Date]",PW="[object Error]",Dk="[object Function]",CW="[object GeneratorFunction]",kW="[object Map]",TW="[object Number]",Fk="[object Object]",RW="[object RegExp]",DW="[object Set]",FW="[object String]",EW="[object Symbol]",IW="[object WeakMap]",MW="[object ArrayBuffer]",AW="[object DataView]",NW="[object Float32Array]",qW="[object Float64Array]",OW="[object Int8Array]",LW="[object Int16Array]",BW="[object Int32Array]",UW="[object Uint8Array]",$W="[object Uint8ClampedArray]",WW="[object Uint16Array]",jW="[object Uint32Array]",it={};it[Rk]=it[wW]=it[MW]=it[AW]=it[SW]=it[xW]=it[NW]=it[qW]=it[OW]=it[LW]=it[BW]=it[kW]=it[TW]=it[Fk]=it[RW]=it[DW]=it[FW]=it[EW]=it[UW]=it[$W]=it[WW]=it[jW]=!0;it[PW]=it[Dk]=it[IW]=!1;function Vu(t,e,n,i,s,c){var u,d=e&_W,f=e&vW,m=e&yW;if(n&&(u=s?n(t,i,s,c):n(t)),u!==void 0)return u;if(!gW(t))return t;var y=dW(t);if(y){if(u=cW(t),!d)return nW(t,u)}else{var x=aW(t),D=x==Dk||x==CW;if(fW(t))return tW(t,d);if(x==Fk||x==Rk||D&&!s){if(u=f||D?{}:lW(t),!d)return f?iW(t,eW(u,t)):rW(t,Z$(u,t))}else{if(!it[x])return s?t:{};u=uW(t,x,d)}}c||(c=new J$);var N=c.get(t);if(N)return N;c.set(t,u),pW(t)?t.forEach(function(B){u.add(Vu(B,e,n,B,t,c))}):hW(t)&&t.forEach(function(B,K){u.set(K,Vu(B,e,n,K,t,c))});var A=m?f?sW:oW:f?bW:mW,L=y?void 0:A(t);return X$(L||t,function(B,K){L&&(K=B,B=t[K]),Y$(u,K,Vu(B,e,n,K,t,c))}),u}Ek.exports=Vu});var Ak=I((HV,Mk)=>{"use strict";var zW=Ik(),HW=1,VW=4;function GW(t){return zW(t,HW|VW)}Mk.exports=GW});var Ig=I((Nk,Gu)=>{"use strict";(function(t,e){"use strict";typeof define=="function"&&define.amd?define(e):typeof Gu=="object"&&Gu.exports?Gu.exports=e():t.log=e()})(Nk,function(){"use strict";var t=function(){},e="undefined",n=typeof window!==e&&typeof window.navigator!==e&&/Trident\/|MSIE /.test(window.navigator.userAgent),i=["trace","debug","info","warn","error"],s={},c=null;function u(A,L){var B=A[L];if(typeof B.bind=="function")return B.bind(A);try{return Function.prototype.bind.call(B,A)}catch{return function(){return Function.prototype.apply.apply(B,[A,arguments])}}}function d(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function f(A){return A==="debug"&&(A="log"),typeof console===e?!1:A==="trace"&&n?d:console[A]!==void 0?u(console,A):console.log!==void 0?u(console,"log"):t}function m(){for(var A=this.getLevel(),L=0;L<i.length;L++){var B=i[L];this[B]=L<A?t:this.methodFactory(B,A,this.name)}if(this.log=this.debug,typeof console===e&&A<this.levels.SILENT)return"No console available for logging"}function y(A){return function(){typeof console!==e&&(m.call(this),this[A].apply(this,arguments))}}function x(A,L,B){return f(A)||y.apply(this,arguments)}function D(A,L){var B=this,K,F,P,U="loglevel";typeof A=="string"?U+=":"+A:typeof A=="symbol"&&(U=void 0);function te(xe){var X=(i[xe]||"silent").toUpperCase();if(!(typeof window===e||!U)){try{window.localStorage[U]=X;return}catch{}try{window.document.cookie=encodeURIComponent(U)+"="+X+";"}catch{}}}function ye(){var xe;if(!(typeof window===e||!U)){try{xe=window.localStorage[U]}catch{}if(typeof xe===e)try{var X=window.document.cookie,z=encodeURIComponent(U),se=X.indexOf(z+"=");se!==-1&&(xe=/^([^;]+)/.exec(X.slice(se+z.length+1))[1])}catch{}return B.levels[xe]===void 0&&(xe=void 0),xe}}function Be(){if(!(typeof window===e||!U)){try{window.localStorage.removeItem(U)}catch{}try{window.document.cookie=encodeURIComponent(U)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch{}}}function Je(xe){var X=xe;if(typeof X=="string"&&B.levels[X.toUpperCase()]!==void 0&&(X=B.levels[X.toUpperCase()]),typeof X=="number"&&X>=0&&X<=B.levels.SILENT)return X;throw new TypeError("log.setLevel() called with invalid level: "+xe)}B.name=A,B.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},B.methodFactory=L||x,B.getLevel=function(){return P??F??K},B.setLevel=function(xe,X){return P=Je(xe),X!==!1&&te(P),m.call(B)},B.setDefaultLevel=function(xe){F=Je(xe),ye()||B.setLevel(xe,!1)},B.resetLevel=function(){P=null,Be(),m.call(B)},B.enableAll=function(xe){B.setLevel(B.levels.TRACE,xe)},B.disableAll=function(xe){B.setLevel(B.levels.SILENT,xe)},B.rebuild=function(){if(c!==B&&(K=Je(c.getLevel())),m.call(B),c===B)for(var xe in s)s[xe].rebuild()},K=Je(c?c.getLevel():"WARN");var Dt=ye();Dt!=null&&(P=Je(Dt)),m.call(B)}c=new D,c.getLogger=function(L){if(typeof L!="symbol"&&typeof L!="string"||L==="")throw new TypeError("You must supply a name when creating a logger.");var B=s[L];return B||(B=s[L]=new D(L,c.methodFactory)),B};var N=typeof window!==e?window.log:void 0;return c.noConflict=function(){return typeof window!==e&&window.log===c&&(window.log=N),c},c.getLoggers=function(){return s},c.default=c,c})});var Qo=I((Go,ta)=>{"use strict";(function(){var t,e="4.17.21",n=200,i="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",s="Expected a function",c="Invalid `variable` option passed into `_.template`",u="__lodash_hash_undefined__",d=500,f="__lodash_placeholder__",m=1,y=2,x=4,D=1,N=2,A=1,L=2,B=4,K=8,F=16,P=32,U=64,te=128,ye=256,Be=512,Je=30,Dt="...",xe=800,X=16,z=1,se=2,re=3,De=1/0,ge=9007199254740991,me=17976931348623157e292,St=NaN,We=4294967295,je=We-1,bn=We>>>1,yr=[["ary",te],["bind",A],["bindKey",L],["curry",K],["curryRight",F],["flip",Be],["partial",P],["partialRight",U],["rearg",ye]],Vn="[object Arguments]",ur="[object Array]",xi="[object AsyncFunction]",lr="[object Boolean]",dr="[object Date]",Ki="[object DOMException]",wr="[object Error]",Sr="[object Function]",xr="[object GeneratorFunction]",mt="[object Map]",fr="[object Number]",Ji="[object Null]",Bt="[object Object]",Pi="[object Promise]",zr="[object Proxy]",hr="[object RegExp]",xt="[object Set]",sn="[object String]",Pr="[object Symbol]",Xi="[object Undefined]",jt="[object WeakMap]",Ci="[object WeakSet]",Gn="[object ArrayBuffer]",_n="[object DataView]",Cr="[object Float32Array]",vn="[object Float64Array]",C="[object Int8Array]",H="[object Int16Array]",Z="[object Int32Array]",ie="[object Uint8Array]",Ue="[object Uint8ClampedArray]",Le="[object Uint16Array]",Ge="[object Uint32Array]",tt=/\b__p \+= '';/g,Fe=/\b(__p \+=) '' \+/g,Qe=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ne=/&(?:amp|lt|gt|quot|#39);/g,ct=/[&<>"']/g,Ft=RegExp(Ne.source),Zt=RegExp(ct.source),Yi=/<%-([\s\S]+?)%>/g,Sa=/<%([\s\S]+?)%>/g,ds=/<%=([\s\S]+?)%>/g,xa=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Pa=/^\w*$/,Vl=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,q=/[\\^$.*+?()[\]{}|]/g,p=RegExp(q.source),k=/^\s+/,R=/\s/,v=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,g=/\{\n\/\* \[wrapped with (.+)\] \*/,O=/,? & /,G=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,_e=/[()=,{}\[\]\/\s]/,ht=/\\(\\)?/g,Xe=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,an=/\w*$/,yn=/^[-+]0x[0-9a-f]+$/i,Qn=/^0b[01]+$/i,Ie=/^\[object .+?Constructor\]$/,Hr=/^0o[0-7]+$/i,Zi=/^(?:0|[1-9]\d*)$/,Ca=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ka=/($^)/,jT=/['\n\r\u2028\u2029\\]/g,Ta="\\ud800-\\udfff",zT="\\u0300-\\u036f",HT="\\ufe20-\\ufe2f",VT="\\u20d0-\\u20ff",Np=zT+HT+VT,qp="\\u2700-\\u27bf",Op="a-z\\xdf-\\xf6\\xf8-\\xff",GT="\\xac\\xb1\\xd7\\xf7",QT="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",KT="\\u2000-\\u206f",JT=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Lp="A-Z\\xc0-\\xd6\\xd8-\\xde",Bp="\\ufe0e\\ufe0f",Up=GT+QT+KT+JT,Gl="['\u2019]",XT="["+Ta+"]",$p="["+Up+"]",Ra="["+Np+"]",Wp="\\d+",YT="["+qp+"]",jp="["+Op+"]",zp="[^"+Ta+Up+Wp+qp+Op+Lp+"]",Ql="\\ud83c[\\udffb-\\udfff]",ZT="(?:"+Ra+"|"+Ql+")",Hp="[^"+Ta+"]",Kl="(?:\\ud83c[\\udde6-\\uddff]){2}",Jl="[\\ud800-\\udbff][\\udc00-\\udfff]",eo="["+Lp+"]",Vp="\\u200d",Gp="(?:"+jp+"|"+zp+")",eR="(?:"+eo+"|"+zp+")",Qp="(?:"+Gl+"(?:d|ll|m|re|s|t|ve))?",Kp="(?:"+Gl+"(?:D|LL|M|RE|S|T|VE))?",Jp=ZT+"?",Xp="["+Bp+"]?",tR="(?:"+Vp+"(?:"+[Hp,Kl,Jl].join("|")+")"+Xp+Jp+")*",nR="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",rR="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Yp=Xp+Jp+tR,iR="(?:"+[YT,Kl,Jl].join("|")+")"+Yp,oR="(?:"+[Hp+Ra+"?",Ra,Kl,Jl,XT].join("|")+")",sR=RegExp(Gl,"g"),aR=RegExp(Ra,"g"),Xl=RegExp(Ql+"(?="+Ql+")|"+oR+Yp,"g"),cR=RegExp([eo+"?"+jp+"+"+Qp+"(?="+[$p,eo,"$"].join("|")+")",eR+"+"+Kp+"(?="+[$p,eo+Gp,"$"].join("|")+")",eo+"?"+Gp+"+"+Qp,eo+"+"+Kp,rR,nR,Wp,iR].join("|"),"g"),uR=RegExp("["+Vp+Ta+Np+Bp+"]"),lR=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,dR=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],fR=-1,ut={};ut[Cr]=ut[vn]=ut[C]=ut[H]=ut[Z]=ut[ie]=ut[Ue]=ut[Le]=ut[Ge]=!0,ut[Vn]=ut[ur]=ut[Gn]=ut[lr]=ut[_n]=ut[dr]=ut[wr]=ut[Sr]=ut[mt]=ut[fr]=ut[Bt]=ut[hr]=ut[xt]=ut[sn]=ut[jt]=!1;var nt={};nt[Vn]=nt[ur]=nt[Gn]=nt[_n]=nt[lr]=nt[dr]=nt[Cr]=nt[vn]=nt[C]=nt[H]=nt[Z]=nt[mt]=nt[fr]=nt[Bt]=nt[hr]=nt[xt]=nt[sn]=nt[Pr]=nt[ie]=nt[Ue]=nt[Le]=nt[Ge]=!0,nt[wr]=nt[Sr]=nt[jt]=!1;var hR={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"A",\u00C5:"A",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"a",\u00E5:"a",\u00C7:"C",\u00E7:"c",\u00D0:"D",\u00F0:"d",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00D1:"N",\u00F1:"n",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"O",\u00D8:"O",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"o",\u00F8:"o",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"U",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"u",\u00DD:"Y",\u00FD:"y",\u00FF:"y",\u00C6:"Ae",\u00E6:"ae",\u00DE:"Th",\u00FE:"th",\u00DF:"ss",\u0100:"A",\u0102:"A",\u0104:"A",\u0101:"a",\u0103:"a",\u0105:"a",\u0106:"C",\u0108:"C",\u010A:"C",\u010C:"C",\u0107:"c",\u0109:"c",\u010B:"c",\u010D:"c",\u010E:"D",\u0110:"D",\u010F:"d",\u0111:"d",\u0112:"E",\u0114:"E",\u0116:"E",\u0118:"E",\u011A:"E",\u0113:"e",\u0115:"e",\u0117:"e",\u0119:"e",\u011B:"e",\u011C:"G",\u011E:"G",\u0120:"G",\u0122:"G",\u011D:"g",\u011F:"g",\u0121:"g",\u0123:"g",\u0124:"H",\u0126:"H",\u0125:"h",\u0127:"h",\u0128:"I",\u012A:"I",\u012C:"I",\u012E:"I",\u0130:"I",\u0129:"i",\u012B:"i",\u012D:"i",\u012F:"i",\u0131:"i",\u0134:"J",\u0135:"j",\u0136:"K",\u0137:"k",\u0138:"k",\u0139:"L",\u013B:"L",\u013D:"L",\u013F:"L",\u0141:"L",\u013A:"l",\u013C:"l",\u013E:"l",\u0140:"l",\u0142:"l",\u0143:"N",\u0145:"N",\u0147:"N",\u014A:"N",\u0144:"n",\u0146:"n",\u0148:"n",\u014B:"n",\u014C:"O",\u014E:"O",\u0150:"O",\u014D:"o",\u014F:"o",\u0151:"o",\u0154:"R",\u0156:"R",\u0158:"R",\u0155:"r",\u0157:"r",\u0159:"r",\u015A:"S",\u015C:"S",\u015E:"S",\u0160:"S",\u015B:"s",\u015D:"s",\u015F:"s",\u0161:"s",\u0162:"T",\u0164:"T",\u0166:"T",\u0163:"t",\u0165:"t",\u0167:"t",\u0168:"U",\u016A:"U",\u016C:"U",\u016E:"U",\u0170:"U",\u0172:"U",\u0169:"u",\u016B:"u",\u016D:"u",\u016F:"u",\u0171:"u",\u0173:"u",\u0174:"W",\u0175:"w",\u0176:"Y",\u0177:"y",\u0178:"Y",\u0179:"Z",\u017B:"Z",\u017D:"Z",\u017A:"z",\u017C:"z",\u017E:"z",\u0132:"IJ",\u0133:"ij",\u0152:"Oe",\u0153:"oe",\u0149:"'n",\u017F:"s"},gR={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},pR={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},mR={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},bR=parseFloat,_R=parseInt,Zp=typeof global=="object"&&global&&global.Object===Object&&global,vR=typeof self=="object"&&self&&self.Object===Object&&self,qt=Zp||vR||Function("return this")(),Yl=typeof Go=="object"&&Go&&!Go.nodeType&&Go,ki=Yl&&typeof ta=="object"&&ta&&!ta.nodeType&&ta,em=ki&&ki.exports===Yl,Zl=em&&Zp.process,En=function(){try{var E=ki&&ki.require&&ki.require("util").types;return E||Zl&&Zl.binding&&Zl.binding("util")}catch{}}(),tm=En&&En.isArrayBuffer,nm=En&&En.isDate,rm=En&&En.isMap,im=En&&En.isRegExp,om=En&&En.isSet,sm=En&&En.isTypedArray;function wn(E,W,$){switch($.length){case 0:return E.call(W);case 1:return E.call(W,$[0]);case 2:return E.call(W,$[0],$[1]);case 3:return E.call(W,$[0],$[1],$[2])}return E.apply(W,$)}function yR(E,W,$,oe){for(var we=-1,ze=E==null?0:E.length;++we<ze;){var Mt=E[we];W(oe,Mt,$(Mt),E)}return oe}function In(E,W){for(var $=-1,oe=E==null?0:E.length;++$<oe&&W(E[$],$,E)!==!1;);return E}function wR(E,W){for(var $=E==null?0:E.length;$--&&W(E[$],$,E)!==!1;);return E}function am(E,W){for(var $=-1,oe=E==null?0:E.length;++$<oe;)if(!W(E[$],$,E))return!1;return!0}function Vr(E,W){for(var $=-1,oe=E==null?0:E.length,we=0,ze=[];++$<oe;){var Mt=E[$];W(Mt,$,E)&&(ze[we++]=Mt)}return ze}function Da(E,W){var $=E==null?0:E.length;return!!$&&to(E,W,0)>-1}function ed(E,W,$){for(var oe=-1,we=E==null?0:E.length;++oe<we;)if($(W,E[oe]))return!0;return!1}function gt(E,W){for(var $=-1,oe=E==null?0:E.length,we=Array(oe);++$<oe;)we[$]=W(E[$],$,E);return we}function Gr(E,W){for(var $=-1,oe=W.length,we=E.length;++$<oe;)E[we+$]=W[$];return E}function td(E,W,$,oe){var we=-1,ze=E==null?0:E.length;for(oe&&ze&&($=E[++we]);++we<ze;)$=W($,E[we],we,E);return $}function SR(E,W,$,oe){var we=E==null?0:E.length;for(oe&&we&&($=E[--we]);we--;)$=W($,E[we],we,E);return $}function nd(E,W){for(var $=-1,oe=E==null?0:E.length;++$<oe;)if(W(E[$],$,E))return!0;return!1}var xR=rd("length");function PR(E){return E.split("")}function CR(E){return E.match(G)||[]}function cm(E,W,$){var oe;return $(E,function(we,ze,Mt){if(W(we,ze,Mt))return oe=ze,!1}),oe}function Fa(E,W,$,oe){for(var we=E.length,ze=$+(oe?1:-1);oe?ze--:++ze<we;)if(W(E[ze],ze,E))return ze;return-1}function to(E,W,$){return W===W?OR(E,W,$):Fa(E,um,$)}function kR(E,W,$,oe){for(var we=$-1,ze=E.length;++we<ze;)if(oe(E[we],W))return we;return-1}function um(E){return E!==E}function lm(E,W){var $=E==null?0:E.length;return $?od(E,W)/$:St}function rd(E){return function(W){return W==null?t:W[E]}}function id(E){return function(W){return E==null?t:E[W]}}function dm(E,W,$,oe,we){return we(E,function(ze,Mt,Ye){$=oe?(oe=!1,ze):W($,ze,Mt,Ye)}),$}function TR(E,W){var $=E.length;for(E.sort(W);$--;)E[$]=E[$].value;return E}function od(E,W){for(var $,oe=-1,we=E.length;++oe<we;){var ze=W(E[oe]);ze!==t&&($=$===t?ze:$+ze)}return $}function sd(E,W){for(var $=-1,oe=Array(E);++$<E;)oe[$]=W($);return oe}function RR(E,W){return gt(W,function($){return[$,E[$]]})}function fm(E){return E&&E.slice(0,mm(E)+1).replace(k,"")}function Sn(E){return function(W){return E(W)}}function ad(E,W){return gt(W,function($){return E[$]})}function fs(E,W){return E.has(W)}function hm(E,W){for(var $=-1,oe=E.length;++$<oe&&to(W,E[$],0)>-1;);return $}function gm(E,W){for(var $=E.length;$--&&to(W,E[$],0)>-1;);return $}function DR(E,W){for(var $=E.length,oe=0;$--;)E[$]===W&&++oe;return oe}var FR=id(hR),ER=id(gR);function IR(E){return"\\"+mR[E]}function MR(E,W){return E==null?t:E[W]}function no(E){return uR.test(E)}function AR(E){return lR.test(E)}function NR(E){for(var W,$=[];!(W=E.next()).done;)$.push(W.value);return $}function cd(E){var W=-1,$=Array(E.size);return E.forEach(function(oe,we){$[++W]=[we,oe]}),$}function pm(E,W){return function($){return E(W($))}}function Qr(E,W){for(var $=-1,oe=E.length,we=0,ze=[];++$<oe;){var Mt=E[$];(Mt===W||Mt===f)&&(E[$]=f,ze[we++]=$)}return ze}function Ea(E){var W=-1,$=Array(E.size);return E.forEach(function(oe){$[++W]=oe}),$}function qR(E){var W=-1,$=Array(E.size);return E.forEach(function(oe){$[++W]=[oe,oe]}),$}function OR(E,W,$){for(var oe=$-1,we=E.length;++oe<we;)if(E[oe]===W)return oe;return-1}function LR(E,W,$){for(var oe=$+1;oe--;)if(E[oe]===W)return oe;return oe}function ro(E){return no(E)?UR(E):xR(E)}function Kn(E){return no(E)?$R(E):PR(E)}function mm(E){for(var W=E.length;W--&&R.test(E.charAt(W)););return W}var BR=id(pR);function UR(E){for(var W=Xl.lastIndex=0;Xl.test(E);)++W;return W}function $R(E){return E.match(Xl)||[]}function WR(E){return E.match(cR)||[]}var jR=function E(W){W=W==null?qt:Kr.defaults(qt.Object(),W,Kr.pick(qt,dR));var $=W.Array,oe=W.Date,we=W.Error,ze=W.Function,Mt=W.Math,Ye=W.Object,ud=W.RegExp,zR=W.String,Mn=W.TypeError,Ia=$.prototype,HR=ze.prototype,io=Ye.prototype,Ma=W["__core-js_shared__"],Aa=HR.toString,Ke=io.hasOwnProperty,VR=0,bm=function(){var r=/[^.]+$/.exec(Ma&&Ma.keys&&Ma.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),Na=io.toString,GR=Aa.call(Ye),QR=qt._,KR=ud("^"+Aa.call(Ke).replace(q,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),qa=em?W.Buffer:t,Jr=W.Symbol,Oa=W.Uint8Array,_m=qa?qa.allocUnsafe:t,La=pm(Ye.getPrototypeOf,Ye),vm=Ye.create,ym=io.propertyIsEnumerable,Ba=Ia.splice,wm=Jr?Jr.isConcatSpreadable:t,hs=Jr?Jr.iterator:t,Ti=Jr?Jr.toStringTag:t,Ua=function(){try{var r=Ii(Ye,"defineProperty");return r({},"",{}),r}catch{}}(),JR=W.clearTimeout!==qt.clearTimeout&&W.clearTimeout,XR=oe&&oe.now!==qt.Date.now&&oe.now,YR=W.setTimeout!==qt.setTimeout&&W.setTimeout,$a=Mt.ceil,Wa=Mt.floor,ld=Ye.getOwnPropertySymbols,ZR=qa?qa.isBuffer:t,Sm=W.isFinite,eD=Ia.join,tD=pm(Ye.keys,Ye),At=Mt.max,zt=Mt.min,nD=oe.now,rD=W.parseInt,xm=Mt.random,iD=Ia.reverse,dd=Ii(W,"DataView"),gs=Ii(W,"Map"),fd=Ii(W,"Promise"),oo=Ii(W,"Set"),ps=Ii(W,"WeakMap"),ms=Ii(Ye,"create"),ja=ps&&new ps,so={},oD=Mi(dd),sD=Mi(gs),aD=Mi(fd),cD=Mi(oo),uD=Mi(ps),za=Jr?Jr.prototype:t,bs=za?za.valueOf:t,Pm=za?za.toString:t;function b(r){if(yt(r)&&!Pe(r)&&!(r instanceof Ae)){if(r instanceof An)return r;if(Ke.call(r,"__wrapped__"))return Cb(r)}return new An(r)}var ao=function(){function r(){}return function(o){if(!bt(o))return{};if(vm)return vm(o);r.prototype=o;var a=new r;return r.prototype=t,a}}();function Ha(){}function An(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=t}b.templateSettings={escape:Yi,evaluate:Sa,interpolate:ds,variable:"",imports:{_:b}},b.prototype=Ha.prototype,b.prototype.constructor=b,An.prototype=ao(Ha.prototype),An.prototype.constructor=An;function Ae(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=We,this.__views__=[]}function lD(){var r=new Ae(this.__wrapped__);return r.__actions__=cn(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=cn(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=cn(this.__views__),r}function dD(){if(this.__filtered__){var r=new Ae(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function fD(){var r=this.__wrapped__.value(),o=this.__dir__,a=Pe(r),l=o<0,h=a?r.length:0,_=P0(0,h,this.__views__),S=_.start,T=_.end,M=T-S,V=l?T:S-1,Q=this.__iteratees__,J=Q.length,ne=0,ae=zt(M,this.__takeCount__);if(!a||!l&&h==M&&ae==M)return Qm(r,this.__actions__);var de=[];e:for(;M--&&ne<ae;){V+=o;for(var Re=-1,fe=r[V];++Re<J;){var Me=Q[Re],qe=Me.iteratee,Cn=Me.type,nn=qe(fe);if(Cn==se)fe=nn;else if(!nn){if(Cn==z)continue e;break e}}de[ne++]=fe}return de}Ae.prototype=ao(Ha.prototype),Ae.prototype.constructor=Ae;function Ri(r){var o=-1,a=r==null?0:r.length;for(this.clear();++o<a;){var l=r[o];this.set(l[0],l[1])}}function hD(){this.__data__=ms?ms(null):{},this.size=0}function gD(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function pD(r){var o=this.__data__;if(ms){var a=o[r];return a===u?t:a}return Ke.call(o,r)?o[r]:t}function mD(r){var o=this.__data__;return ms?o[r]!==t:Ke.call(o,r)}function bD(r,o){var a=this.__data__;return this.size+=this.has(r)?0:1,a[r]=ms&&o===t?u:o,this}Ri.prototype.clear=hD,Ri.prototype.delete=gD,Ri.prototype.get=pD,Ri.prototype.has=mD,Ri.prototype.set=bD;function kr(r){var o=-1,a=r==null?0:r.length;for(this.clear();++o<a;){var l=r[o];this.set(l[0],l[1])}}function _D(){this.__data__=[],this.size=0}function vD(r){var o=this.__data__,a=Va(o,r);if(a<0)return!1;var l=o.length-1;return a==l?o.pop():Ba.call(o,a,1),--this.size,!0}function yD(r){var o=this.__data__,a=Va(o,r);return a<0?t:o[a][1]}function wD(r){return Va(this.__data__,r)>-1}function SD(r,o){var a=this.__data__,l=Va(a,r);return l<0?(++this.size,a.push([r,o])):a[l][1]=o,this}kr.prototype.clear=_D,kr.prototype.delete=vD,kr.prototype.get=yD,kr.prototype.has=wD,kr.prototype.set=SD;function Tr(r){var o=-1,a=r==null?0:r.length;for(this.clear();++o<a;){var l=r[o];this.set(l[0],l[1])}}function xD(){this.size=0,this.__data__={hash:new Ri,map:new(gs||kr),string:new Ri}}function PD(r){var o=ic(this,r).delete(r);return this.size-=o?1:0,o}function CD(r){return ic(this,r).get(r)}function kD(r){return ic(this,r).has(r)}function TD(r,o){var a=ic(this,r),l=a.size;return a.set(r,o),this.size+=a.size==l?0:1,this}Tr.prototype.clear=xD,Tr.prototype.delete=PD,Tr.prototype.get=CD,Tr.prototype.has=kD,Tr.prototype.set=TD;function Di(r){var o=-1,a=r==null?0:r.length;for(this.__data__=new Tr;++o<a;)this.add(r[o])}function RD(r){return this.__data__.set(r,u),this}function DD(r){return this.__data__.has(r)}Di.prototype.add=Di.prototype.push=RD,Di.prototype.has=DD;function Jn(r){var o=this.__data__=new kr(r);this.size=o.size}function FD(){this.__data__=new kr,this.size=0}function ED(r){var o=this.__data__,a=o.delete(r);return this.size=o.size,a}function ID(r){return this.__data__.get(r)}function MD(r){return this.__data__.has(r)}function AD(r,o){var a=this.__data__;if(a instanceof kr){var l=a.__data__;if(!gs||l.length<n-1)return l.push([r,o]),this.size=++a.size,this;a=this.__data__=new Tr(l)}return a.set(r,o),this.size=a.size,this}Jn.prototype.clear=FD,Jn.prototype.delete=ED,Jn.prototype.get=ID,Jn.prototype.has=MD,Jn.prototype.set=AD;function Cm(r,o){var a=Pe(r),l=!a&&Ai(r),h=!a&&!l&&ti(r),_=!a&&!l&&!h&&fo(r),S=a||l||h||_,T=S?sd(r.length,zR):[],M=T.length;for(var V in r)(o||Ke.call(r,V))&&!(S&&(V=="length"||h&&(V=="offset"||V=="parent")||_&&(V=="buffer"||V=="byteLength"||V=="byteOffset")||Er(V,M)))&&T.push(V);return T}function km(r){var o=r.length;return o?r[xd(0,o-1)]:t}function ND(r,o){return oc(cn(r),Fi(o,0,r.length))}function qD(r){return oc(cn(r))}function hd(r,o,a){(a!==t&&!Xn(r[o],a)||a===t&&!(o in r))&&Rr(r,o,a)}function _s(r,o,a){var l=r[o];(!(Ke.call(r,o)&&Xn(l,a))||a===t&&!(o in r))&&Rr(r,o,a)}function Va(r,o){for(var a=r.length;a--;)if(Xn(r[a][0],o))return a;return-1}function OD(r,o,a,l){return Xr(r,function(h,_,S){o(l,h,a(h),S)}),l}function Tm(r,o){return r&&pr(o,Ot(o),r)}function LD(r,o){return r&&pr(o,ln(o),r)}function Rr(r,o,a){o=="__proto__"&&Ua?Ua(r,o,{configurable:!0,enumerable:!0,value:a,writable:!0}):r[o]=a}function gd(r,o){for(var a=-1,l=o.length,h=$(l),_=r==null;++a<l;)h[a]=_?t:Qd(r,o[a]);return h}function Fi(r,o,a){return r===r&&(a!==t&&(r=r<=a?r:a),o!==t&&(r=r>=o?r:o)),r}function Nn(r,o,a,l,h,_){var S,T=o&m,M=o&y,V=o&x;if(a&&(S=h?a(r,l,h,_):a(r)),S!==t)return S;if(!bt(r))return r;var Q=Pe(r);if(Q){if(S=k0(r),!T)return cn(r,S)}else{var J=Ht(r),ne=J==Sr||J==xr;if(ti(r))return Xm(r,T);if(J==Bt||J==Vn||ne&&!h){if(S=M||ne?{}:mb(r),!T)return M?p0(r,LD(S,r)):g0(r,Tm(S,r))}else{if(!nt[J])return h?r:{};S=T0(r,J,T)}}_||(_=new Jn);var ae=_.get(r);if(ae)return ae;_.set(r,S),Hb(r)?r.forEach(function(fe){S.add(Nn(fe,o,a,fe,r,_))}):jb(r)&&r.forEach(function(fe,Me){S.set(Me,Nn(fe,o,a,Me,r,_))});var de=V?M?Ad:Md:M?ln:Ot,Re=Q?t:de(r);return In(Re||r,function(fe,Me){Re&&(Me=fe,fe=r[Me]),_s(S,Me,Nn(fe,o,a,Me,r,_))}),S}function BD(r){var o=Ot(r);return function(a){return Rm(a,r,o)}}function Rm(r,o,a){var l=a.length;if(r==null)return!l;for(r=Ye(r);l--;){var h=a[l],_=o[h],S=r[h];if(S===t&&!(h in r)||!_(S))return!1}return!0}function Dm(r,o,a){if(typeof r!="function")throw new Mn(s);return Cs(function(){r.apply(t,a)},o)}function vs(r,o,a,l){var h=-1,_=Da,S=!0,T=r.length,M=[],V=o.length;if(!T)return M;a&&(o=gt(o,Sn(a))),l?(_=ed,S=!1):o.length>=n&&(_=fs,S=!1,o=new Di(o));e:for(;++h<T;){var Q=r[h],J=a==null?Q:a(Q);if(Q=l||Q!==0?Q:0,S&&J===J){for(var ne=V;ne--;)if(o[ne]===J)continue e;M.push(Q)}else _(o,J,l)||M.push(Q)}return M}var Xr=nb(gr),Fm=nb(md,!0);function UD(r,o){var a=!0;return Xr(r,function(l,h,_){return a=!!o(l,h,_),a}),a}function Ga(r,o,a){for(var l=-1,h=r.length;++l<h;){var _=r[l],S=o(_);if(S!=null&&(T===t?S===S&&!Pn(S):a(S,T)))var T=S,M=_}return M}function $D(r,o,a,l){var h=r.length;for(a=Te(a),a<0&&(a=-a>h?0:h+a),l=l===t||l>h?h:Te(l),l<0&&(l+=h),l=a>l?0:Gb(l);a<l;)r[a++]=o;return r}function Em(r,o){var a=[];return Xr(r,function(l,h,_){o(l,h,_)&&a.push(l)}),a}function Ut(r,o,a,l,h){var _=-1,S=r.length;for(a||(a=D0),h||(h=[]);++_<S;){var T=r[_];o>0&&a(T)?o>1?Ut(T,o-1,a,l,h):Gr(h,T):l||(h[h.length]=T)}return h}var pd=rb(),Im=rb(!0);function gr(r,o){return r&&pd(r,o,Ot)}function md(r,o){return r&&Im(r,o,Ot)}function Qa(r,o){return Vr(o,function(a){return Ir(r[a])})}function Ei(r,o){o=Zr(o,r);for(var a=0,l=o.length;r!=null&&a<l;)r=r[mr(o[a++])];return a&&a==l?r:t}function Mm(r,o,a){var l=o(r);return Pe(r)?l:Gr(l,a(r))}function en(r){return r==null?r===t?Xi:Ji:Ti&&Ti in Ye(r)?x0(r):q0(r)}function bd(r,o){return r>o}function WD(r,o){return r!=null&&Ke.call(r,o)}function jD(r,o){return r!=null&&o in Ye(r)}function zD(r,o,a){return r>=zt(o,a)&&r<At(o,a)}function _d(r,o,a){for(var l=a?ed:Da,h=r[0].length,_=r.length,S=_,T=$(_),M=1/0,V=[];S--;){var Q=r[S];S&&o&&(Q=gt(Q,Sn(o))),M=zt(Q.length,M),T[S]=!a&&(o||h>=120&&Q.length>=120)?new Di(S&&Q):t}Q=r[0];var J=-1,ne=T[0];e:for(;++J<h&&V.length<M;){var ae=Q[J],de=o?o(ae):ae;if(ae=a||ae!==0?ae:0,!(ne?fs(ne,de):l(V,de,a))){for(S=_;--S;){var Re=T[S];if(!(Re?fs(Re,de):l(r[S],de,a)))continue e}ne&&ne.push(de),V.push(ae)}}return V}function HD(r,o,a,l){return gr(r,function(h,_,S){o(l,a(h),_,S)}),l}function ys(r,o,a){o=Zr(o,r),r=yb(r,o);var l=r==null?r:r[mr(On(o))];return l==null?t:wn(l,r,a)}function Am(r){return yt(r)&&en(r)==Vn}function VD(r){return yt(r)&&en(r)==Gn}function GD(r){return yt(r)&&en(r)==dr}function ws(r,o,a,l,h){return r===o?!0:r==null||o==null||!yt(r)&&!yt(o)?r!==r&&o!==o:QD(r,o,a,l,ws,h)}function QD(r,o,a,l,h,_){var S=Pe(r),T=Pe(o),M=S?ur:Ht(r),V=T?ur:Ht(o);M=M==Vn?Bt:M,V=V==Vn?Bt:V;var Q=M==Bt,J=V==Bt,ne=M==V;if(ne&&ti(r)){if(!ti(o))return!1;S=!0,Q=!1}if(ne&&!Q)return _||(_=new Jn),S||fo(r)?hb(r,o,a,l,h,_):w0(r,o,M,a,l,h,_);if(!(a&D)){var ae=Q&&Ke.call(r,"__wrapped__"),de=J&&Ke.call(o,"__wrapped__");if(ae||de){var Re=ae?r.value():r,fe=de?o.value():o;return _||(_=new Jn),h(Re,fe,a,l,_)}}return ne?(_||(_=new Jn),S0(r,o,a,l,h,_)):!1}function KD(r){return yt(r)&&Ht(r)==mt}function vd(r,o,a,l){var h=a.length,_=h,S=!l;if(r==null)return!_;for(r=Ye(r);h--;){var T=a[h];if(S&&T[2]?T[1]!==r[T[0]]:!(T[0]in r))return!1}for(;++h<_;){T=a[h];var M=T[0],V=r[M],Q=T[1];if(S&&T[2]){if(V===t&&!(M in r))return!1}else{var J=new Jn;if(l)var ne=l(V,Q,M,r,o,J);if(!(ne===t?ws(Q,V,D|N,l,J):ne))return!1}}return!0}function Nm(r){if(!bt(r)||E0(r))return!1;var o=Ir(r)?KR:Ie;return o.test(Mi(r))}function JD(r){return yt(r)&&en(r)==hr}function XD(r){return yt(r)&&Ht(r)==xt}function YD(r){return yt(r)&&dc(r.length)&&!!ut[en(r)]}function qm(r){return typeof r=="function"?r:r==null?dn:typeof r=="object"?Pe(r)?Bm(r[0],r[1]):Lm(r):i_(r)}function yd(r){if(!Ps(r))return tD(r);var o=[];for(var a in Ye(r))Ke.call(r,a)&&a!="constructor"&&o.push(a);return o}function ZD(r){if(!bt(r))return N0(r);var o=Ps(r),a=[];for(var l in r)l=="constructor"&&(o||!Ke.call(r,l))||a.push(l);return a}function wd(r,o){return r<o}function Om(r,o){var a=-1,l=un(r)?$(r.length):[];return Xr(r,function(h,_,S){l[++a]=o(h,_,S)}),l}function Lm(r){var o=qd(r);return o.length==1&&o[0][2]?_b(o[0][0],o[0][1]):function(a){return a===r||vd(a,r,o)}}function Bm(r,o){return Ld(r)&&bb(o)?_b(mr(r),o):function(a){var l=Qd(a,r);return l===t&&l===o?Kd(a,r):ws(o,l,D|N)}}function Ka(r,o,a,l,h){r!==o&&pd(o,function(_,S){if(h||(h=new Jn),bt(_))e0(r,o,S,a,Ka,l,h);else{var T=l?l(Ud(r,S),_,S+"",r,o,h):t;T===t&&(T=_),hd(r,S,T)}},ln)}function e0(r,o,a,l,h,_,S){var T=Ud(r,a),M=Ud(o,a),V=S.get(M);if(V){hd(r,a,V);return}var Q=_?_(T,M,a+"",r,o,S):t,J=Q===t;if(J){var ne=Pe(M),ae=!ne&&ti(M),de=!ne&&!ae&&fo(M);Q=M,ne||ae||de?Pe(T)?Q=T:Pt(T)?Q=cn(T):ae?(J=!1,Q=Xm(M,!0)):de?(J=!1,Q=Ym(M,!0)):Q=[]:ks(M)||Ai(M)?(Q=T,Ai(T)?Q=Qb(T):(!bt(T)||Ir(T))&&(Q=mb(M))):J=!1}J&&(S.set(M,Q),h(Q,M,l,_,S),S.delete(M)),hd(r,a,Q)}function Um(r,o){var a=r.length;if(a)return o+=o<0?a:0,Er(o,a)?r[o]:t}function $m(r,o,a){o.length?o=gt(o,function(_){return Pe(_)?function(S){return Ei(S,_.length===1?_[0]:_)}:_}):o=[dn];var l=-1;o=gt(o,Sn(le()));var h=Om(r,function(_,S,T){var M=gt(o,function(V){return V(_)});return{criteria:M,index:++l,value:_}});return TR(h,function(_,S){return h0(_,S,a)})}function t0(r,o){return Wm(r,o,function(a,l){return Kd(r,l)})}function Wm(r,o,a){for(var l=-1,h=o.length,_={};++l<h;){var S=o[l],T=Ei(r,S);a(T,S)&&Ss(_,Zr(S,r),T)}return _}function n0(r){return function(o){return Ei(o,r)}}function Sd(r,o,a,l){var h=l?kR:to,_=-1,S=o.length,T=r;for(r===o&&(o=cn(o)),a&&(T=gt(r,Sn(a)));++_<S;)for(var M=0,V=o[_],Q=a?a(V):V;(M=h(T,Q,M,l))>-1;)T!==r&&Ba.call(T,M,1),Ba.call(r,M,1);return r}function jm(r,o){for(var a=r?o.length:0,l=a-1;a--;){var h=o[a];if(a==l||h!==_){var _=h;Er(h)?Ba.call(r,h,1):kd(r,h)}}return r}function xd(r,o){return r+Wa(xm()*(o-r+1))}function r0(r,o,a,l){for(var h=-1,_=At($a((o-r)/(a||1)),0),S=$(_);_--;)S[l?_:++h]=r,r+=a;return S}function Pd(r,o){var a="";if(!r||o<1||o>ge)return a;do o%2&&(a+=r),o=Wa(o/2),o&&(r+=r);while(o);return a}function Ee(r,o){return $d(vb(r,o,dn),r+"")}function i0(r){return km(ho(r))}function o0(r,o){var a=ho(r);return oc(a,Fi(o,0,a.length))}function Ss(r,o,a,l){if(!bt(r))return r;o=Zr(o,r);for(var h=-1,_=o.length,S=_-1,T=r;T!=null&&++h<_;){var M=mr(o[h]),V=a;if(M==="__proto__"||M==="constructor"||M==="prototype")return r;if(h!=S){var Q=T[M];V=l?l(Q,M,T):t,V===t&&(V=bt(Q)?Q:Er(o[h+1])?[]:{})}_s(T,M,V),T=T[M]}return r}var zm=ja?function(r,o){return ja.set(r,o),r}:dn,s0=Ua?function(r,o){return Ua(r,"toString",{configurable:!0,enumerable:!1,value:Xd(o),writable:!0})}:dn;function a0(r){return oc(ho(r))}function qn(r,o,a){var l=-1,h=r.length;o<0&&(o=-o>h?0:h+o),a=a>h?h:a,a<0&&(a+=h),h=o>a?0:a-o>>>0,o>>>=0;for(var _=$(h);++l<h;)_[l]=r[l+o];return _}function c0(r,o){var a;return Xr(r,function(l,h,_){return a=o(l,h,_),!a}),!!a}function Ja(r,o,a){var l=0,h=r==null?l:r.length;if(typeof o=="number"&&o===o&&h<=bn){for(;l<h;){var _=l+h>>>1,S=r[_];S!==null&&!Pn(S)&&(a?S<=o:S<o)?l=_+1:h=_}return h}return Cd(r,o,dn,a)}function Cd(r,o,a,l){var h=0,_=r==null?0:r.length;if(_===0)return 0;o=a(o);for(var S=o!==o,T=o===null,M=Pn(o),V=o===t;h<_;){var Q=Wa((h+_)/2),J=a(r[Q]),ne=J!==t,ae=J===null,de=J===J,Re=Pn(J);if(S)var fe=l||de;else V?fe=de&&(l||ne):T?fe=de&&ne&&(l||!ae):M?fe=de&&ne&&!ae&&(l||!Re):ae||Re?fe=!1:fe=l?J<=o:J<o;fe?h=Q+1:_=Q}return zt(_,je)}function Hm(r,o){for(var a=-1,l=r.length,h=0,_=[];++a<l;){var S=r[a],T=o?o(S):S;if(!a||!Xn(T,M)){var M=T;_[h++]=S===0?0:S}}return _}function Vm(r){return typeof r=="number"?r:Pn(r)?St:+r}function xn(r){if(typeof r=="string")return r;if(Pe(r))return gt(r,xn)+"";if(Pn(r))return Pm?Pm.call(r):"";var o=r+"";return o=="0"&&1/r==-De?"-0":o}function Yr(r,o,a){var l=-1,h=Da,_=r.length,S=!0,T=[],M=T;if(a)S=!1,h=ed;else if(_>=n){var V=o?null:v0(r);if(V)return Ea(V);S=!1,h=fs,M=new Di}else M=o?[]:T;e:for(;++l<_;){var Q=r[l],J=o?o(Q):Q;if(Q=a||Q!==0?Q:0,S&&J===J){for(var ne=M.length;ne--;)if(M[ne]===J)continue e;o&&M.push(J),T.push(Q)}else h(M,J,a)||(M!==T&&M.push(J),T.push(Q))}return T}function kd(r,o){return o=Zr(o,r),r=yb(r,o),r==null||delete r[mr(On(o))]}function Gm(r,o,a,l){return Ss(r,o,a(Ei(r,o)),l)}function Xa(r,o,a,l){for(var h=r.length,_=l?h:-1;(l?_--:++_<h)&&o(r[_],_,r););return a?qn(r,l?0:_,l?_+1:h):qn(r,l?_+1:0,l?h:_)}function Qm(r,o){var a=r;return a instanceof Ae&&(a=a.value()),td(o,function(l,h){return h.func.apply(h.thisArg,Gr([l],h.args))},a)}function Td(r,o,a){var l=r.length;if(l<2)return l?Yr(r[0]):[];for(var h=-1,_=$(l);++h<l;)for(var S=r[h],T=-1;++T<l;)T!=h&&(_[h]=vs(_[h]||S,r[T],o,a));return Yr(Ut(_,1),o,a)}function Km(r,o,a){for(var l=-1,h=r.length,_=o.length,S={};++l<h;){var T=l<_?o[l]:t;a(S,r[l],T)}return S}function Rd(r){return Pt(r)?r:[]}function Dd(r){return typeof r=="function"?r:dn}function Zr(r,o){return Pe(r)?r:Ld(r,o)?[r]:Pb(Ve(r))}var u0=Ee;function ei(r,o,a){var l=r.length;return a=a===t?l:a,!o&&a>=l?r:qn(r,o,a)}var Jm=JR||function(r){return qt.clearTimeout(r)};function Xm(r,o){if(o)return r.slice();var a=r.length,l=_m?_m(a):new r.constructor(a);return r.copy(l),l}function Fd(r){var o=new r.constructor(r.byteLength);return new Oa(o).set(new Oa(r)),o}function l0(r,o){var a=o?Fd(r.buffer):r.buffer;return new r.constructor(a,r.byteOffset,r.byteLength)}function d0(r){var o=new r.constructor(r.source,an.exec(r));return o.lastIndex=r.lastIndex,o}function f0(r){return bs?Ye(bs.call(r)):{}}function Ym(r,o){var a=o?Fd(r.buffer):r.buffer;return new r.constructor(a,r.byteOffset,r.length)}function Zm(r,o){if(r!==o){var a=r!==t,l=r===null,h=r===r,_=Pn(r),S=o!==t,T=o===null,M=o===o,V=Pn(o);if(!T&&!V&&!_&&r>o||_&&S&&M&&!T&&!V||l&&S&&M||!a&&M||!h)return 1;if(!l&&!_&&!V&&r<o||V&&a&&h&&!l&&!_||T&&a&&h||!S&&h||!M)return-1}return 0}function h0(r,o,a){for(var l=-1,h=r.criteria,_=o.criteria,S=h.length,T=a.length;++l<S;){var M=Zm(h[l],_[l]);if(M){if(l>=T)return M;var V=a[l];return M*(V=="desc"?-1:1)}}return r.index-o.index}function eb(r,o,a,l){for(var h=-1,_=r.length,S=a.length,T=-1,M=o.length,V=At(_-S,0),Q=$(M+V),J=!l;++T<M;)Q[T]=o[T];for(;++h<S;)(J||h<_)&&(Q[a[h]]=r[h]);for(;V--;)Q[T++]=r[h++];return Q}function tb(r,o,a,l){for(var h=-1,_=r.length,S=-1,T=a.length,M=-1,V=o.length,Q=At(_-T,0),J=$(Q+V),ne=!l;++h<Q;)J[h]=r[h];for(var ae=h;++M<V;)J[ae+M]=o[M];for(;++S<T;)(ne||h<_)&&(J[ae+a[S]]=r[h++]);return J}function cn(r,o){var a=-1,l=r.length;for(o||(o=$(l));++a<l;)o[a]=r[a];return o}function pr(r,o,a,l){var h=!a;a||(a={});for(var _=-1,S=o.length;++_<S;){var T=o[_],M=l?l(a[T],r[T],T,a,r):t;M===t&&(M=r[T]),h?Rr(a,T,M):_s(a,T,M)}return a}function g0(r,o){return pr(r,Od(r),o)}function p0(r,o){return pr(r,gb(r),o)}function Ya(r,o){return function(a,l){var h=Pe(a)?yR:OD,_=o?o():{};return h(a,r,le(l,2),_)}}function co(r){return Ee(function(o,a){var l=-1,h=a.length,_=h>1?a[h-1]:t,S=h>2?a[2]:t;for(_=r.length>3&&typeof _=="function"?(h--,_):t,S&&tn(a[0],a[1],S)&&(_=h<3?t:_,h=1),o=Ye(o);++l<h;){var T=a[l];T&&r(o,T,l,_)}return o})}function nb(r,o){return function(a,l){if(a==null)return a;if(!un(a))return r(a,l);for(var h=a.length,_=o?h:-1,S=Ye(a);(o?_--:++_<h)&&l(S[_],_,S)!==!1;);return a}}function rb(r){return function(o,a,l){for(var h=-1,_=Ye(o),S=l(o),T=S.length;T--;){var M=S[r?T:++h];if(a(_[M],M,_)===!1)break}return o}}function m0(r,o,a){var l=o&A,h=xs(r);function _(){var S=this&&this!==qt&&this instanceof _?h:r;return S.apply(l?a:this,arguments)}return _}function ib(r){return function(o){o=Ve(o);var a=no(o)?Kn(o):t,l=a?a[0]:o.charAt(0),h=a?ei(a,1).join(""):o.slice(1);return l[r]()+h}}function uo(r){return function(o){return td(n_(t_(o).replace(sR,"")),r,"")}}function xs(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var a=ao(r.prototype),l=r.apply(a,o);return bt(l)?l:a}}function b0(r,o,a){var l=xs(r);function h(){for(var _=arguments.length,S=$(_),T=_,M=lo(h);T--;)S[T]=arguments[T];var V=_<3&&S[0]!==M&&S[_-1]!==M?[]:Qr(S,M);if(_-=V.length,_<a)return ub(r,o,Za,h.placeholder,t,S,V,t,t,a-_);var Q=this&&this!==qt&&this instanceof h?l:r;return wn(Q,this,S)}return h}function ob(r){return function(o,a,l){var h=Ye(o);if(!un(o)){var _=le(a,3);o=Ot(o),a=function(T){return _(h[T],T,h)}}var S=r(o,a,l);return S>-1?h[_?o[S]:S]:t}}function sb(r){return Fr(function(o){var a=o.length,l=a,h=An.prototype.thru;for(r&&o.reverse();l--;){var _=o[l];if(typeof _!="function")throw new Mn(s);if(h&&!S&&rc(_)=="wrapper")var S=new An([],!0)}for(l=S?l:a;++l<a;){_=o[l];var T=rc(_),M=T=="wrapper"?Nd(_):t;M&&Bd(M[0])&&M[1]==(te|K|P|ye)&&!M[4].length&&M[9]==1?S=S[rc(M[0])].apply(S,M[3]):S=_.length==1&&Bd(_)?S[T]():S.thru(_)}return function(){var V=arguments,Q=V[0];if(S&&V.length==1&&Pe(Q))return S.plant(Q).value();for(var J=0,ne=a?o[J].apply(this,V):Q;++J<a;)ne=o[J].call(this,ne);return ne}})}function Za(r,o,a,l,h,_,S,T,M,V){var Q=o&te,J=o&A,ne=o&L,ae=o&(K|F),de=o&Be,Re=ne?t:xs(r);function fe(){for(var Me=arguments.length,qe=$(Me),Cn=Me;Cn--;)qe[Cn]=arguments[Cn];if(ae)var nn=lo(fe),kn=DR(qe,nn);if(l&&(qe=eb(qe,l,h,ae)),_&&(qe=tb(qe,_,S,ae)),Me-=kn,ae&&Me<V){var Ct=Qr(qe,nn);return ub(r,o,Za,fe.placeholder,a,qe,Ct,T,M,V-Me)}var Yn=J?a:this,Ar=ne?Yn[r]:r;return Me=qe.length,T?qe=O0(qe,T):de&&Me>1&&qe.reverse(),Q&&M<Me&&(qe.length=M),this&&this!==qt&&this instanceof fe&&(Ar=Re||xs(Ar)),Ar.apply(Yn,qe)}return fe}function ab(r,o){return function(a,l){return HD(a,r,o(l),{})}}function ec(r,o){return function(a,l){var h;if(a===t&&l===t)return o;if(a!==t&&(h=a),l!==t){if(h===t)return l;typeof a=="string"||typeof l=="string"?(a=xn(a),l=xn(l)):(a=Vm(a),l=Vm(l)),h=r(a,l)}return h}}function Ed(r){return Fr(function(o){return o=gt(o,Sn(le())),Ee(function(a){var l=this;return r(o,function(h){return wn(h,l,a)})})})}function tc(r,o){o=o===t?" ":xn(o);var a=o.length;if(a<2)return a?Pd(o,r):o;var l=Pd(o,$a(r/ro(o)));return no(o)?ei(Kn(l),0,r).join(""):l.slice(0,r)}function _0(r,o,a,l){var h=o&A,_=xs(r);function S(){for(var T=-1,M=arguments.length,V=-1,Q=l.length,J=$(Q+M),ne=this&&this!==qt&&this instanceof S?_:r;++V<Q;)J[V]=l[V];for(;M--;)J[V++]=arguments[++T];return wn(ne,h?a:this,J)}return S}function cb(r){return function(o,a,l){return l&&typeof l!="number"&&tn(o,a,l)&&(a=l=t),o=Mr(o),a===t?(a=o,o=0):a=Mr(a),l=l===t?o<a?1:-1:Mr(l),r0(o,a,l,r)}}function nc(r){return function(o,a){return typeof o=="string"&&typeof a=="string"||(o=Ln(o),a=Ln(a)),r(o,a)}}function ub(r,o,a,l,h,_,S,T,M,V){var Q=o&K,J=Q?S:t,ne=Q?t:S,ae=Q?_:t,de=Q?t:_;o|=Q?P:U,o&=~(Q?U:P),o&B||(o&=~(A|L));var Re=[r,o,h,ae,J,de,ne,T,M,V],fe=a.apply(t,Re);return Bd(r)&&wb(fe,Re),fe.placeholder=l,Sb(fe,r,o)}function Id(r){var o=Mt[r];return function(a,l){if(a=Ln(a),l=l==null?0:zt(Te(l),292),l&&Sm(a)){var h=(Ve(a)+"e").split("e"),_=o(h[0]+"e"+(+h[1]+l));return h=(Ve(_)+"e").split("e"),+(h[0]+"e"+(+h[1]-l))}return o(a)}}var v0=oo&&1/Ea(new oo([,-0]))[1]==De?function(r){return new oo(r)}:ef;function lb(r){return function(o){var a=Ht(o);return a==mt?cd(o):a==xt?qR(o):RR(o,r(o))}}function Dr(r,o,a,l,h,_,S,T){var M=o&L;if(!M&&typeof r!="function")throw new Mn(s);var V=l?l.length:0;if(V||(o&=~(P|U),l=h=t),S=S===t?S:At(Te(S),0),T=T===t?T:Te(T),V-=h?h.length:0,o&U){var Q=l,J=h;l=h=t}var ne=M?t:Nd(r),ae=[r,o,a,l,h,Q,J,_,S,T];if(ne&&A0(ae,ne),r=ae[0],o=ae[1],a=ae[2],l=ae[3],h=ae[4],T=ae[9]=ae[9]===t?M?0:r.length:At(ae[9]-V,0),!T&&o&(K|F)&&(o&=~(K|F)),!o||o==A)var de=m0(r,o,a);else o==K||o==F?de=b0(r,o,T):(o==P||o==(A|P))&&!h.length?de=_0(r,o,a,l):de=Za.apply(t,ae);var Re=ne?zm:wb;return Sb(Re(de,ae),r,o)}function db(r,o,a,l){return r===t||Xn(r,io[a])&&!Ke.call(l,a)?o:r}function fb(r,o,a,l,h,_){return bt(r)&&bt(o)&&(_.set(o,r),Ka(r,o,t,fb,_),_.delete(o)),r}function y0(r){return ks(r)?t:r}function hb(r,o,a,l,h,_){var S=a&D,T=r.length,M=o.length;if(T!=M&&!(S&&M>T))return!1;var V=_.get(r),Q=_.get(o);if(V&&Q)return V==o&&Q==r;var J=-1,ne=!0,ae=a&N?new Di:t;for(_.set(r,o),_.set(o,r);++J<T;){var de=r[J],Re=o[J];if(l)var fe=S?l(Re,de,J,o,r,_):l(de,Re,J,r,o,_);if(fe!==t){if(fe)continue;ne=!1;break}if(ae){if(!nd(o,function(Me,qe){if(!fs(ae,qe)&&(de===Me||h(de,Me,a,l,_)))return ae.push(qe)})){ne=!1;break}}else if(!(de===Re||h(de,Re,a,l,_))){ne=!1;break}}return _.delete(r),_.delete(o),ne}function w0(r,o,a,l,h,_,S){switch(a){case _n:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Gn:return!(r.byteLength!=o.byteLength||!_(new Oa(r),new Oa(o)));case lr:case dr:case fr:return Xn(+r,+o);case wr:return r.name==o.name&&r.message==o.message;case hr:case sn:return r==o+"";case mt:var T=cd;case xt:var M=l&D;if(T||(T=Ea),r.size!=o.size&&!M)return!1;var V=S.get(r);if(V)return V==o;l|=N,S.set(r,o);var Q=hb(T(r),T(o),l,h,_,S);return S.delete(r),Q;case Pr:if(bs)return bs.call(r)==bs.call(o)}return!1}function S0(r,o,a,l,h,_){var S=a&D,T=Md(r),M=T.length,V=Md(o),Q=V.length;if(M!=Q&&!S)return!1;for(var J=M;J--;){var ne=T[J];if(!(S?ne in o:Ke.call(o,ne)))return!1}var ae=_.get(r),de=_.get(o);if(ae&&de)return ae==o&&de==r;var Re=!0;_.set(r,o),_.set(o,r);for(var fe=S;++J<M;){ne=T[J];var Me=r[ne],qe=o[ne];if(l)var Cn=S?l(qe,Me,ne,o,r,_):l(Me,qe,ne,r,o,_);if(!(Cn===t?Me===qe||h(Me,qe,a,l,_):Cn)){Re=!1;break}fe||(fe=ne=="constructor")}if(Re&&!fe){var nn=r.constructor,kn=o.constructor;nn!=kn&&"constructor"in r&&"constructor"in o&&!(typeof nn=="function"&&nn instanceof nn&&typeof kn=="function"&&kn instanceof kn)&&(Re=!1)}return _.delete(r),_.delete(o),Re}function Fr(r){return $d(vb(r,t,Rb),r+"")}function Md(r){return Mm(r,Ot,Od)}function Ad(r){return Mm(r,ln,gb)}var Nd=ja?function(r){return ja.get(r)}:ef;function rc(r){for(var o=r.name+"",a=so[o],l=Ke.call(so,o)?a.length:0;l--;){var h=a[l],_=h.func;if(_==null||_==r)return h.name}return o}function lo(r){var o=Ke.call(b,"placeholder")?b:r;return o.placeholder}function le(){var r=b.iteratee||Yd;return r=r===Yd?qm:r,arguments.length?r(arguments[0],arguments[1]):r}function ic(r,o){var a=r.__data__;return F0(o)?a[typeof o=="string"?"string":"hash"]:a.map}function qd(r){for(var o=Ot(r),a=o.length;a--;){var l=o[a],h=r[l];o[a]=[l,h,bb(h)]}return o}function Ii(r,o){var a=MR(r,o);return Nm(a)?a:t}function x0(r){var o=Ke.call(r,Ti),a=r[Ti];try{r[Ti]=t;var l=!0}catch{}var h=Na.call(r);return l&&(o?r[Ti]=a:delete r[Ti]),h}var Od=ld?function(r){return r==null?[]:(r=Ye(r),Vr(ld(r),function(o){return ym.call(r,o)}))}:tf,gb=ld?function(r){for(var o=[];r;)Gr(o,Od(r)),r=La(r);return o}:tf,Ht=en;(dd&&Ht(new dd(new ArrayBuffer(1)))!=_n||gs&&Ht(new gs)!=mt||fd&&Ht(fd.resolve())!=Pi||oo&&Ht(new oo)!=xt||ps&&Ht(new ps)!=jt)&&(Ht=function(r){var o=en(r),a=o==Bt?r.constructor:t,l=a?Mi(a):"";if(l)switch(l){case oD:return _n;case sD:return mt;case aD:return Pi;case cD:return xt;case uD:return jt}return o});function P0(r,o,a){for(var l=-1,h=a.length;++l<h;){var _=a[l],S=_.size;switch(_.type){case"drop":r+=S;break;case"dropRight":o-=S;break;case"take":o=zt(o,r+S);break;case"takeRight":r=At(r,o-S);break}}return{start:r,end:o}}function C0(r){var o=r.match(g);return o?o[1].split(O):[]}function pb(r,o,a){o=Zr(o,r);for(var l=-1,h=o.length,_=!1;++l<h;){var S=mr(o[l]);if(!(_=r!=null&&a(r,S)))break;r=r[S]}return _||++l!=h?_:(h=r==null?0:r.length,!!h&&dc(h)&&Er(S,h)&&(Pe(r)||Ai(r)))}function k0(r){var o=r.length,a=new r.constructor(o);return o&&typeof r[0]=="string"&&Ke.call(r,"index")&&(a.index=r.index,a.input=r.input),a}function mb(r){return typeof r.constructor=="function"&&!Ps(r)?ao(La(r)):{}}function T0(r,o,a){var l=r.constructor;switch(o){case Gn:return Fd(r);case lr:case dr:return new l(+r);case _n:return l0(r,a);case Cr:case vn:case C:case H:case Z:case ie:case Ue:case Le:case Ge:return Ym(r,a);case mt:return new l;case fr:case sn:return new l(r);case hr:return d0(r);case xt:return new l;case Pr:return f0(r)}}function R0(r,o){var a=o.length;if(!a)return r;var l=a-1;return o[l]=(a>1?"& ":"")+o[l],o=o.join(a>2?", ":" "),r.replace(v,`{
/* [wrapped with `+o+`] */
`)}function D0(r){return Pe(r)||Ai(r)||!!(wm&&r&&r[wm])}function Er(r,o){var a=typeof r;return o=o??ge,!!o&&(a=="number"||a!="symbol"&&Zi.test(r))&&r>-1&&r%1==0&&r<o}function tn(r,o,a){if(!bt(a))return!1;var l=typeof o;return(l=="number"?un(a)&&Er(o,a.length):l=="string"&&o in a)?Xn(a[o],r):!1}function Ld(r,o){if(Pe(r))return!1;var a=typeof r;return a=="number"||a=="symbol"||a=="boolean"||r==null||Pn(r)?!0:Pa.test(r)||!xa.test(r)||o!=null&&r in Ye(o)}function F0(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function Bd(r){var o=rc(r),a=b[o];if(typeof a!="function"||!(o in Ae.prototype))return!1;if(r===a)return!0;var l=Nd(a);return!!l&&r===l[0]}function E0(r){return!!bm&&bm in r}var I0=Ma?Ir:nf;function Ps(r){var o=r&&r.constructor,a=typeof o=="function"&&o.prototype||io;return r===a}function bb(r){return r===r&&!bt(r)}function _b(r,o){return function(a){return a==null?!1:a[r]===o&&(o!==t||r in Ye(a))}}function M0(r){var o=uc(r,function(l){return a.size===d&&a.clear(),l}),a=o.cache;return o}function A0(r,o){var a=r[1],l=o[1],h=a|l,_=h<(A|L|te),S=l==te&&a==K||l==te&&a==ye&&r[7].length<=o[8]||l==(te|ye)&&o[7].length<=o[8]&&a==K;if(!(_||S))return r;l&A&&(r[2]=o[2],h|=a&A?0:B);var T=o[3];if(T){var M=r[3];r[3]=M?eb(M,T,o[4]):T,r[4]=M?Qr(r[3],f):o[4]}return T=o[5],T&&(M=r[5],r[5]=M?tb(M,T,o[6]):T,r[6]=M?Qr(r[5],f):o[6]),T=o[7],T&&(r[7]=T),l&te&&(r[8]=r[8]==null?o[8]:zt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=h,r}function N0(r){var o=[];if(r!=null)for(var a in Ye(r))o.push(a);return o}function q0(r){return Na.call(r)}function vb(r,o,a){return o=At(o===t?r.length-1:o,0),function(){for(var l=arguments,h=-1,_=At(l.length-o,0),S=$(_);++h<_;)S[h]=l[o+h];h=-1;for(var T=$(o+1);++h<o;)T[h]=l[h];return T[o]=a(S),wn(r,this,T)}}function yb(r,o){return o.length<2?r:Ei(r,qn(o,0,-1))}function O0(r,o){for(var a=r.length,l=zt(o.length,a),h=cn(r);l--;){var _=o[l];r[l]=Er(_,a)?h[_]:t}return r}function Ud(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var wb=xb(zm),Cs=YR||function(r,o){return qt.setTimeout(r,o)},$d=xb(s0);function Sb(r,o,a){var l=o+"";return $d(r,R0(l,L0(C0(l),a)))}function xb(r){var o=0,a=0;return function(){var l=nD(),h=X-(l-a);if(a=l,h>0){if(++o>=xe)return arguments[0]}else o=0;return r.apply(t,arguments)}}function oc(r,o){var a=-1,l=r.length,h=l-1;for(o=o===t?l:o;++a<o;){var _=xd(a,h),S=r[_];r[_]=r[a],r[a]=S}return r.length=o,r}var Pb=M0(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(Vl,function(a,l,h,_){o.push(h?_.replace(ht,"$1"):l||a)}),o});function mr(r){if(typeof r=="string"||Pn(r))return r;var o=r+"";return o=="0"&&1/r==-De?"-0":o}function Mi(r){if(r!=null){try{return Aa.call(r)}catch{}try{return r+""}catch{}}return""}function L0(r,o){return In(yr,function(a){var l="_."+a[0];o&a[1]&&!Da(r,l)&&r.push(l)}),r.sort()}function Cb(r){if(r instanceof Ae)return r.clone();var o=new An(r.__wrapped__,r.__chain__);return o.__actions__=cn(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function B0(r,o,a){(a?tn(r,o,a):o===t)?o=1:o=At(Te(o),0);var l=r==null?0:r.length;if(!l||o<1)return[];for(var h=0,_=0,S=$($a(l/o));h<l;)S[_++]=qn(r,h,h+=o);return S}function U0(r){for(var o=-1,a=r==null?0:r.length,l=0,h=[];++o<a;){var _=r[o];_&&(h[l++]=_)}return h}function $0(){var r=arguments.length;if(!r)return[];for(var o=$(r-1),a=arguments[0],l=r;l--;)o[l-1]=arguments[l];return Gr(Pe(a)?cn(a):[a],Ut(o,1))}var W0=Ee(function(r,o){return Pt(r)?vs(r,Ut(o,1,Pt,!0)):[]}),j0=Ee(function(r,o){var a=On(o);return Pt(a)&&(a=t),Pt(r)?vs(r,Ut(o,1,Pt,!0),le(a,2)):[]}),z0=Ee(function(r,o){var a=On(o);return Pt(a)&&(a=t),Pt(r)?vs(r,Ut(o,1,Pt,!0),t,a):[]});function H0(r,o,a){var l=r==null?0:r.length;return l?(o=a||o===t?1:Te(o),qn(r,o<0?0:o,l)):[]}function V0(r,o,a){var l=r==null?0:r.length;return l?(o=a||o===t?1:Te(o),o=l-o,qn(r,0,o<0?0:o)):[]}function G0(r,o){return r&&r.length?Xa(r,le(o,3),!0,!0):[]}function Q0(r,o){return r&&r.length?Xa(r,le(o,3),!0):[]}function K0(r,o,a,l){var h=r==null?0:r.length;return h?(a&&typeof a!="number"&&tn(r,o,a)&&(a=0,l=h),$D(r,o,a,l)):[]}function kb(r,o,a){var l=r==null?0:r.length;if(!l)return-1;var h=a==null?0:Te(a);return h<0&&(h=At(l+h,0)),Fa(r,le(o,3),h)}function Tb(r,o,a){var l=r==null?0:r.length;if(!l)return-1;var h=l-1;return a!==t&&(h=Te(a),h=a<0?At(l+h,0):zt(h,l-1)),Fa(r,le(o,3),h,!0)}function Rb(r){var o=r==null?0:r.length;return o?Ut(r,1):[]}function J0(r){var o=r==null?0:r.length;return o?Ut(r,De):[]}function X0(r,o){var a=r==null?0:r.length;return a?(o=o===t?1:Te(o),Ut(r,o)):[]}function Y0(r){for(var o=-1,a=r==null?0:r.length,l={};++o<a;){var h=r[o];l[h[0]]=h[1]}return l}function Db(r){return r&&r.length?r[0]:t}function Z0(r,o,a){var l=r==null?0:r.length;if(!l)return-1;var h=a==null?0:Te(a);return h<0&&(h=At(l+h,0)),to(r,o,h)}function eF(r){var o=r==null?0:r.length;return o?qn(r,0,-1):[]}var tF=Ee(function(r){var o=gt(r,Rd);return o.length&&o[0]===r[0]?_d(o):[]}),nF=Ee(function(r){var o=On(r),a=gt(r,Rd);return o===On(a)?o=t:a.pop(),a.length&&a[0]===r[0]?_d(a,le(o,2)):[]}),rF=Ee(function(r){var o=On(r),a=gt(r,Rd);return o=typeof o=="function"?o:t,o&&a.pop(),a.length&&a[0]===r[0]?_d(a,t,o):[]});function iF(r,o){return r==null?"":eD.call(r,o)}function On(r){var o=r==null?0:r.length;return o?r[o-1]:t}function oF(r,o,a){var l=r==null?0:r.length;if(!l)return-1;var h=l;return a!==t&&(h=Te(a),h=h<0?At(l+h,0):zt(h,l-1)),o===o?LR(r,o,h):Fa(r,um,h,!0)}function sF(r,o){return r&&r.length?Um(r,Te(o)):t}var aF=Ee(Fb);function Fb(r,o){return r&&r.length&&o&&o.length?Sd(r,o):r}function cF(r,o,a){return r&&r.length&&o&&o.length?Sd(r,o,le(a,2)):r}function uF(r,o,a){return r&&r.length&&o&&o.length?Sd(r,o,t,a):r}var lF=Fr(function(r,o){var a=r==null?0:r.length,l=gd(r,o);return jm(r,gt(o,function(h){return Er(h,a)?+h:h}).sort(Zm)),l});function dF(r,o){var a=[];if(!(r&&r.length))return a;var l=-1,h=[],_=r.length;for(o=le(o,3);++l<_;){var S=r[l];o(S,l,r)&&(a.push(S),h.push(l))}return jm(r,h),a}function Wd(r){return r==null?r:iD.call(r)}function fF(r,o,a){var l=r==null?0:r.length;return l?(a&&typeof a!="number"&&tn(r,o,a)?(o=0,a=l):(o=o==null?0:Te(o),a=a===t?l:Te(a)),qn(r,o,a)):[]}function hF(r,o){return Ja(r,o)}function gF(r,o,a){return Cd(r,o,le(a,2))}function pF(r,o){var a=r==null?0:r.length;if(a){var l=Ja(r,o);if(l<a&&Xn(r[l],o))return l}return-1}function mF(r,o){return Ja(r,o,!0)}function bF(r,o,a){return Cd(r,o,le(a,2),!0)}function _F(r,o){var a=r==null?0:r.length;if(a){var l=Ja(r,o,!0)-1;if(Xn(r[l],o))return l}return-1}function vF(r){return r&&r.length?Hm(r):[]}function yF(r,o){return r&&r.length?Hm(r,le(o,2)):[]}function wF(r){var o=r==null?0:r.length;return o?qn(r,1,o):[]}function SF(r,o,a){return r&&r.length?(o=a||o===t?1:Te(o),qn(r,0,o<0?0:o)):[]}function xF(r,o,a){var l=r==null?0:r.length;return l?(o=a||o===t?1:Te(o),o=l-o,qn(r,o<0?0:o,l)):[]}function PF(r,o){return r&&r.length?Xa(r,le(o,3),!1,!0):[]}function CF(r,o){return r&&r.length?Xa(r,le(o,3)):[]}var kF=Ee(function(r){return Yr(Ut(r,1,Pt,!0))}),TF=Ee(function(r){var o=On(r);return Pt(o)&&(o=t),Yr(Ut(r,1,Pt,!0),le(o,2))}),RF=Ee(function(r){var o=On(r);return o=typeof o=="function"?o:t,Yr(Ut(r,1,Pt,!0),t,o)});function DF(r){return r&&r.length?Yr(r):[]}function FF(r,o){return r&&r.length?Yr(r,le(o,2)):[]}function EF(r,o){return o=typeof o=="function"?o:t,r&&r.length?Yr(r,t,o):[]}function jd(r){if(!(r&&r.length))return[];var o=0;return r=Vr(r,function(a){if(Pt(a))return o=At(a.length,o),!0}),sd(o,function(a){return gt(r,rd(a))})}function Eb(r,o){if(!(r&&r.length))return[];var a=jd(r);return o==null?a:gt(a,function(l){return wn(o,t,l)})}var IF=Ee(function(r,o){return Pt(r)?vs(r,o):[]}),MF=Ee(function(r){return Td(Vr(r,Pt))}),AF=Ee(function(r){var o=On(r);return Pt(o)&&(o=t),Td(Vr(r,Pt),le(o,2))}),NF=Ee(function(r){var o=On(r);return o=typeof o=="function"?o:t,Td(Vr(r,Pt),t,o)}),qF=Ee(jd);function OF(r,o){return Km(r||[],o||[],_s)}function LF(r,o){return Km(r||[],o||[],Ss)}var BF=Ee(function(r){var o=r.length,a=o>1?r[o-1]:t;return a=typeof a=="function"?(r.pop(),a):t,Eb(r,a)});function Ib(r){var o=b(r);return o.__chain__=!0,o}function UF(r,o){return o(r),r}function sc(r,o){return o(r)}var $F=Fr(function(r){var o=r.length,a=o?r[0]:0,l=this.__wrapped__,h=function(_){return gd(_,r)};return o>1||this.__actions__.length||!(l instanceof Ae)||!Er(a)?this.thru(h):(l=l.slice(a,+a+(o?1:0)),l.__actions__.push({func:sc,args:[h],thisArg:t}),new An(l,this.__chain__).thru(function(_){return o&&!_.length&&_.push(t),_}))});function WF(){return Ib(this)}function jF(){return new An(this.value(),this.__chain__)}function zF(){this.__values__===t&&(this.__values__=Vb(this.value()));var r=this.__index__>=this.__values__.length,o=r?t:this.__values__[this.__index__++];return{done:r,value:o}}function HF(){return this}function VF(r){for(var o,a=this;a instanceof Ha;){var l=Cb(a);l.__index__=0,l.__values__=t,o?h.__wrapped__=l:o=l;var h=l;a=a.__wrapped__}return h.__wrapped__=r,o}function GF(){var r=this.__wrapped__;if(r instanceof Ae){var o=r;return this.__actions__.length&&(o=new Ae(this)),o=o.reverse(),o.__actions__.push({func:sc,args:[Wd],thisArg:t}),new An(o,this.__chain__)}return this.thru(Wd)}function QF(){return Qm(this.__wrapped__,this.__actions__)}var KF=Ya(function(r,o,a){Ke.call(r,a)?++r[a]:Rr(r,a,1)});function JF(r,o,a){var l=Pe(r)?am:UD;return a&&tn(r,o,a)&&(o=t),l(r,le(o,3))}function XF(r,o){var a=Pe(r)?Vr:Em;return a(r,le(o,3))}var YF=ob(kb),ZF=ob(Tb);function eE(r,o){return Ut(ac(r,o),1)}function tE(r,o){return Ut(ac(r,o),De)}function nE(r,o,a){return a=a===t?1:Te(a),Ut(ac(r,o),a)}function Mb(r,o){var a=Pe(r)?In:Xr;return a(r,le(o,3))}function Ab(r,o){var a=Pe(r)?wR:Fm;return a(r,le(o,3))}var rE=Ya(function(r,o,a){Ke.call(r,a)?r[a].push(o):Rr(r,a,[o])});function iE(r,o,a,l){r=un(r)?r:ho(r),a=a&&!l?Te(a):0;var h=r.length;return a<0&&(a=At(h+a,0)),fc(r)?a<=h&&r.indexOf(o,a)>-1:!!h&&to(r,o,a)>-1}var oE=Ee(function(r,o,a){var l=-1,h=typeof o=="function",_=un(r)?$(r.length):[];return Xr(r,function(S){_[++l]=h?wn(o,S,a):ys(S,o,a)}),_}),sE=Ya(function(r,o,a){Rr(r,a,o)});function ac(r,o){var a=Pe(r)?gt:Om;return a(r,le(o,3))}function aE(r,o,a,l){return r==null?[]:(Pe(o)||(o=o==null?[]:[o]),a=l?t:a,Pe(a)||(a=a==null?[]:[a]),$m(r,o,a))}var cE=Ya(function(r,o,a){r[a?0:1].push(o)},function(){return[[],[]]});function uE(r,o,a){var l=Pe(r)?td:dm,h=arguments.length<3;return l(r,le(o,4),a,h,Xr)}function lE(r,o,a){var l=Pe(r)?SR:dm,h=arguments.length<3;return l(r,le(o,4),a,h,Fm)}function dE(r,o){var a=Pe(r)?Vr:Em;return a(r,lc(le(o,3)))}function fE(r){var o=Pe(r)?km:i0;return o(r)}function hE(r,o,a){(a?tn(r,o,a):o===t)?o=1:o=Te(o);var l=Pe(r)?ND:o0;return l(r,o)}function gE(r){var o=Pe(r)?qD:a0;return o(r)}function pE(r){if(r==null)return 0;if(un(r))return fc(r)?ro(r):r.length;var o=Ht(r);return o==mt||o==xt?r.size:yd(r).length}function mE(r,o,a){var l=Pe(r)?nd:c0;return a&&tn(r,o,a)&&(o=t),l(r,le(o,3))}var bE=Ee(function(r,o){if(r==null)return[];var a=o.length;return a>1&&tn(r,o[0],o[1])?o=[]:a>2&&tn(o[0],o[1],o[2])&&(o=[o[0]]),$m(r,Ut(o,1),[])}),cc=XR||function(){return qt.Date.now()};function _E(r,o){if(typeof o!="function")throw new Mn(s);return r=Te(r),function(){if(--r<1)return o.apply(this,arguments)}}function Nb(r,o,a){return o=a?t:o,o=r&&o==null?r.length:o,Dr(r,te,t,t,t,t,o)}function qb(r,o){var a;if(typeof o!="function")throw new Mn(s);return r=Te(r),function(){return--r>0&&(a=o.apply(this,arguments)),r<=1&&(o=t),a}}var zd=Ee(function(r,o,a){var l=A;if(a.length){var h=Qr(a,lo(zd));l|=P}return Dr(r,l,o,a,h)}),Ob=Ee(function(r,o,a){var l=A|L;if(a.length){var h=Qr(a,lo(Ob));l|=P}return Dr(o,l,r,a,h)});function Lb(r,o,a){o=a?t:o;var l=Dr(r,K,t,t,t,t,t,o);return l.placeholder=Lb.placeholder,l}function Bb(r,o,a){o=a?t:o;var l=Dr(r,F,t,t,t,t,t,o);return l.placeholder=Bb.placeholder,l}function Ub(r,o,a){var l,h,_,S,T,M,V=0,Q=!1,J=!1,ne=!0;if(typeof r!="function")throw new Mn(s);o=Ln(o)||0,bt(a)&&(Q=!!a.leading,J="maxWait"in a,_=J?At(Ln(a.maxWait)||0,o):_,ne="trailing"in a?!!a.trailing:ne);function ae(Ct){var Yn=l,Ar=h;return l=h=t,V=Ct,S=r.apply(Ar,Yn),S}function de(Ct){return V=Ct,T=Cs(Me,o),Q?ae(Ct):S}function Re(Ct){var Yn=Ct-M,Ar=Ct-V,o_=o-Yn;return J?zt(o_,_-Ar):o_}function fe(Ct){var Yn=Ct-M,Ar=Ct-V;return M===t||Yn>=o||Yn<0||J&&Ar>=_}function Me(){var Ct=cc();if(fe(Ct))return qe(Ct);T=Cs(Me,Re(Ct))}function qe(Ct){return T=t,ne&&l?ae(Ct):(l=h=t,S)}function Cn(){T!==t&&Jm(T),V=0,l=M=h=T=t}function nn(){return T===t?S:qe(cc())}function kn(){var Ct=cc(),Yn=fe(Ct);if(l=arguments,h=this,M=Ct,Yn){if(T===t)return de(M);if(J)return Jm(T),T=Cs(Me,o),ae(M)}return T===t&&(T=Cs(Me,o)),S}return kn.cancel=Cn,kn.flush=nn,kn}var vE=Ee(function(r,o){return Dm(r,1,o)}),yE=Ee(function(r,o,a){return Dm(r,Ln(o)||0,a)});function wE(r){return Dr(r,Be)}function uc(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new Mn(s);var a=function(){var l=arguments,h=o?o.apply(this,l):l[0],_=a.cache;if(_.has(h))return _.get(h);var S=r.apply(this,l);return a.cache=_.set(h,S)||_,S};return a.cache=new(uc.Cache||Tr),a}uc.Cache=Tr;function lc(r){if(typeof r!="function")throw new Mn(s);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function SE(r){return qb(2,r)}var xE=u0(function(r,o){o=o.length==1&&Pe(o[0])?gt(o[0],Sn(le())):gt(Ut(o,1),Sn(le()));var a=o.length;return Ee(function(l){for(var h=-1,_=zt(l.length,a);++h<_;)l[h]=o[h].call(this,l[h]);return wn(r,this,l)})}),Hd=Ee(function(r,o){var a=Qr(o,lo(Hd));return Dr(r,P,t,o,a)}),$b=Ee(function(r,o){var a=Qr(o,lo($b));return Dr(r,U,t,o,a)}),PE=Fr(function(r,o){return Dr(r,ye,t,t,t,o)});function CE(r,o){if(typeof r!="function")throw new Mn(s);return o=o===t?o:Te(o),Ee(r,o)}function kE(r,o){if(typeof r!="function")throw new Mn(s);return o=o==null?0:At(Te(o),0),Ee(function(a){var l=a[o],h=ei(a,0,o);return l&&Gr(h,l),wn(r,this,h)})}function TE(r,o,a){var l=!0,h=!0;if(typeof r!="function")throw new Mn(s);return bt(a)&&(l="leading"in a?!!a.leading:l,h="trailing"in a?!!a.trailing:h),Ub(r,o,{leading:l,maxWait:o,trailing:h})}function RE(r){return Nb(r,1)}function DE(r,o){return Hd(Dd(o),r)}function FE(){if(!arguments.length)return[];var r=arguments[0];return Pe(r)?r:[r]}function EE(r){return Nn(r,x)}function IE(r,o){return o=typeof o=="function"?o:t,Nn(r,x,o)}function ME(r){return Nn(r,m|x)}function AE(r,o){return o=typeof o=="function"?o:t,Nn(r,m|x,o)}function NE(r,o){return o==null||Rm(r,o,Ot(o))}function Xn(r,o){return r===o||r!==r&&o!==o}var qE=nc(bd),OE=nc(function(r,o){return r>=o}),Ai=Am(function(){return arguments}())?Am:function(r){return yt(r)&&Ke.call(r,"callee")&&!ym.call(r,"callee")},Pe=$.isArray,LE=tm?Sn(tm):VD;function un(r){return r!=null&&dc(r.length)&&!Ir(r)}function Pt(r){return yt(r)&&un(r)}function BE(r){return r===!0||r===!1||yt(r)&&en(r)==lr}var ti=ZR||nf,UE=nm?Sn(nm):GD;function $E(r){return yt(r)&&r.nodeType===1&&!ks(r)}function WE(r){if(r==null)return!0;if(un(r)&&(Pe(r)||typeof r=="string"||typeof r.splice=="function"||ti(r)||fo(r)||Ai(r)))return!r.length;var o=Ht(r);if(o==mt||o==xt)return!r.size;if(Ps(r))return!yd(r).length;for(var a in r)if(Ke.call(r,a))return!1;return!0}function jE(r,o){return ws(r,o)}function zE(r,o,a){a=typeof a=="function"?a:t;var l=a?a(r,o):t;return l===t?ws(r,o,t,a):!!l}function Vd(r){if(!yt(r))return!1;var o=en(r);return o==wr||o==Ki||typeof r.message=="string"&&typeof r.name=="string"&&!ks(r)}function HE(r){return typeof r=="number"&&Sm(r)}function Ir(r){if(!bt(r))return!1;var o=en(r);return o==Sr||o==xr||o==xi||o==zr}function Wb(r){return typeof r=="number"&&r==Te(r)}function dc(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=ge}function bt(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function yt(r){return r!=null&&typeof r=="object"}var jb=rm?Sn(rm):KD;function VE(r,o){return r===o||vd(r,o,qd(o))}function GE(r,o,a){return a=typeof a=="function"?a:t,vd(r,o,qd(o),a)}function QE(r){return zb(r)&&r!=+r}function KE(r){if(I0(r))throw new we(i);return Nm(r)}function JE(r){return r===null}function XE(r){return r==null}function zb(r){return typeof r=="number"||yt(r)&&en(r)==fr}function ks(r){if(!yt(r)||en(r)!=Bt)return!1;var o=La(r);if(o===null)return!0;var a=Ke.call(o,"constructor")&&o.constructor;return typeof a=="function"&&a instanceof a&&Aa.call(a)==GR}var Gd=im?Sn(im):JD;function YE(r){return Wb(r)&&r>=-ge&&r<=ge}var Hb=om?Sn(om):XD;function fc(r){return typeof r=="string"||!Pe(r)&&yt(r)&&en(r)==sn}function Pn(r){return typeof r=="symbol"||yt(r)&&en(r)==Pr}var fo=sm?Sn(sm):YD;function ZE(r){return r===t}function eI(r){return yt(r)&&Ht(r)==jt}function tI(r){return yt(r)&&en(r)==Ci}var nI=nc(wd),rI=nc(function(r,o){return r<=o});function Vb(r){if(!r)return[];if(un(r))return fc(r)?Kn(r):cn(r);if(hs&&r[hs])return NR(r[hs]());var o=Ht(r),a=o==mt?cd:o==xt?Ea:ho;return a(r)}function Mr(r){if(!r)return r===0?r:0;if(r=Ln(r),r===De||r===-De){var o=r<0?-1:1;return o*me}return r===r?r:0}function Te(r){var o=Mr(r),a=o%1;return o===o?a?o-a:o:0}function Gb(r){return r?Fi(Te(r),0,We):0}function Ln(r){if(typeof r=="number")return r;if(Pn(r))return St;if(bt(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=bt(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=fm(r);var a=Qn.test(r);return a||Hr.test(r)?_R(r.slice(2),a?2:8):yn.test(r)?St:+r}function Qb(r){return pr(r,ln(r))}function iI(r){return r?Fi(Te(r),-ge,ge):r===0?r:0}function Ve(r){return r==null?"":xn(r)}var oI=co(function(r,o){if(Ps(o)||un(o)){pr(o,Ot(o),r);return}for(var a in o)Ke.call(o,a)&&_s(r,a,o[a])}),Kb=co(function(r,o){pr(o,ln(o),r)}),hc=co(function(r,o,a,l){pr(o,ln(o),r,l)}),sI=co(function(r,o,a,l){pr(o,Ot(o),r,l)}),aI=Fr(gd);function cI(r,o){var a=ao(r);return o==null?a:Tm(a,o)}var uI=Ee(function(r,o){r=Ye(r);var a=-1,l=o.length,h=l>2?o[2]:t;for(h&&tn(o[0],o[1],h)&&(l=1);++a<l;)for(var _=o[a],S=ln(_),T=-1,M=S.length;++T<M;){var V=S[T],Q=r[V];(Q===t||Xn(Q,io[V])&&!Ke.call(r,V))&&(r[V]=_[V])}return r}),lI=Ee(function(r){return r.push(t,fb),wn(Jb,t,r)});function dI(r,o){return cm(r,le(o,3),gr)}function fI(r,o){return cm(r,le(o,3),md)}function hI(r,o){return r==null?r:pd(r,le(o,3),ln)}function gI(r,o){return r==null?r:Im(r,le(o,3),ln)}function pI(r,o){return r&&gr(r,le(o,3))}function mI(r,o){return r&&md(r,le(o,3))}function bI(r){return r==null?[]:Qa(r,Ot(r))}function _I(r){return r==null?[]:Qa(r,ln(r))}function Qd(r,o,a){var l=r==null?t:Ei(r,o);return l===t?a:l}function vI(r,o){return r!=null&&pb(r,o,WD)}function Kd(r,o){return r!=null&&pb(r,o,jD)}var yI=ab(function(r,o,a){o!=null&&typeof o.toString!="function"&&(o=Na.call(o)),r[o]=a},Xd(dn)),wI=ab(function(r,o,a){o!=null&&typeof o.toString!="function"&&(o=Na.call(o)),Ke.call(r,o)?r[o].push(a):r[o]=[a]},le),SI=Ee(ys);function Ot(r){return un(r)?Cm(r):yd(r)}function ln(r){return un(r)?Cm(r,!0):ZD(r)}function xI(r,o){var a={};return o=le(o,3),gr(r,function(l,h,_){Rr(a,o(l,h,_),l)}),a}function PI(r,o){var a={};return o=le(o,3),gr(r,function(l,h,_){Rr(a,h,o(l,h,_))}),a}var CI=co(function(r,o,a){Ka(r,o,a)}),Jb=co(function(r,o,a,l){Ka(r,o,a,l)}),kI=Fr(function(r,o){var a={};if(r==null)return a;var l=!1;o=gt(o,function(_){return _=Zr(_,r),l||(l=_.length>1),_}),pr(r,Ad(r),a),l&&(a=Nn(a,m|y|x,y0));for(var h=o.length;h--;)kd(a,o[h]);return a});function TI(r,o){return Xb(r,lc(le(o)))}var RI=Fr(function(r,o){return r==null?{}:t0(r,o)});function Xb(r,o){if(r==null)return{};var a=gt(Ad(r),function(l){return[l]});return o=le(o),Wm(r,a,function(l,h){return o(l,h[0])})}function DI(r,o,a){o=Zr(o,r);var l=-1,h=o.length;for(h||(h=1,r=t);++l<h;){var _=r==null?t:r[mr(o[l])];_===t&&(l=h,_=a),r=Ir(_)?_.call(r):_}return r}function FI(r,o,a){return r==null?r:Ss(r,o,a)}function EI(r,o,a,l){return l=typeof l=="function"?l:t,r==null?r:Ss(r,o,a,l)}var Yb=lb(Ot),Zb=lb(ln);function II(r,o,a){var l=Pe(r),h=l||ti(r)||fo(r);if(o=le(o,4),a==null){var _=r&&r.constructor;h?a=l?new _:[]:bt(r)?a=Ir(_)?ao(La(r)):{}:a={}}return(h?In:gr)(r,function(S,T,M){return o(a,S,T,M)}),a}function MI(r,o){return r==null?!0:kd(r,o)}function AI(r,o,a){return r==null?r:Gm(r,o,Dd(a))}function NI(r,o,a,l){return l=typeof l=="function"?l:t,r==null?r:Gm(r,o,Dd(a),l)}function ho(r){return r==null?[]:ad(r,Ot(r))}function qI(r){return r==null?[]:ad(r,ln(r))}function OI(r,o,a){return a===t&&(a=o,o=t),a!==t&&(a=Ln(a),a=a===a?a:0),o!==t&&(o=Ln(o),o=o===o?o:0),Fi(Ln(r),o,a)}function LI(r,o,a){return o=Mr(o),a===t?(a=o,o=0):a=Mr(a),r=Ln(r),zD(r,o,a)}function BI(r,o,a){if(a&&typeof a!="boolean"&&tn(r,o,a)&&(o=a=t),a===t&&(typeof o=="boolean"?(a=o,o=t):typeof r=="boolean"&&(a=r,r=t)),r===t&&o===t?(r=0,o=1):(r=Mr(r),o===t?(o=r,r=0):o=Mr(o)),r>o){var l=r;r=o,o=l}if(a||r%1||o%1){var h=xm();return zt(r+h*(o-r+bR("1e-"+((h+"").length-1))),o)}return xd(r,o)}var UI=uo(function(r,o,a){return o=o.toLowerCase(),r+(a?e_(o):o)});function e_(r){return Jd(Ve(r).toLowerCase())}function t_(r){return r=Ve(r),r&&r.replace(Ca,FR).replace(aR,"")}function $I(r,o,a){r=Ve(r),o=xn(o);var l=r.length;a=a===t?l:Fi(Te(a),0,l);var h=a;return a-=o.length,a>=0&&r.slice(a,h)==o}function WI(r){return r=Ve(r),r&&Zt.test(r)?r.replace(ct,ER):r}function jI(r){return r=Ve(r),r&&p.test(r)?r.replace(q,"\\$&"):r}var zI=uo(function(r,o,a){return r+(a?"-":"")+o.toLowerCase()}),HI=uo(function(r,o,a){return r+(a?" ":"")+o.toLowerCase()}),VI=ib("toLowerCase");function GI(r,o,a){r=Ve(r),o=Te(o);var l=o?ro(r):0;if(!o||l>=o)return r;var h=(o-l)/2;return tc(Wa(h),a)+r+tc($a(h),a)}function QI(r,o,a){r=Ve(r),o=Te(o);var l=o?ro(r):0;return o&&l<o?r+tc(o-l,a):r}function KI(r,o,a){r=Ve(r),o=Te(o);var l=o?ro(r):0;return o&&l<o?tc(o-l,a)+r:r}function JI(r,o,a){return a||o==null?o=0:o&&(o=+o),rD(Ve(r).replace(k,""),o||0)}function XI(r,o,a){return(a?tn(r,o,a):o===t)?o=1:o=Te(o),Pd(Ve(r),o)}function YI(){var r=arguments,o=Ve(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var ZI=uo(function(r,o,a){return r+(a?"_":"")+o.toLowerCase()});function eM(r,o,a){return a&&typeof a!="number"&&tn(r,o,a)&&(o=a=t),a=a===t?We:a>>>0,a?(r=Ve(r),r&&(typeof o=="string"||o!=null&&!Gd(o))&&(o=xn(o),!o&&no(r))?ei(Kn(r),0,a):r.split(o,a)):[]}var tM=uo(function(r,o,a){return r+(a?" ":"")+Jd(o)});function nM(r,o,a){return r=Ve(r),a=a==null?0:Fi(Te(a),0,r.length),o=xn(o),r.slice(a,a+o.length)==o}function rM(r,o,a){var l=b.templateSettings;a&&tn(r,o,a)&&(o=t),r=Ve(r),o=hc({},o,l,db);var h=hc({},o.imports,l.imports,db),_=Ot(h),S=ad(h,_),T,M,V=0,Q=o.interpolate||ka,J="__p += '",ne=ud((o.escape||ka).source+"|"+Q.source+"|"+(Q===ds?Xe:ka).source+"|"+(o.evaluate||ka).source+"|$","g"),ae="//# sourceURL="+(Ke.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++fR+"]")+`
`;r.replace(ne,function(fe,Me,qe,Cn,nn,kn){return qe||(qe=Cn),J+=r.slice(V,kn).replace(jT,IR),Me&&(T=!0,J+=`' +
__e(`+Me+`) +
'`),nn&&(M=!0,J+=`';
`+nn+`;
__p += '`),qe&&(J+=`' +
((__t = (`+qe+`)) == null ? '' : __t) +
'`),V=kn+fe.length,fe}),J+=`';
`;var de=Ke.call(o,"variable")&&o.variable;if(!de)J=`with (obj) {
`+J+`
}
`;else if(_e.test(de))throw new we(c);J=(M?J.replace(tt,""):J).replace(Fe,"$1").replace(Qe,"$1;"),J="function("+(de||"obj")+`) {
`+(de?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(T?", __e = _.escape":"")+(M?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+J+`return __p
}`;var Re=r_(function(){return ze(_,ae+"return "+J).apply(t,S)});if(Re.source=J,Vd(Re))throw Re;return Re}function iM(r){return Ve(r).toLowerCase()}function oM(r){return Ve(r).toUpperCase()}function sM(r,o,a){if(r=Ve(r),r&&(a||o===t))return fm(r);if(!r||!(o=xn(o)))return r;var l=Kn(r),h=Kn(o),_=hm(l,h),S=gm(l,h)+1;return ei(l,_,S).join("")}function aM(r,o,a){if(r=Ve(r),r&&(a||o===t))return r.slice(0,mm(r)+1);if(!r||!(o=xn(o)))return r;var l=Kn(r),h=gm(l,Kn(o))+1;return ei(l,0,h).join("")}function cM(r,o,a){if(r=Ve(r),r&&(a||o===t))return r.replace(k,"");if(!r||!(o=xn(o)))return r;var l=Kn(r),h=hm(l,Kn(o));return ei(l,h).join("")}function uM(r,o){var a=Je,l=Dt;if(bt(o)){var h="separator"in o?o.separator:h;a="length"in o?Te(o.length):a,l="omission"in o?xn(o.omission):l}r=Ve(r);var _=r.length;if(no(r)){var S=Kn(r);_=S.length}if(a>=_)return r;var T=a-ro(l);if(T<1)return l;var M=S?ei(S,0,T).join(""):r.slice(0,T);if(h===t)return M+l;if(S&&(T+=M.length-T),Gd(h)){if(r.slice(T).search(h)){var V,Q=M;for(h.global||(h=ud(h.source,Ve(an.exec(h))+"g")),h.lastIndex=0;V=h.exec(Q);)var J=V.index;M=M.slice(0,J===t?T:J)}}else if(r.indexOf(xn(h),T)!=T){var ne=M.lastIndexOf(h);ne>-1&&(M=M.slice(0,ne))}return M+l}function lM(r){return r=Ve(r),r&&Ft.test(r)?r.replace(Ne,BR):r}var dM=uo(function(r,o,a){return r+(a?" ":"")+o.toUpperCase()}),Jd=ib("toUpperCase");function n_(r,o,a){return r=Ve(r),o=a?t:o,o===t?AR(r)?WR(r):CR(r):r.match(o)||[]}var r_=Ee(function(r,o){try{return wn(r,t,o)}catch(a){return Vd(a)?a:new we(a)}}),fM=Fr(function(r,o){return In(o,function(a){a=mr(a),Rr(r,a,zd(r[a],r))}),r});function hM(r){var o=r==null?0:r.length,a=le();return r=o?gt(r,function(l){if(typeof l[1]!="function")throw new Mn(s);return[a(l[0]),l[1]]}):[],Ee(function(l){for(var h=-1;++h<o;){var _=r[h];if(wn(_[0],this,l))return wn(_[1],this,l)}})}function gM(r){return BD(Nn(r,m))}function Xd(r){return function(){return r}}function pM(r,o){return r==null||r!==r?o:r}var mM=sb(),bM=sb(!0);function dn(r){return r}function Yd(r){return qm(typeof r=="function"?r:Nn(r,m))}function _M(r){return Lm(Nn(r,m))}function vM(r,o){return Bm(r,Nn(o,m))}var yM=Ee(function(r,o){return function(a){return ys(a,r,o)}}),wM=Ee(function(r,o){return function(a){return ys(r,a,o)}});function Zd(r,o,a){var l=Ot(o),h=Qa(o,l);a==null&&!(bt(o)&&(h.length||!l.length))&&(a=o,o=r,r=this,h=Qa(o,Ot(o)));var _=!(bt(a)&&"chain"in a)||!!a.chain,S=Ir(r);return In(h,function(T){var M=o[T];r[T]=M,S&&(r.prototype[T]=function(){var V=this.__chain__;if(_||V){var Q=r(this.__wrapped__),J=Q.__actions__=cn(this.__actions__);return J.push({func:M,args:arguments,thisArg:r}),Q.__chain__=V,Q}return M.apply(r,Gr([this.value()],arguments))})}),r}function SM(){return qt._===this&&(qt._=QR),this}function ef(){}function xM(r){return r=Te(r),Ee(function(o){return Um(o,r)})}var PM=Ed(gt),CM=Ed(am),kM=Ed(nd);function i_(r){return Ld(r)?rd(mr(r)):n0(r)}function TM(r){return function(o){return r==null?t:Ei(r,o)}}var RM=cb(),DM=cb(!0);function tf(){return[]}function nf(){return!1}function FM(){return{}}function EM(){return""}function IM(){return!0}function MM(r,o){if(r=Te(r),r<1||r>ge)return[];var a=We,l=zt(r,We);o=le(o),r-=We;for(var h=sd(l,o);++a<r;)o(a);return h}function AM(r){return Pe(r)?gt(r,mr):Pn(r)?[r]:cn(Pb(Ve(r)))}function NM(r){var o=++VR;return Ve(r)+o}var qM=ec(function(r,o){return r+o},0),OM=Id("ceil"),LM=ec(function(r,o){return r/o},1),BM=Id("floor");function UM(r){return r&&r.length?Ga(r,dn,bd):t}function $M(r,o){return r&&r.length?Ga(r,le(o,2),bd):t}function WM(r){return lm(r,dn)}function jM(r,o){return lm(r,le(o,2))}function zM(r){return r&&r.length?Ga(r,dn,wd):t}function HM(r,o){return r&&r.length?Ga(r,le(o,2),wd):t}var VM=ec(function(r,o){return r*o},1),GM=Id("round"),QM=ec(function(r,o){return r-o},0);function KM(r){return r&&r.length?od(r,dn):0}function JM(r,o){return r&&r.length?od(r,le(o,2)):0}return b.after=_E,b.ary=Nb,b.assign=oI,b.assignIn=Kb,b.assignInWith=hc,b.assignWith=sI,b.at=aI,b.before=qb,b.bind=zd,b.bindAll=fM,b.bindKey=Ob,b.castArray=FE,b.chain=Ib,b.chunk=B0,b.compact=U0,b.concat=$0,b.cond=hM,b.conforms=gM,b.constant=Xd,b.countBy=KF,b.create=cI,b.curry=Lb,b.curryRight=Bb,b.debounce=Ub,b.defaults=uI,b.defaultsDeep=lI,b.defer=vE,b.delay=yE,b.difference=W0,b.differenceBy=j0,b.differenceWith=z0,b.drop=H0,b.dropRight=V0,b.dropRightWhile=G0,b.dropWhile=Q0,b.fill=K0,b.filter=XF,b.flatMap=eE,b.flatMapDeep=tE,b.flatMapDepth=nE,b.flatten=Rb,b.flattenDeep=J0,b.flattenDepth=X0,b.flip=wE,b.flow=mM,b.flowRight=bM,b.fromPairs=Y0,b.functions=bI,b.functionsIn=_I,b.groupBy=rE,b.initial=eF,b.intersection=tF,b.intersectionBy=nF,b.intersectionWith=rF,b.invert=yI,b.invertBy=wI,b.invokeMap=oE,b.iteratee=Yd,b.keyBy=sE,b.keys=Ot,b.keysIn=ln,b.map=ac,b.mapKeys=xI,b.mapValues=PI,b.matches=_M,b.matchesProperty=vM,b.memoize=uc,b.merge=CI,b.mergeWith=Jb,b.method=yM,b.methodOf=wM,b.mixin=Zd,b.negate=lc,b.nthArg=xM,b.omit=kI,b.omitBy=TI,b.once=SE,b.orderBy=aE,b.over=PM,b.overArgs=xE,b.overEvery=CM,b.overSome=kM,b.partial=Hd,b.partialRight=$b,b.partition=cE,b.pick=RI,b.pickBy=Xb,b.property=i_,b.propertyOf=TM,b.pull=aF,b.pullAll=Fb,b.pullAllBy=cF,b.pullAllWith=uF,b.pullAt=lF,b.range=RM,b.rangeRight=DM,b.rearg=PE,b.reject=dE,b.remove=dF,b.rest=CE,b.reverse=Wd,b.sampleSize=hE,b.set=FI,b.setWith=EI,b.shuffle=gE,b.slice=fF,b.sortBy=bE,b.sortedUniq=vF,b.sortedUniqBy=yF,b.split=eM,b.spread=kE,b.tail=wF,b.take=SF,b.takeRight=xF,b.takeRightWhile=PF,b.takeWhile=CF,b.tap=UF,b.throttle=TE,b.thru=sc,b.toArray=Vb,b.toPairs=Yb,b.toPairsIn=Zb,b.toPath=AM,b.toPlainObject=Qb,b.transform=II,b.unary=RE,b.union=kF,b.unionBy=TF,b.unionWith=RF,b.uniq=DF,b.uniqBy=FF,b.uniqWith=EF,b.unset=MI,b.unzip=jd,b.unzipWith=Eb,b.update=AI,b.updateWith=NI,b.values=ho,b.valuesIn=qI,b.without=IF,b.words=n_,b.wrap=DE,b.xor=MF,b.xorBy=AF,b.xorWith=NF,b.zip=qF,b.zipObject=OF,b.zipObjectDeep=LF,b.zipWith=BF,b.entries=Yb,b.entriesIn=Zb,b.extend=Kb,b.extendWith=hc,Zd(b,b),b.add=qM,b.attempt=r_,b.camelCase=UI,b.capitalize=e_,b.ceil=OM,b.clamp=OI,b.clone=EE,b.cloneDeep=ME,b.cloneDeepWith=AE,b.cloneWith=IE,b.conformsTo=NE,b.deburr=t_,b.defaultTo=pM,b.divide=LM,b.endsWith=$I,b.eq=Xn,b.escape=WI,b.escapeRegExp=jI,b.every=JF,b.find=YF,b.findIndex=kb,b.findKey=dI,b.findLast=ZF,b.findLastIndex=Tb,b.findLastKey=fI,b.floor=BM,b.forEach=Mb,b.forEachRight=Ab,b.forIn=hI,b.forInRight=gI,b.forOwn=pI,b.forOwnRight=mI,b.get=Qd,b.gt=qE,b.gte=OE,b.has=vI,b.hasIn=Kd,b.head=Db,b.identity=dn,b.includes=iE,b.indexOf=Z0,b.inRange=LI,b.invoke=SI,b.isArguments=Ai,b.isArray=Pe,b.isArrayBuffer=LE,b.isArrayLike=un,b.isArrayLikeObject=Pt,b.isBoolean=BE,b.isBuffer=ti,b.isDate=UE,b.isElement=$E,b.isEmpty=WE,b.isEqual=jE,b.isEqualWith=zE,b.isError=Vd,b.isFinite=HE,b.isFunction=Ir,b.isInteger=Wb,b.isLength=dc,b.isMap=jb,b.isMatch=VE,b.isMatchWith=GE,b.isNaN=QE,b.isNative=KE,b.isNil=XE,b.isNull=JE,b.isNumber=zb,b.isObject=bt,b.isObjectLike=yt,b.isPlainObject=ks,b.isRegExp=Gd,b.isSafeInteger=YE,b.isSet=Hb,b.isString=fc,b.isSymbol=Pn,b.isTypedArray=fo,b.isUndefined=ZE,b.isWeakMap=eI,b.isWeakSet=tI,b.join=iF,b.kebabCase=zI,b.last=On,b.lastIndexOf=oF,b.lowerCase=HI,b.lowerFirst=VI,b.lt=nI,b.lte=rI,b.max=UM,b.maxBy=$M,b.mean=WM,b.meanBy=jM,b.min=zM,b.minBy=HM,b.stubArray=tf,b.stubFalse=nf,b.stubObject=FM,b.stubString=EM,b.stubTrue=IM,b.multiply=VM,b.nth=sF,b.noConflict=SM,b.noop=ef,b.now=cc,b.pad=GI,b.padEnd=QI,b.padStart=KI,b.parseInt=JI,b.random=BI,b.reduce=uE,b.reduceRight=lE,b.repeat=XI,b.replace=YI,b.result=DI,b.round=GM,b.runInContext=E,b.sample=fE,b.size=pE,b.snakeCase=ZI,b.some=mE,b.sortedIndex=hF,b.sortedIndexBy=gF,b.sortedIndexOf=pF,b.sortedLastIndex=mF,b.sortedLastIndexBy=bF,b.sortedLastIndexOf=_F,b.startCase=tM,b.startsWith=nM,b.subtract=QM,b.sum=KM,b.sumBy=JM,b.template=rM,b.times=MM,b.toFinite=Mr,b.toInteger=Te,b.toLength=Gb,b.toLower=iM,b.toNumber=Ln,b.toSafeInteger=iI,b.toString=Ve,b.toUpper=oM,b.trim=sM,b.trimEnd=aM,b.trimStart=cM,b.truncate=uM,b.unescape=lM,b.uniqueId=NM,b.upperCase=dM,b.upperFirst=Jd,b.each=Mb,b.eachRight=Ab,b.first=Db,Zd(b,function(){var r={};return gr(b,function(o,a){Ke.call(b.prototype,a)||(r[a]=o)}),r}(),{chain:!1}),b.VERSION=e,In(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){b[r].placeholder=b}),In(["drop","take"],function(r,o){Ae.prototype[r]=function(a){a=a===t?1:At(Te(a),0);var l=this.__filtered__&&!o?new Ae(this):this.clone();return l.__filtered__?l.__takeCount__=zt(a,l.__takeCount__):l.__views__.push({size:zt(a,We),type:r+(l.__dir__<0?"Right":"")}),l},Ae.prototype[r+"Right"]=function(a){return this.reverse()[r](a).reverse()}}),In(["filter","map","takeWhile"],function(r,o){var a=o+1,l=a==z||a==re;Ae.prototype[r]=function(h){var _=this.clone();return _.__iteratees__.push({iteratee:le(h,3),type:a}),_.__filtered__=_.__filtered__||l,_}}),In(["head","last"],function(r,o){var a="take"+(o?"Right":"");Ae.prototype[r]=function(){return this[a](1).value()[0]}}),In(["initial","tail"],function(r,o){var a="drop"+(o?"":"Right");Ae.prototype[r]=function(){return this.__filtered__?new Ae(this):this[a](1)}}),Ae.prototype.compact=function(){return this.filter(dn)},Ae.prototype.find=function(r){return this.filter(r).head()},Ae.prototype.findLast=function(r){return this.reverse().find(r)},Ae.prototype.invokeMap=Ee(function(r,o){return typeof r=="function"?new Ae(this):this.map(function(a){return ys(a,r,o)})}),Ae.prototype.reject=function(r){return this.filter(lc(le(r)))},Ae.prototype.slice=function(r,o){r=Te(r);var a=this;return a.__filtered__&&(r>0||o<0)?new Ae(a):(r<0?a=a.takeRight(-r):r&&(a=a.drop(r)),o!==t&&(o=Te(o),a=o<0?a.dropRight(-o):a.take(o-r)),a)},Ae.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},Ae.prototype.toArray=function(){return this.take(We)},gr(Ae.prototype,function(r,o){var a=/^(?:filter|find|map|reject)|While$/.test(o),l=/^(?:head|last)$/.test(o),h=b[l?"take"+(o=="last"?"Right":""):o],_=l||/^find/.test(o);h&&(b.prototype[o]=function(){var S=this.__wrapped__,T=l?[1]:arguments,M=S instanceof Ae,V=T[0],Q=M||Pe(S),J=function(Me){var qe=h.apply(b,Gr([Me],T));return l&&ne?qe[0]:qe};Q&&a&&typeof V=="function"&&V.length!=1&&(M=Q=!1);var ne=this.__chain__,ae=!!this.__actions__.length,de=_&&!ne,Re=M&&!ae;if(!_&&Q){S=Re?S:new Ae(this);var fe=r.apply(S,T);return fe.__actions__.push({func:sc,args:[J],thisArg:t}),new An(fe,ne)}return de&&Re?r.apply(this,T):(fe=this.thru(J),de?l?fe.value()[0]:fe.value():fe)})}),In(["pop","push","shift","sort","splice","unshift"],function(r){var o=Ia[r],a=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",l=/^(?:pop|shift)$/.test(r);b.prototype[r]=function(){var h=arguments;if(l&&!this.__chain__){var _=this.value();return o.apply(Pe(_)?_:[],h)}return this[a](function(S){return o.apply(Pe(S)?S:[],h)})}}),gr(Ae.prototype,function(r,o){var a=b[o];if(a){var l=a.name+"";Ke.call(so,l)||(so[l]=[]),so[l].push({name:o,func:a})}}),so[Za(t,L).name]=[{name:"wrapper",func:t}],Ae.prototype.clone=lD,Ae.prototype.reverse=dD,Ae.prototype.value=fD,b.prototype.at=$F,b.prototype.chain=WF,b.prototype.commit=jF,b.prototype.next=zF,b.prototype.plant=VF,b.prototype.reverse=GF,b.prototype.toJSON=b.prototype.valueOf=b.prototype.value=QF,b.prototype.first=b.prototype.head,hs&&(b.prototype[hs]=HF),b},Kr=jR();typeof define=="function"&&typeof define.amd=="object"&&define.amd?(qt._=Kr,define(function(){return Kr})):ki?((ki.exports=Kr)._=Kr,Yl._=Kr):qt._=Kr}).call(Go)});var Xk=I((g3,nj)=>{nj.exports=[{name:"C",vscode_name:"c",type:"programming",extensions:[".c",".cats",".h",".idc",".w"]},{name:"C++",vscode_name:"cpp",type:"programming",extensions:[".cpp",".c++",".cc",".cp",".cxx",".h",".h++",".hh",".hpp",".hxx",".inc",".inl",".ipp",".tcc",".tpp"]},{name:"Go",vscode_name:"go",type:"programming",extensions:[".go"]},{name:"Java",vscode_name:"java",type:"programming",extensions:[".java"]},{name:"JavaScript",vscode_name:"javascript",type:"programming",extensions:[".js","._js",".bones",".es",".es6",".frag",".gs",".jake",".jsb",".jscad",".jsfl",".jsm",".jss",".njs",".pac",".sjs",".ssjs",".sublime-build",".sublime-commands",".sublime-completions",".sublime-keymap",".sublime-macro",".sublime-menu",".sublime-mousemap",".sublime-project",".sublime-settings",".sublime-theme",".sublime-workspace",".sublime_metrics",".sublime_session",".xsjs",".xsjslib"]},{name:"Python",vscode_name:"python",type:"programming",extensions:[".py",".bzl",".cgi",".fcgi",".gyp",".lmi",".pyde",".pyp",".pyt",".pyw",".rpy",".tac",".wsgi",".xpy"]}]});var bT=I((pG,mT)=>{"use strict";function cT(t){return Array.isArray(t)?t:[t]}var fT="",uT=" ",Yg="\\",pj=/^\s+$/,mj=/(?:[^\\]|^)\\$/,bj=/^\\!/,_j=/^\\#/,vj=/\r?\n/g,yj=/^\.*\/|^\.+$/,Zg="/",hT="node-ignore";typeof Symbol<"u"&&(hT=Symbol.for("node-ignore"));var lT=hT,wj=(t,e,n)=>Object.defineProperty(t,e,{value:n}),Sj=/([0-z])-([0-z])/g,gT=()=>!1,xj=t=>t.replace(Sj,(e,n,i)=>n.charCodeAt(0)<=i.charCodeAt(0)?e:fT),Pj=t=>{let{length:e}=t;return t.slice(0,e-e%2)},Cj=[[/\\?\s+$/,t=>t.indexOf("\\")===0?uT:fT],[/\\\s/g,()=>uT],[/[\\$.|*+(){^]/g,t=>`\\${t}`],[/(?!\\)\?/g,()=>"[^/]"],[/^\//,()=>"^"],[/\//g,()=>"\\/"],[/^\^*\\\*\\\*\\\//,()=>"^(?:.*\\/)?"],[/^(?=[^^])/,function(){return/\/(?!$)/.test(this)?"^":"(?:^|\\/)"}],[/\\\/\\\*\\\*(?=\\\/|$)/g,(t,e,n)=>e+6<n.length?"(?:\\/[^\\/]+)*":"\\/.+"],[/(^|[^\\]+)(\\\*)+(?=.+)/g,(t,e,n)=>{let i=n.replace(/\\\*/g,"[^\\/]*");return e+i}],[/\\\\\\(?=[$.|*+(){^])/g,()=>Yg],[/\\\\/g,()=>Yg],[/(\\)?\[([^\]/]*?)(\\*)($|\])/g,(t,e,n,i,s)=>e===Yg?`\\[${n}${Pj(i)}${s}`:s==="]"&&i.length%2===0?`[${xj(n)}${i}]`:"[]"],[/(?:[^*])$/,t=>/\/$/.test(t)?`${t}$`:`${t}(?=$|\\/$)`],[/(\^|\\\/)?\\\*$/,(t,e)=>`${e?`${e}[^/]+`:"[^/]*"}(?=$|\\/$)`]],dT=Object.create(null),kj=(t,e)=>{let n=dT[t];return n||(n=Cj.reduce((i,s)=>i.replace(s[0],s[1].bind(t)),t),dT[t]=n),e?new RegExp(n,"i"):new RegExp(n)},np=t=>typeof t=="string",Tj=t=>t&&np(t)&&!pj.test(t)&&!mj.test(t)&&t.indexOf("#")!==0,Rj=t=>t.split(vj),ep=class{constructor(e,n,i,s){this.origin=e,this.pattern=n,this.negative=i,this.regex=s}},Dj=(t,e)=>{let n=t,i=!1;t.indexOf("!")===0&&(i=!0,t=t.substr(1)),t=t.replace(bj,"!").replace(_j,"#");let s=kj(t,e);return new ep(n,t,i,s)},Fj=(t,e)=>{throw new e(t)},Br=(t,e,n)=>np(t)?t?Br.isNotRelative(t)?n(`path should be a \`path.relative()\`d string, but got "${e}"`,RangeError):!0:n("path must not be empty",TypeError):n(`path must be a string, but got \`${e}\``,TypeError),pT=t=>yj.test(t);Br.isNotRelative=pT;Br.convert=t=>t;var tp=class{constructor({ignorecase:e=!0,ignoreCase:n=e,allowRelativePaths:i=!1}={}){wj(this,lT,!0),this._rules=[],this._ignoreCase=n,this._allowRelativePaths=i,this._initCache()}_initCache(){this._ignoreCache=Object.create(null),this._testCache=Object.create(null)}_addPattern(e){if(e&&e[lT]){this._rules=this._rules.concat(e._rules),this._added=!0;return}if(Tj(e)){let n=Dj(e,this._ignoreCase);this._added=!0,this._rules.push(n)}}add(e){return this._added=!1,cT(np(e)?Rj(e):e).forEach(this._addPattern,this),this._added&&this._initCache(),this}addPattern(e){return this.add(e)}_testOne(e,n){let i=!1,s=!1;return this._rules.forEach(c=>{let{negative:u}=c;if(s===u&&i!==s||u&&!i&&!s&&!n)return;c.regex.test(e)&&(i=!u,s=u)}),{ignored:i,unignored:s}}_test(e,n,i,s){let c=e&&Br.convert(e);return Br(c,e,this._allowRelativePaths?gT:Fj),this._t(c,n,i,s)}_t(e,n,i,s){if(e in n)return n[e];if(s||(s=e.split(Zg)),s.pop(),!s.length)return n[e]=this._testOne(e,i);let c=this._t(s.join(Zg)+Zg,n,i,s);if(c.ignored)return n[e]=c;let u=this._testOne(e,i);return n[e]={ignored:u.ignored,unignored:c.unignored||u.unignored}}ignores(e){return this._test(e,this._ignoreCache,!1).ignored}createFilter(){return e=>!this.ignores(e)}filter(e){return cT(e).filter(this.createFilter())}test(e){return this._test(e,this._testCache,!0)}},gl=t=>new tp(t),Ej=t=>Br(t&&Br.convert(t),t,gT);gl.isPathValid=Ej;gl.default=gl;mT.exports=gl;if(typeof process<"u"&&(process.env&&process.env.IGNORE_TEST_WIN32||process.platform==="win32")){let t=n=>/^\\\\\?\\/.test(n)||/["<>|\u0000-\u001F]+/u.test(n)?n:n.replace(/\\/g,"/");Br.convert=t;let e=/^[a-z]:\//i;Br.isNotRelative=n=>e.test(n)||pT(n)}});var t2={};module.exports=nA(t2);var WT=Ze(require("os")),Hl=Ze(require("path"));var gc=class t{constructor(e,n,i,s){this._uri=e,this._languageId=n,this._version=i,this._content=s,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let n=this.offsetAt(e.start),i=this.offsetAt(e.end);return this._content.substring(n,i)}return this._content}update(e,n){for(let i of e)if(t.isIncremental(i)){let s=u_(i.range),c=this.offsetAt(s.start),u=this.offsetAt(s.end);this._content=this._content.substring(0,c)+i.text+this._content.substring(u,this._content.length);let d=Math.max(s.start.line,0),f=Math.max(s.end.line,0),m=this._lineOffsets,y=a_(i.text,!1,c);if(f-d===y.length)for(let D=0,N=y.length;D<N;D++)m[D+d+1]=y[D];else y.length<1e4?m.splice(d+1,f-d,...y):this._lineOffsets=m=m.slice(0,d+1).concat(y,m.slice(f+1));let x=i.text.length-(u-c);if(x!==0)for(let D=d+1+y.length,N=m.length;D<N;D++)m[D]=m[D]+x}else if(t.isFull(i))this._content=i.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=n}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=a_(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let n=this.getLineOffsets(),i=0,s=n.length;if(s===0)return{line:0,character:e};for(;i<s;){let u=Math.floor((i+s)/2);n[u]>e?s=u:i=u+1}let c=i-1;return e=this.ensureBeforeEOL(e,n[c]),{line:c,character:e-n[c]}}offsetAt(e){let n=this.getLineOffsets();if(e.line>=n.length)return this._content.length;if(e.line<0)return 0;let i=n[e.line];if(e.character<=0)return i;let s=e.line+1<n.length?n[e.line+1]:this._content.length,c=Math.min(i+e.character,s);return this.ensureBeforeEOL(c,i)}ensureBeforeEOL(e,n){for(;e>n&&c_(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let n=e;return n!=null&&typeof n.text=="string"&&n.range!==void 0&&(n.rangeLength===void 0||typeof n.rangeLength=="number")}static isFull(e){let n=e;return n!=null&&typeof n.text=="string"&&n.range===void 0&&n.rangeLength===void 0}},Ni;(function(t){function e(s,c,u,d){return new gc(s,c,u,d)}t.create=e;function n(s,c,u){if(s instanceof gc)return s.update(c,u),s;throw new Error("TextDocument.update: document must be created by TextDocument.create")}t.update=n;function i(s,c){let u=s.getText(),d=of(c.map(rA),(y,x)=>{let D=y.range.start.line-x.range.start.line;return D===0?y.range.start.character-x.range.start.character:D}),f=0,m=[];for(let y of d){let x=s.offsetAt(y.range.start);if(x<f)throw new Error("Overlapping edit");x>f&&m.push(u.substring(f,x)),y.newText.length&&m.push(y.newText),f=s.offsetAt(y.range.end)}return m.push(u.substr(f)),m.join("")}t.applyEdits=i})(Ni||(Ni={}));function of(t,e){if(t.length<=1)return t;let n=t.length/2|0,i=t.slice(0,n),s=t.slice(n);of(i,e),of(s,e);let c=0,u=0,d=0;for(;c<i.length&&u<s.length;)e(i[c],s[u])<=0?t[d++]=i[c++]:t[d++]=s[u++];for(;c<i.length;)t[d++]=i[c++];for(;u<s.length;)t[d++]=s[u++];return t}function a_(t,e,n=0){let i=e?[n]:[];for(let s=0;s<t.length;s++){let c=t.charCodeAt(s);c_(c)&&(c===13&&s+1<t.length&&t.charCodeAt(s+1)===10&&s++,i.push(n+s+1))}return i}function c_(t){return t===13||t===10}function u_(t){let e=t.start,n=t.end;return e.line>n.line||e.line===n.line&&e.character>n.character?{start:n,end:e}:t}function rA(t){let e=u_(t.range);return e!==t.range?{newText:t.newText,range:e}:t}var ke=Ze(og());var IS=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;function vq(t){return typeof t=="string"&&IS.test(t)}var sg=vq;var Wt=[];for(let t=0;t<256;++t)Wt.push((t+256).toString(16).slice(1));function MS(t,e=0){return(Wt[t[e+0]]+Wt[t[e+1]]+Wt[t[e+2]]+Wt[t[e+3]]+"-"+Wt[t[e+4]]+Wt[t[e+5]]+"-"+Wt[t[e+6]]+Wt[t[e+7]]+"-"+Wt[t[e+8]]+Wt[t[e+9]]+"-"+Wt[t[e+10]]+Wt[t[e+11]]+Wt[t[e+12]]+Wt[t[e+13]]+Wt[t[e+14]]+Wt[t[e+15]]).toLowerCase()}var AS=require("crypto"),Iu=new Uint8Array(256),Eu=Iu.length;function ag(){return Eu>Iu.length-16&&((0,AS.randomFillSync)(Iu),Eu=0),Iu.slice(Eu,Eu+=16)}var NS=require("crypto"),cg={randomUUID:NS.randomUUID};function yq(t,e,n){if(cg.randomUUID&&!e&&!t)return cg.randomUUID();t=t||{};let i=t.random||(t.rng||ag)();if(i[6]=i[6]&15|64,i[8]=i[8]&63|128,e){n=n||0;for(let s=0;s<16;++s)e[n+s]=i[s];return e}return MS(i)}var Ws=yq;var Mu=class extends Error{constructor(n){super(`Configured model "${n}" is not available`);this.modelName=n}};var Au=class extends Error{constructor(){super("No models available")}},gi=class extends Error{constructor(e="Skipping inline completion."){super(e)}},vt=class t extends Error{constructor(n,i){super(i);this.status=n}static transientIssue(n){return new t(3,n)}static fromResponse(n){return new t(wq(n.status),`HTTP error: ${n.status} ${n.statusText}`)}static isAPIErrorWithStatus(n,i){return n instanceof t?n.status===i:!1}static isRetriableAPIError(n){return n instanceof t?Sq.has(n.status):!1}};function wq(t){switch(t){case 200:return 0;case 400:return 5;case 401:return 7;case 403:return 8;case 404:return 4;case 413:return 10;case 429:return 6;case 499:return 1;case 504:return 9}return t>=500&&t<600?3:2}var Sq=new Set([3,1]);function xq(t){return t.cause instanceof String?String(t.cause):t.cause instanceof Object?JSON.stringify(t.cause):""}function pe(t,e=!1){if(t instanceof Error){if(e){let n=xq(t);if(n!=="")return`${t.message} (due to ${n})`}return t.message}return String(t)}var Jk=Ze(Ak());var Qu=Ze(require("fs")),Vo=Ze(Ig()),Ok=Ze(require("os")),ea=Ze(require("path")),QW=process.env.TEST_TMPDIR?ea.default.join(process.env.TEST_TMPDIR,"state"):process.env.XDG_STATE_HOME||ea.default.join(Ok.default.homedir(),".local","state"),qk=ea.default.join(QW,"augment","augment-server.log");function Lk(){let t=ea.default.dirname(qk);Qu.default.existsSync(t)||Qu.default.mkdirSync(t,{recursive:!0});let e=(process.env.AUGMENT_LOG_LEVEL||"warn").toLowerCase(),n;["trace","debug","info","warn","error"].includes(e)?Vo.default.setLevel(e):(n=`Environment variable AUGMENT_LOG_LEVEL set to invalid log level "${e}". Defaulting to "warn"`,Vo.default.setLevel("warn")),Vo.default.methodFactory=function(s,c,u){return function(d){let f=new Date().toISOString(),m=typeof u=="string"?` '${u}'`:"";Qu.default.appendFileSync(qk,`${f} [${s.toUpperCase()}]${m} ${d}
`)}};let i=Vo.default.getLogger("server");return n&&i.warn(n),i}function ve(t){let e=Vo.default.getLogger(t);return{...e,verbose:(n,...i)=>e.debug(n,...i)}}var na=Ze(Qo()),Ko=class{constructor(e,n){this.name=e;this._logger=n}_isSet=!1;_value;get value(){return this._value}update(e){return this._isSet&&(0,na.isEqual)(e,this._value)?!1:(this._isSet?this._logger.info(`${this.name} changed:
${this.diff(this.value,e).map(n=>`  - ${n}`).join(`
`)}`):this._logger.info(`${this.name} changed from <unset> to ${this._formatValue(e)}`),this._value=(0,na.cloneDeep)(e),this._isSet=!0,!0)}diff(e,n,i=[]){if((0,na.isEqual)(n,e))return[];if(!this.isObject(n)||!this.isObject(e))return[`${this._formatValue(e)} to ${this._formatValue(n)}`];let s=new Set([...Object.keys(e||{}),...Object.keys(n||{})]),c=[];for(let u of s){let d=n?n[u]:void 0,f=e?e[u]:void 0;!this.isObject(d)||!this.isObject(f)?d!==f&&c.push(`${i.concat(u).join(" > ")}: ${this._formatValue(f)} to ${this._formatValue(d)}`):c.push(...this.diff(f,d,i.concat(u)))}return c}isObject(e){return typeof e=="object"&&e!==null}toString(){return this._isSet?this._formatValue(this.value):"<unset>"}_formatValue(e){return e===void 0?"undefined":JSON.stringify(e)}};var zk=require("events"),Dn=Ze(require("fs")),Hk=Ze(Ig()),Vk=Ze(require("os")),Yu=Ze(require("path")),Gk=Ze(ig());var Bk;(()=>{"use strict";var t={470:s=>{function c(f){if(typeof f!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(f))}function u(f,m){for(var y,x="",D=0,N=-1,A=0,L=0;L<=f.length;++L){if(L<f.length)y=f.charCodeAt(L);else{if(y===47)break;y=47}if(y===47){if(!(N===L-1||A===1))if(N!==L-1&&A===2){if(x.length<2||D!==2||x.charCodeAt(x.length-1)!==46||x.charCodeAt(x.length-2)!==46){if(x.length>2){var B=x.lastIndexOf("/");if(B!==x.length-1){B===-1?(x="",D=0):D=(x=x.slice(0,B)).length-1-x.lastIndexOf("/"),N=L,A=0;continue}}else if(x.length===2||x.length===1){x="",D=0,N=L,A=0;continue}}m&&(x.length>0?x+="/..":x="..",D=2)}else x.length>0?x+="/"+f.slice(N+1,L):x=f.slice(N+1,L),D=L-N-1;N=L,A=0}else y===46&&A!==-1?++A:A=-1}return x}var d={resolve:function(){for(var f,m="",y=!1,x=arguments.length-1;x>=-1&&!y;x--){var D;x>=0?D=arguments[x]:(f===void 0&&(f=process.cwd()),D=f),c(D),D.length!==0&&(m=D+"/"+m,y=D.charCodeAt(0)===47)}return m=u(m,!y),y?m.length>0?"/"+m:"/":m.length>0?m:"."},normalize:function(f){if(c(f),f.length===0)return".";var m=f.charCodeAt(0)===47,y=f.charCodeAt(f.length-1)===47;return(f=u(f,!m)).length!==0||m||(f="."),f.length>0&&y&&(f+="/"),m?"/"+f:f},isAbsolute:function(f){return c(f),f.length>0&&f.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var f,m=0;m<arguments.length;++m){var y=arguments[m];c(y),y.length>0&&(f===void 0?f=y:f+="/"+y)}return f===void 0?".":d.normalize(f)},relative:function(f,m){if(c(f),c(m),f===m||(f=d.resolve(f))===(m=d.resolve(m)))return"";for(var y=1;y<f.length&&f.charCodeAt(y)===47;++y);for(var x=f.length,D=x-y,N=1;N<m.length&&m.charCodeAt(N)===47;++N);for(var A=m.length-N,L=D<A?D:A,B=-1,K=0;K<=L;++K){if(K===L){if(A>L){if(m.charCodeAt(N+K)===47)return m.slice(N+K+1);if(K===0)return m.slice(N+K)}else D>L&&(f.charCodeAt(y+K)===47?B=K:K===0&&(B=0));break}var F=f.charCodeAt(y+K);if(F!==m.charCodeAt(N+K))break;F===47&&(B=K)}var P="";for(K=y+B+1;K<=x;++K)K!==x&&f.charCodeAt(K)!==47||(P.length===0?P+="..":P+="/..");return P.length>0?P+m.slice(N+B):(N+=B,m.charCodeAt(N)===47&&++N,m.slice(N))},_makeLong:function(f){return f},dirname:function(f){if(c(f),f.length===0)return".";for(var m=f.charCodeAt(0),y=m===47,x=-1,D=!0,N=f.length-1;N>=1;--N)if((m=f.charCodeAt(N))===47){if(!D){x=N;break}}else D=!1;return x===-1?y?"/":".":y&&x===1?"//":f.slice(0,x)},basename:function(f,m){if(m!==void 0&&typeof m!="string")throw new TypeError('"ext" argument must be a string');c(f);var y,x=0,D=-1,N=!0;if(m!==void 0&&m.length>0&&m.length<=f.length){if(m.length===f.length&&m===f)return"";var A=m.length-1,L=-1;for(y=f.length-1;y>=0;--y){var B=f.charCodeAt(y);if(B===47){if(!N){x=y+1;break}}else L===-1&&(N=!1,L=y+1),A>=0&&(B===m.charCodeAt(A)?--A==-1&&(D=y):(A=-1,D=L))}return x===D?D=L:D===-1&&(D=f.length),f.slice(x,D)}for(y=f.length-1;y>=0;--y)if(f.charCodeAt(y)===47){if(!N){x=y+1;break}}else D===-1&&(N=!1,D=y+1);return D===-1?"":f.slice(x,D)},extname:function(f){c(f);for(var m=-1,y=0,x=-1,D=!0,N=0,A=f.length-1;A>=0;--A){var L=f.charCodeAt(A);if(L!==47)x===-1&&(D=!1,x=A+1),L===46?m===-1?m=A:N!==1&&(N=1):m!==-1&&(N=-1);else if(!D){y=A+1;break}}return m===-1||x===-1||N===0||N===1&&m===x-1&&m===y+1?"":f.slice(m,x)},format:function(f){if(f===null||typeof f!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof f);return function(m,y){var x=y.dir||y.root,D=y.base||(y.name||"")+(y.ext||"");return x?x===y.root?x+D:x+"/"+D:D}(0,f)},parse:function(f){c(f);var m={root:"",dir:"",base:"",ext:"",name:""};if(f.length===0)return m;var y,x=f.charCodeAt(0),D=x===47;D?(m.root="/",y=1):y=0;for(var N=-1,A=0,L=-1,B=!0,K=f.length-1,F=0;K>=y;--K)if((x=f.charCodeAt(K))!==47)L===-1&&(B=!1,L=K+1),x===46?N===-1?N=K:F!==1&&(F=1):N!==-1&&(F=-1);else if(!B){A=K+1;break}return N===-1||L===-1||F===0||F===1&&N===L-1&&N===A+1?L!==-1&&(m.base=m.name=A===0&&D?f.slice(1,L):f.slice(A,L)):(A===0&&D?(m.name=f.slice(1,N),m.base=f.slice(1,L)):(m.name=f.slice(A,N),m.base=f.slice(A,L)),m.ext=f.slice(N,L)),A>0?m.dir=f.slice(0,A-1):D&&(m.dir="/"),m},sep:"/",delimiter:":",win32:null,posix:null};d.posix=d,s.exports=d}},e={};function n(s){var c=e[s];if(c!==void 0)return c.exports;var u=e[s]={exports:{}};return t[s](u,u.exports,n),u.exports}n.d=(s,c)=>{for(var u in c)n.o(c,u)&&!n.o(s,u)&&Object.defineProperty(s,u,{enumerable:!0,get:c[u]})},n.o=(s,c)=>Object.prototype.hasOwnProperty.call(s,c),n.r=s=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})};var i={};(()=>{let s;n.r(i),n.d(i,{URI:()=>D,Utils:()=>xe}),typeof process=="object"?s=process.platform==="win32":typeof navigator=="object"&&(s=navigator.userAgent.indexOf("Windows")>=0);let c=/^\w[\w\d+.-]*$/,u=/^\//,d=/^\/\//;function f(X,z){if(!X.scheme&&z)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${X.authority}", path: "${X.path}", query: "${X.query}", fragment: "${X.fragment}"}`);if(X.scheme&&!c.test(X.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(X.path){if(X.authority){if(!u.test(X.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(d.test(X.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}let m="",y="/",x=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class D{static isUri(z){return z instanceof D||!!z&&typeof z.authority=="string"&&typeof z.fragment=="string"&&typeof z.path=="string"&&typeof z.query=="string"&&typeof z.scheme=="string"&&typeof z.fsPath=="string"&&typeof z.with=="function"&&typeof z.toString=="function"}scheme;authority;path;query;fragment;constructor(z,se,re,De,ge,me=!1){typeof z=="object"?(this.scheme=z.scheme||m,this.authority=z.authority||m,this.path=z.path||m,this.query=z.query||m,this.fragment=z.fragment||m):(this.scheme=function(St,We){return St||We?St:"file"}(z,me),this.authority=se||m,this.path=function(St,We){switch(St){case"https":case"http":case"file":We?We[0]!==y&&(We=y+We):We=y}return We}(this.scheme,re||m),this.query=De||m,this.fragment=ge||m,f(this,me))}get fsPath(){return F(this,!1)}with(z){if(!z)return this;let{scheme:se,authority:re,path:De,query:ge,fragment:me}=z;return se===void 0?se=this.scheme:se===null&&(se=m),re===void 0?re=this.authority:re===null&&(re=m),De===void 0?De=this.path:De===null&&(De=m),ge===void 0?ge=this.query:ge===null&&(ge=m),me===void 0?me=this.fragment:me===null&&(me=m),se===this.scheme&&re===this.authority&&De===this.path&&ge===this.query&&me===this.fragment?this:new A(se,re,De,ge,me)}static parse(z,se=!1){let re=x.exec(z);return re?new A(re[2]||m,ye(re[4]||m),ye(re[5]||m),ye(re[7]||m),ye(re[9]||m),se):new A(m,m,m,m,m)}static file(z){let se=m;if(s&&(z=z.replace(/\\/g,y)),z[0]===y&&z[1]===y){let re=z.indexOf(y,2);re===-1?(se=z.substring(2),z=y):(se=z.substring(2,re),z=z.substring(re)||y)}return new A("file",se,z,m,m)}static from(z){let se=new A(z.scheme,z.authority,z.path,z.query,z.fragment);return f(se,!0),se}toString(z=!1){return P(this,z)}toJSON(){return this}static revive(z){if(z){if(z instanceof D)return z;{let se=new A(z);return se._formatted=z.external,se._fsPath=z._sep===N?z.fsPath:null,se}}return z}}let N=s?1:void 0;class A extends D{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=F(this,!1)),this._fsPath}toString(z=!1){return z?P(this,!0):(this._formatted||(this._formatted=P(this,!1)),this._formatted)}toJSON(){let z={$mid:1};return this._fsPath&&(z.fsPath=this._fsPath,z._sep=N),this._formatted&&(z.external=this._formatted),this.path&&(z.path=this.path),this.scheme&&(z.scheme=this.scheme),this.authority&&(z.authority=this.authority),this.query&&(z.query=this.query),this.fragment&&(z.fragment=this.fragment),z}}let L={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function B(X,z,se){let re,De=-1;for(let ge=0;ge<X.length;ge++){let me=X.charCodeAt(ge);if(me>=97&&me<=122||me>=65&&me<=90||me>=48&&me<=57||me===45||me===46||me===95||me===126||z&&me===47||se&&me===91||se&&me===93||se&&me===58)De!==-1&&(re+=encodeURIComponent(X.substring(De,ge)),De=-1),re!==void 0&&(re+=X.charAt(ge));else{re===void 0&&(re=X.substr(0,ge));let St=L[me];St!==void 0?(De!==-1&&(re+=encodeURIComponent(X.substring(De,ge)),De=-1),re+=St):De===-1&&(De=ge)}}return De!==-1&&(re+=encodeURIComponent(X.substring(De))),re!==void 0?re:X}function K(X){let z;for(let se=0;se<X.length;se++){let re=X.charCodeAt(se);re===35||re===63?(z===void 0&&(z=X.substr(0,se)),z+=L[re]):z!==void 0&&(z+=X[se])}return z!==void 0?z:X}function F(X,z){let se;return se=X.authority&&X.path.length>1&&X.scheme==="file"?`//${X.authority}${X.path}`:X.path.charCodeAt(0)===47&&(X.path.charCodeAt(1)>=65&&X.path.charCodeAt(1)<=90||X.path.charCodeAt(1)>=97&&X.path.charCodeAt(1)<=122)&&X.path.charCodeAt(2)===58?z?X.path.substr(1):X.path[1].toLowerCase()+X.path.substr(2):X.path,s&&(se=se.replace(/\//g,"\\")),se}function P(X,z){let se=z?K:B,re="",{scheme:De,authority:ge,path:me,query:St,fragment:We}=X;if(De&&(re+=De,re+=":"),(ge||De==="file")&&(re+=y,re+=y),ge){let je=ge.indexOf("@");if(je!==-1){let bn=ge.substr(0,je);ge=ge.substr(je+1),je=bn.lastIndexOf(":"),je===-1?re+=se(bn,!1,!1):(re+=se(bn.substr(0,je),!1,!1),re+=":",re+=se(bn.substr(je+1),!1,!0)),re+="@"}ge=ge.toLowerCase(),je=ge.lastIndexOf(":"),je===-1?re+=se(ge,!1,!0):(re+=se(ge.substr(0,je),!1,!0),re+=ge.substr(je))}if(me){if(me.length>=3&&me.charCodeAt(0)===47&&me.charCodeAt(2)===58){let je=me.charCodeAt(1);je>=65&&je<=90&&(me=`/${String.fromCharCode(je+32)}:${me.substr(3)}`)}else if(me.length>=2&&me.charCodeAt(1)===58){let je=me.charCodeAt(0);je>=65&&je<=90&&(me=`${String.fromCharCode(je+32)}:${me.substr(2)}`)}re+=se(me,!0,!1)}return St&&(re+="?",re+=se(St,!1,!1)),We&&(re+="#",re+=z?We:B(We,!1,!1)),re}function U(X){try{return decodeURIComponent(X)}catch{return X.length>3?X.substr(0,3)+U(X.substr(3)):X}}let te=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ye(X){return X.match(te)?X.replace(te,z=>U(z)):X}var Be=n(470);let Je=Be.posix||Be,Dt="/";var xe;(function(X){X.joinPath=function(z,...se){return z.with({path:Je.join(z.path,...se)})},X.resolvePath=function(z,...se){let re=z.path,De=!1;re[0]!==Dt&&(re=Dt+re,De=!0);let ge=Je.resolve(re,...se);return De&&ge[0]===Dt&&!z.authority&&(ge=ge.substring(1)),z.with({path:ge})},X.dirname=function(z){if(z.path.length===0||z.path===Dt)return z;let se=Je.dirname(z.path);return se.length===1&&se.charCodeAt(0)===46&&(se=""),z.with({path:se})},X.basename=function(z){return Je.basename(z.path)},X.extname=function(z){return Je.extname(z.path)}})(xe||(xe={}))})(),Bk=i})();var{URI:ot,Utils:Tt}=Bk;var Ku=class{documents=new Map;add(e){this.documents.set(e.uri.toString(),e)}remove(e){this.documents.delete(e)}get(e){return this.documents.get(e)}has(e){return this.documents.has(e)}clear(){this.documents.clear()}getAllDocuments(){return Array.from(this.documents.values())}getDocumentVersion(e){return this.documents.get(e)?.version}updateDocument(e,n){let i=this.documents.get(e);if(!i)return;let s=Ni.update(i,n.contentChanges,n.textDocument.version);this.documents.set(e,s)}};var Hi=Ze(require("fs")),rn=Ze(require("fs/promises"));function KW(t){return t.isFile()?"File":t.isDirectory()?"Directory":"Other"}function JW(t){return t.isFile()?"File":t.isDirectory()?"Directory":"Other"}function Uk(t){return{size:t.size,type:JW(t),mtime:Math.floor(t.mtimeMs)}}async function $k(t){let e=await rn.lstat(t);return Uk(e)}function Ju(t){try{return Vi(t).type==="File"}catch{return!1}}async function Wk(t){try{return(await rn.stat(t)).isDirectory()}catch{return!1}}function Vi(t){let e=Hi.lstatSync(t);return Uk(e)}function ra(t){let e=[],n=Hi.readdirSync(t,{withFileTypes:!0});for(let i of n)e.push([i.name,KW(i)]);return e}async function ia(t){await rn.mkdir(t,{recursive:!0})}async function Mg(t,e){await rn.rename(t,e)}async function jk(t){return await rn.readFile(t)}async function Jo(t){return await rn.readFile(t,{encoding:"utf8"})}async function Xu(t,e){return await rn.writeFile(t,e,{encoding:"utf8"})}function Ag(t){return Hi.readFileSync(t,{encoding:"utf8"})}var Xo=class extends Error{constructor(){super("Operation cancelled")}};var Or=class t{_position;constructor(e,n){this._position=Gk.Position.create(e,n)}get line(){return this._position.line}get character(){return this._position.character}translate(e,n){return e=e??0,n=n??0,new t(this.line+e,this.character+n)}},bi=class{start;end;constructor(e,n,i,s){if(typeof e=="number"&&typeof n=="number"&&typeof i=="number"&&typeof s=="number")this.start=new Or(e,n),this.end=new Or(i,s);else if(e instanceof Or&&n instanceof Or)this.start=e,this.end=n;else throw new Error("Invalid arguments for Range constructor")}},_i=class{constructor(e){this.document=e}get uri(){return ot.parse(this.document.uri)}get languageId(){return this.document.languageId}get version(){return this.document.version}positionAt(e){let n=this.document.positionAt(e);return new Or(n.line,n.character)}offsetAt(e){return this.document.offsetAt(e)}getText(e){return this.document.getText(e)}},rr=class{dispose;constructor(e){this.dispose=()=>e()}},ue=class{nodeEventEmitter;constructor(){this.nodeEventEmitter=new zk.EventEmitter,this.event=(e,n,i)=>{let s=n?e.bind(n):e;this.nodeEventEmitter.on("event",s);let c=new rr(()=>{this.nodeEventEmitter.off("event",s)});return Array.isArray(i)&&i.push(c),c}}event;fire(e){this.nodeEventEmitter.emit("event",e)}dispose(){this.nodeEventEmitter.removeAllListeners("event")}};var Lr=class{token;tokenEvent;constructor(){this.tokenEvent=new ue,this.token={isCancellationRequested:!1,onCancellationRequested:this.tokenEvent.event}}cancel(){this.token.isCancellationRequested=!0,this.tokenEvent.fire()}dispose(){this.tokenEvent.dispose()}},XW=new ue,Og=new ue,Lg=new ue,Bg=new ue,YW=new ue,ZW=new ue;var Ng=new ue,Qk=[];function Kk(t){Qk=t.map((e,n)=>({uri:ot.parse(e.uri),name:e.name,index:n}))}function ej(){let t=(0,Hk.getLogger)("WorkspaceConfig"),e=process.env.TEST_TMPDIR?Yu.join(process.env.TEST_TMPDIR,"config"):process.env.XDG_CONFIG_HOME||Yu.join(Vk.default.homedir(),".config"),n=process.env.AUGMENT_CONFIG_PATH||Yu.join(e,"augment","user_config.json");try{if(t.debug(`Checking for user config at ${n}`),!Ju(n))return t.debug(`File doesn't exist: ${n}`),{};let i=Ag(n),s=JSON.parse(i);return t.info(`Found user config: ${JSON.stringify(s)}`),s}catch(i){t.error(`Failed to read augment config at ${n}: ${pe(i)}`)}return{}}var Rt={onDidChangeConfiguration:XW.event,getConfiguration:t=>t==="augment"?ej():{},onDidOpenTextDocument:Og.event,onDidChangeTextDocument:Lg.event,onDidCloseTextDocument:Bg.event,onWillRenameFiles:YW.event,onDidChangeWorkspaceFolders:ZW.event,documents:new Ku,get textDocuments(){return this.documents.getAllDocuments().map(t=>new _i(t))},get workspaceFolders(){return Qk}},qg=class{secretsPath;constructor(e){Dn.existsSync(e.fsPath)||Dn.mkdirSync(e.fsPath,{recursive:!0}),this.secretsPath=Tt.joinPath(e,"secrets.json").fsPath,Dn.existsSync(this.secretsPath)||Dn.writeFileSync(this.secretsPath,"{}",{mode:384})}async readSecrets(){try{let e=await Dn.promises.readFile(this.secretsPath,{encoding:"utf8"});return JSON.parse(e)}catch{return{}}}async writeSecrets(e){await Dn.promises.writeFile(this.secretsPath,JSON.stringify(e,null,2),{mode:384,encoding:"utf8"})}async get(e){return(await this.readSecrets())[e]}async store(e,n){let i=await this.readSecrets();i[e]=n,await this.writeSecrets(i),Ng.fire({key:e})}async delete(e){let n=await this.readSecrets();e in n&&(delete n[e],await this.writeSecrets(n),Ng.fire({key:e}))}onDidChange=Ng.event},Zu=class{inMemoryState=new Map;statePath;constructor(e){Dn.mkdirSync(e.fsPath,{recursive:!0,mode:493}),this.statePath=Tt.joinPath(e,"state.json").fsPath;try{let n=Ag(this.statePath),i=JSON.parse(n);this.inMemoryState=new Map(i)}catch{return}}get(e){return this.inMemoryState.get(e)}async update(e,n){this.inMemoryState.set(e,n);let i=JSON.stringify(Array.from(this.inMemoryState.entries()));await Dn.promises.writeFile(this.statePath,i,{encoding:"utf8",mode:420})}},el=class{globalState;workspaceState;storagePath;storageUri;globalStoragePath;globalStorageUri;secrets;constructor(e){Dn.mkdirSync(e.fsPath,{recursive:!0,mode:493}),this.storageUri=Tt.joinPath(e,"workspaceStorage"),this.storagePath=this.storageUri.fsPath,this.globalStorageUri=Tt.joinPath(e,"globalStorage"),this.globalStoragePath=this.globalStorageUri.fsPath,this.secrets=new qg(e),this.globalState=new Zu(this.globalStorageUri),this.workspaceState=new Zu(this.storageUri)}};var tj=128*1024,$g={gitDiff:!1,gitDiffPollingFrequencyMSec:0,additionalChatModels:"",smallSyncThreshold:15,bigSyncThreshold:1e3,enableWorkspaceManagerUi:!1,enableInstructions:!1,enableSmartPaste:!1,enableSmartPasteMinVersion:"",enableViewTextDocument:!1,bypassLanguageFilter:!1,enableHindsight:!1,maxUploadSizeBytes:tj,vscodeNextEditMinVersion:"",vscodeNextEditUx1MaxVersion:"",vscodeNextEditUx2MaxVersion:"",vscodeFlywheelMinVersion:"",vscodeExternalSourcesInChatMinVersion:"",vscodeShareMinVersion:"",maxTrackableFileCount:25e4,maxTrackableFileCountWithoutPermission:15e4,minUploadedPercentageWithoutPermission:90,vscodeSourcesMinVersion:"",vscodeChatHintDecorationMinVersion:"",nextEditDebounceMs:400,enableCompletionFileEditEvents:!1,vscodeEnableCpuProfile:!1,verifyFolderIsSourceRepo:!1,refuseToSyncHomeDirectories:!1,enableFileLimitsForSyncingPermission:!1,enableChatMermaidDiagrams:!1,enableSummaryTitles:!1,vscodeNewThreadsMenuMinVersion:"",vscodeEditableHistoryMinVersion:"",vscodeEnableChatMermaidDiagramsMinVersion:"",userGuidelinesLengthLimit:2e3,workspaceGuidelinesLengthLimit:2e3,enableGuidelines:!1,useCheckpointManagerContextMinVersion:"",validateCheckpointManagerContext:!1,vscodeDesignSystemRichTextEditorMinVersion:"",vscodeChatWithToolsMinVersion:"",vscodeAgentModeMinVersion:""},Ug=class{constructor(e,n,i){this._watchedFlags=n;this._callback=i;this._currentFlags=(0,Jk.default)(e)}_disposed=!1;_currentFlags;get disposed(){return this._disposed}trigger(e){if(this._disposed)return;let n=[];for(let i of this._watchedFlags)e[i]!==this._currentFlags[i]&&n.push(i);n.length>0&&this._callback({previousFlags:this._currentFlags,newFlags:e,changedFlags:n})}dispose(){this._disposed=!0}},tl=class{_subscriptions=[];_refreshTimer;_disposed=!1;_logger=ve("FeatureFlagManager");_flags;constructor(e){this._flags=new Ko("feature flags",this._logger),this._flags.update(e?.initialFlags??$g),this._setupRefreshTimer(e)}get currentFlags(){if(this._disposed)throw Error("FeatureFlagManager has been disposed");return this._flags.value}update(e){if(this._disposed)throw Error("FeatureFlagManager has been disposed");this._flags.update(e),this._subscriptions=this._subscriptions.filter(n=>!n.disposed);for(let n of this._subscriptions)n.trigger(e)}subscribe(e,n){if(this._disposed)throw Error("FeatureFlagManager has been disposed");let i=new Ug(this._flags.value,e,n);return this._subscriptions.push(i),i}_setupRefreshTimer(e){if(!e?.fetcher||!e?.refreshIntervalMSec)return;this._cleanupRefreshTimer();let n=new Lr().token,i=e.fetcher,s=e.refreshIntervalMSec,c=async()=>{let u=await i(n);u?this.update(u):n.isCancellationRequested&&this._cleanupRefreshTimer()};this._refreshTimer=setInterval(()=>void c(),s)}_cleanupRefreshTimer(){clearInterval(this._refreshTimer),this._refreshTimer=void 0}dispose(){this._disposed||(this._subscriptions.forEach(e=>e.dispose()),this._subscriptions=[],this._cleanupRefreshTimer(),this._disposed=!0)}};var rj=Xk(),Wg=[],ij=new Set;for(let t of rj){Wg.push({name:t.name,vscodeName:t.vscodeName,extensions:t.extensions});for(let e of t.extensions)ij.add(e)}function oa(t,e,n){if(!Array.isArray(n))throw new Error(`Value of ${t}.${e} is not Array`)}function vi(t,e,n){let i=typeof n;if(i!=="number")throw new Error(`Value of ${t}.${e} has unexpected type. Expected number, received ${i}`);return n}function on(t,e,n){let i=typeof n;if(i!=="string")throw new Error(`Value of ${t}.${e} has unexpected type. Expected string, received ${i}`);return n}function Yo(t,e,n){oa(t,e,n);let i=[];for(let s of n)i.push(on(t,e,s));return i}function jg(t,e,n,i=!1){if(n===null)return i;let s=typeof n;if(s!=="boolean")throw new Error(`Value of ${t}.${e} has unexpected type. Expected boolean, received ${s}`);return n}function sa(t,e=""){let n=[];if(typeof t!="object"||t===null)return Yk(t);for(let[i,s]of Object.entries(t))if(Array.isArray(s)){n.push(`${e}${i}: (array) ${s.length} (array length) ${JSON.stringify(s).length} (char length)`);let c=20;s.slice(0,c).forEach((u,d)=>{n.push(`${e}  [${d}]: ${sa(u,e+"  ")}`)}),s.length>c&&n.push(`${e}  ${s.length-c} more items...`)}else typeof s=="object"&&s!==null?(n.push(`${e}${i}: (object) ${Object.keys(s).length} (object size) ${JSON.stringify(s).length} (char length)`),n.push(sa(s,e+"  "))):n.push(`${e}${i}: ${Yk(s)}`);return n.join(`
`)}function Yk(t){return typeof t=="string"?`${t.length} (string length)`:typeof t=="boolean"||t===null||typeof t=="number"?"1":"N/A"}var oj={initialMS:100,mult:2,maxMS:3e4};function aa(t){let e;return new Promise(n=>{e=t(i=>{e.dispose(),n(i)})})}function zg(t){return t===0?Promise.resolve():new Promise(e=>{setTimeout(e,t)})}async function Fn(t,e,n=oj){let i=0,s=n.maxTotalMs!==void 0?Date.now():void 0,c=n.canRetry?n.canRetry:u=>vt.isRetriableAPIError(u);for(let u=0;;u++)try{let d=await t();return u>0&&e.info(`Operation succeeded after ${u} transient failures`),d}catch(d){if(!c(d))throw d;let f=u+1;if(n.maxTries!==void 0&&f>=n.maxTries||(i===0?i=n.initialMS:i=Math.min(i*n.mult,n.maxMS),e.info(`Operation failed with error ${d}, retrying in ${i} ms; retries = ${u}`),n.maxTotalMs!==void 0&&s!==void 0&&Date.now()-s+i>n.maxTotalMs))throw d;await zg(i)}}async function Hg(t,e){let n=new Promise((i,s)=>{setTimeout(()=>{s(new Error("Execution aborted due to timeout."))},e)});return await Promise.race([t,n])}function nl(t){return{checkpoint_id:t.checkpointId,added_blobs:t.addedBlobs.sort(),deleted_blobs:t.deletedBlobs.sort()}}var Vg=class extends Error{constructor(e,n){super(`Conversion failure: ${e}. Response = ${n}`)}},rl=class t{constructor(e,n,i,s,c){this._configListener=e;this._auth=n;this.sessionId=i;this._userAgent=s;this._fetchFunction=c}static defaultRequestTimeoutMs=3e4;_sequenceId=new Gg;_logger=ve("AugmentExtension");getSessionId(){return this.sessionId}createRequestId(){return Ws()}async callApi(e,n,i,s,c,u,d,f){let m=n.apiToken,y=!1;if(this._auth.useOAuth){let U=await this._auth.getSession();U&&(m=U.accessToken,y=!0,u||(u=U.tenantURL))}else u||(u=n.completionURL);if(!u)throw new Error("Please configure Augment API URL");let x;try{x=new URL(i,u)}catch(U){throw this._logger.error("Augment API URL is invalid:",U),new Gi}if(!x.protocol.startsWith("http"))throw new Error("Augment API URL must start with 'http://' or 'https://'");let D=JSON.stringify(s,(U,te)=>te===void 0?null:te),N=d??t.defaultRequestTimeoutMs,A=AbortSignal.timeout(N),L="POST",B,K,F;try{let U={"Content-Type":"application/json","User-Agent":this._userAgent,"x-request-id":`${e}`,"x-request-session-id":`${this.sessionId}`,"x-api-version":"2"};m&&(U.Authorization=`Bearer ${m}`),K=Date.now(),B=await Hg(this._fetchFunction(x.toString(),{method:L,headers:U,body:D,signal:A}),N),F=Date.now()}catch(U){throw U instanceof Error?(this._logger.error(`API request ${e} to ${x.toString()} failed: ${pe(U,!0)}`),vt.transientIssue(U.message)):(this._logger.debug(`API request ${e} to ${x.toString()} failed`),U)}if(f&&(f.rpcStart=K,f.rpcEnd=F),!B.ok)throw B.status===499?vt.fromResponse(B):(B.status===401&&y&&this._auth.removeSession(),B.status===400&&n.enableDebugFeatures&&this._logger.error(`API request ${e} to ${x.toString()} failed: ${await B.text()}`),this._logger.error(`API request ${e} to ${x.toString()} response ${B.status}: ${B.statusText}`),vt.fromResponse(B).status===10&&this._logger.debug(`object size is ${sa(s)} `),vt.fromResponse(B));let P;try{if(B.headers.get("content-length")==="0")return;P=await B.json()}catch(U){throw this._logger.error(`API request ${e} to ${x.toString()} failed to convert response to json: ${U.message}`),U}try{return c?c(P):P}catch(U){throw new Vg(pe(U),JSON.stringify(P))}}async callApiStream(e,n,i,s,c=m=>m,u,d,f){let m=n.apiToken;if(this._auth.useOAuth){let P=await this._auth.getSession();P&&(m=P.accessToken,u||(u=P.tenantURL))}else u||(u=n.completionURL);if(!u)throw new Error("Please configure Augment API URL");let y;try{y=new URL(i,u)}catch(P){throw this._logger.error("Augment API URL is invalid:",P),new Gi}if(!y.protocol.startsWith("http"))throw new Error("Augment API URL must start with 'http://' or 'https://'");let x=JSON.stringify(s,(P,U)=>U===void 0?null:U),D=d??t.defaultRequestTimeoutMs,N=AbortSignal.timeout(D),A="POST",L;try{let P={"Content-Type":"application/json","User-Agent":this._userAgent,"x-request-id":`${e}`,"x-request-session-id":`${f??this.sessionId}`};m&&(P.Authorization=`Bearer ${m}`),L=await Hg(this._fetchFunction(y.toString(),{method:A,headers:P,body:x,signal:N}),D)}catch(P){throw P instanceof Error?(this._logger.error(`API request ${e} to ${y.toString()} failed: ${pe(P,!0)}`),P.stack&&this._logger.error(P.stack),vt.transientIssue(P.message)):P}if(!L.ok)throw L.status===499?vt.fromResponse(L):(L.status===401&&this._auth.removeSession(),L.status===400&&n.enableDebugFeatures&&this._logger.error(`API request ${e} to ${y.toString()} failed: ${await L.text()}`),this._logger.error(`API request ${e} to ${y.toString()} response ${L.status}: ${L.statusText}`),vt.fromResponse(L).status===10&&this._logger.debug(`object size is ${s?sa(s):0} `),vt.fromResponse(L));let B=L.body.getReader(),K=this._logger;async function*F(P){let U=new TextDecoder,te="";for(;;){let{value:ye,done:Be}=await P.read(new Uint8Array);if(Be)return;for(te+=U.decode(ye,{stream:!0});te.includes(`
`);){let Je=te.indexOf(`
`),Dt=te.substring(0,Je);te=te.substring(Je+1);try{let xe=JSON.parse(Dt);yield c(xe)}catch(xe){K.error(`JSON parse failed for ${Dt}: ${pe(xe)}`)}}}}return F(B)}_toCompletionItem(e){if(typeof e.text!="string")throw new Error(`Completion item text is not a string: ${JSON.stringify(e)}`);if(e.skipped_suffix!==void 0&&typeof e.skipped_suffix!="string")throw new Error(`Completion item skipped suffix is not a string: ${JSON.stringify(e)}`);if(e.suffix_replacement_text!==void 0&&typeof e.suffix_replacement_text!="string")throw new Error(`Completion item suffix replacement text is not a string: ${JSON.stringify(e)}`);return{text:e.text,suffixReplacementText:e.suffix_replacement_text??"",skippedSuffix:e.skipped_suffix??"",filterScore:e.filter_score??void 0}}_extractCompletions(e){return Array.isArray(e.completion_items)?e.completion_items.map(n=>this._toCompletionItem(n)):Array.isArray(e.completions)?Yo("BackCompletionResult","completions",e.completions).map(i=>({text:i,suffixReplacementText:"",skippedSuffix:""})):[{text:on("BackCompletionResult","text",e.text),suffixReplacementText:"",skippedSuffix:""}]}toCompleteResult(e){let n=this._extractCompletions(e),i=Yo("BackCompletionResult","unknown_blob_names/unknown_memory_names",e.unknown_blob_names??e.unknown_memory_names),s=e.checkpoint_not_found===void 0?!1:jg("BackCompletionResult","checkpoint_not_found",e.checkpoint_not_found,!1);return{completionItems:n,unknownBlobNames:i,checkpointNotFound:s,suggestedPrefixCharCount:e.suggested_prefix_char_count,suggestedSuffixCharCount:e.suggested_suffix_char_count,completionTimeoutMs:e.completion_timeout_ms}}toCheckpointBlobsResult(e){return{newCheckpointId:on("BackCheckpointBlobsResult","new_checkpoint_id",e.new_checkpoint_id)}}async complete(e,n,i,s,c,u,d,f,m,y,x,D,N){let A=this._configListener.config,L={recent_changes:m},B={model:A.modelName,prompt:n,suffix:i,path:s,blob_name:c,prefix_begin:u?.prefixBegin,cursor_position:u?.cursorPosition,suffix_end:u?.suffixEnd,lang:d,blobs:nl(f),recency_info:L,probe_only:D,sequence_id:this._sequenceId.next(),filter_threshold:A.completions.filterThreshold,edit_events:this.toFileDiffsPayload(y??[])};return await this.callApi(e,A,"completion",B,K=>this.toCompleteResult(K),void 0,x,N)}async checkpointBlobs(e){let n=this.createRequestId(),i=this._configListener.config,s={blobs:nl(e)},c=await this.callApi(n,i,"checkpoint-blobs",s,u=>this.toCheckpointBlobsResult(u));for(let u of this.getUniqueExtraURLs())(await this.callApi(n,i,"checkpoint-blobs",s,f=>this.toCheckpointBlobsResult(f),u)).newCheckpointId!==c.newCheckpointId&&this._logger.error(`Checkpoint blobs API returned different checkpoint IDs for ${u}`);return c}toVCSChangePayload(e){return{working_directory_changes:e.workingDirectory.map(n=>({before_path:n.beforePath,after_path:n.afterPath,change_type:n.changeType,head_blob_name:n.headBlobName,indexed_blob_name:n.indexedBlobName,current_blob_name:n.currentBlobName}))}}toFileDiffsPayload(e){return[]}getUniqueExtraURLs(){return new Set}toMemorizeResult(e){return{blobName:e.blob_name!==void 0?on("BackMemorizeResult","blob_name",e.blob_name):on("BackMemorizeResult","mem_object_name",e.mem_object_name)}}async memorize(e,n,i,s,c){let u=this.createRequestId(),d=this._configListener.config,f=await this.callApi(u,d,"memorize",{model:d.modelName,path:e,t:n,blob_name:i,metadata:s,timeout_ms:c},m=>this.toMemorizeResult(m));for(let m of this.getUniqueExtraURLs())await this.callApi(u,d,"memorize",{model:d.modelName,path:e,t:n,blob_name:i,metadata:s},y=>this.toMemorizeResult(y),m);return f}toBatchUploadResult(e){return{blobNames:e.blob_names}}async batchUpload(e){let n=this.createRequestId(),i=this._configListener.config;try{let s=await this.callApi(n,i,"batch-upload",{blobs:e.map(c=>({blob_name:c.blobName,path:c.pathName,content:c.text}))},this.toBatchUploadResult.bind(this));for(let c of this.getUniqueExtraURLs())await this.callApi(n,i,"batch-upload",{blobs:e.map(u=>({blob_name:u.blobName,path:u.pathName,content:u.text}))},this.toBatchUploadResult.bind(this),c);return s}catch(s){if(!vt.isAPIErrorWithStatus(s,4))throw s;let c=[];for(let u of e){let d=await this.memorize(u.pathName,u.text,u.blobName,u.metadata);c.push(d.blobName)}return{blobNames:c}}}toFindMissingResult(e){return{unknownBlobNames:Yo("BackFindMissingResult","unknown_memory_names",e.unknown_memory_names),nonindexedBlobNames:Yo("BackFindMissingResult","nonindexed_blob_names",e.nonindexed_blob_names)}}async findMissing(e){let n=this._configListener.config,i=this.createRequestId(),s=n.modelName,c=[...e].sort(),u=await this.callApi(i,n,"find-missing",{model:s,mem_object_names:c},d=>this.toFindMissingResult(d));for(let d of this.getUniqueExtraURLs()){let f=await this.callApi(i,n,"find-missing",{model:s,mem_object_names:c},m=>this.toFindMissingResult(m),d);u.unknownBlobNames=u.unknownBlobNames.concat(f.unknownBlobNames),u.nonindexedBlobNames=u.nonindexedBlobNames.concat(f.nonindexedBlobNames)}return u}async resolveCompletions(e){let n=this.createRequestId(),i=this._configListener.config;return await this.callApi(n,i,"resolve-completions",{client_name:"vscode-extension",resolutions:e})}toChatResult(e){let n=on("BackChatResult","text",e.text),i=e.unknown_blob_names===void 0?[]:Yo("BackChatResult","unknown_blob_names",e.unknown_blob_names),s=e.checkpoint_not_found===void 0?!1:jg("BackChatResult","checkpoint_not_found",e.checkpoint_not_found,!1),c=e.workspace_file_chunks===void 0?[]:e.workspace_file_chunks.map(d=>({charStart:vi("BackWorkspaceFileChunk","char_start",d.char_start),charEnd:vi("BackWorkspaceFileChunk","char_end",d.char_end),blobName:on("BackWorkspaceFileChunk","blob_name",d.blob_name)})),u=e.nodes===void 0?[]:e.nodes.map(d=>({id:vi("BackChatResultNode","id",d.id),type:vi("BackChatResultNode","type",d.type),content:on("BackChatResultNode","content",d.content)}));return{text:n,unknownBlobNames:i,checkpointNotFound:s,workspaceFileChunks:c,nodes:u}}async chat(e,n,i,s,c,u,d,f,m,y,x,D,N,A){let L=this._configListener.config,B={model:d??L.chat.model,path:N,prefix:x,selected_code:y,suffix:D,message:n,chat_history:i,lang:A,blobs:nl(s),user_guided_blobs:c,external_source_ids:u,context_code_exchange_request_id:m,vcs_change:{working_directory_changes:[]},recency_info_recent_changes:f,feature_detection_flags:{support_raw_output:!0}};return await this.callApi(e,L,"chat",B,K=>this.toChatResult(K),L.chat.url,12e4)}async chatStream(e,n,i,s,c,u,d,f,m,y,x,D,N,A,L,B,K,F){let P=this._configListener.config,U={model:d??P.chat.model,path:N,prefix:x,selected_code:y,suffix:D,message:n,chat_history:i,lang:A,blobs:nl(s),user_guided_blobs:c,context_code_exchange_request_id:m,vcs_change:{working_directory_changes:[]},recency_info_recent_changes:f,external_source_ids:u,disable_auto_external_sources:B,user_guidelines:K,workspace_guidelines:F,feature_detection_flags:{support_raw_output:!0}};return await Fn(()=>this.callApiStream(e,P,"chat-stream",U,this.toChatResult.bind(this),P.chat.url,12e4,L),this._logger,{initialMS:250,mult:2,maxMS:5e3,maxTries:5,maxTotalMs:5e3})}toModel(e){let n=e.completion_timeout_ms!==void 0?vi("BackModelInfo","completion_timeout_ms",e.completion_timeout_ms):void 0;return{name:on("BackModelInfo","name",e.name),suggestedPrefixCharCount:vi("BackModelInfo","suggested_prefix_char_count",e.suggested_prefix_char_count),suggestedSuffixCharCount:vi("BackModelInfo","suggested_suffix_char_count",e.suggested_suffix_char_count),completionTimeoutMs:n,internalName:e.internal_name&&on("BackModelInfo","internal_name",e.internal_name)}}toLanguage(e){let n=on("BackLanguageInfo","name",e.name),i=on("BackLanguageInfo","vscodeName",e.vscode_name);oa("BackLanguageInfo","extensions",e.extensions);let s=[];for(let c of e.extensions)s.push(on("BackLanguageInfo","extensions",c));return{name:n,vscodeName:i,extensions:s}}toGetModelsResult(e){let n=on("BackGetModelsResult","default_model",e.default_model);oa("BackGetModelsResult","models",e.models);let i=[];for(let u of e.models)i.push(this.toModel(u));let s=$g;if(e.feature_flags!==void 0){let u=e.feature_flags.git_diff_polling_freq_msec;u!==void 0&&u>0&&(s.gitDiff=!0,s.gitDiffPollingFrequencyMSec=u),e.feature_flags.small_sync_threshold!==void 0&&(s.smallSyncThreshold=e.feature_flags.small_sync_threshold),e.feature_flags.big_sync_threshold!==void 0&&(s.bigSyncThreshold=e.feature_flags.big_sync_threshold),e.feature_flags.enable_workspace_manager_ui_launch!==void 0&&(s.enableWorkspaceManagerUi=e.feature_flags.enable_workspace_manager_ui_launch),e.feature_flags.enable_instructions!==void 0&&(s.enableInstructions=e.feature_flags.enable_instructions),e.feature_flags.enable_smart_paste!==void 0&&(s.enableSmartPaste=e.feature_flags.enable_smart_paste),e.feature_flags.enable_smart_paste_min_version!==void 0&&(s.enableSmartPasteMinVersion=e.feature_flags.enable_smart_paste_min_version),e.feature_flags.enable_view_text_document!==void 0&&(s.enableViewTextDocument=e.feature_flags.enable_view_text_document),e.feature_flags.bypass_language_filter!==void 0&&(s.bypassLanguageFilter=e.feature_flags.bypass_language_filter),e.feature_flags.additional_chat_models!==void 0&&(s.additionalChatModels=e.feature_flags.additional_chat_models),e.feature_flags.enable_hindsight!==void 0&&(s.enableHindsight=e.feature_flags.enable_hindsight),e.feature_flags.max_upload_size_bytes!==void 0&&(s.maxUploadSizeBytes=e.feature_flags.max_upload_size_bytes),e.feature_flags.vscode_next_edit_min_version!==void 0&&(s.vscodeNextEditMinVersion=e.feature_flags.vscode_next_edit_min_version),e.feature_flags.vscode_flywheel_min_version!==void 0&&(s.vscodeFlywheelMinVersion=e.feature_flags.vscode_flywheel_min_version),e.feature_flags.vscode_external_sources_in_chat_min_version!==void 0&&(s.vscodeExternalSourcesInChatMinVersion=e.feature_flags.vscode_external_sources_in_chat_min_version),e.feature_flags.vscode_share_min_version!==void 0&&(s.vscodeShareMinVersion=e.feature_flags.vscode_share_min_version),e.feature_flags.max_trackable_file_count!==void 0&&(s.maxTrackableFileCount=e.feature_flags.max_trackable_file_count),e.feature_flags.max_trackable_file_count_without_permission!==void 0&&(s.maxTrackableFileCountWithoutPermission=e.feature_flags.max_trackable_file_count_without_permission),e.feature_flags.min_uploaded_percentage_without_permission!==void 0&&(s.minUploadedPercentageWithoutPermission=e.feature_flags.min_uploaded_percentage_without_permission),e.feature_flags.vscode_sources_min_version!==void 0&&(s.vscodeSourcesMinVersion=e.feature_flags.vscode_sources_min_version),e.feature_flags.vscode_chat_hint_decoration_min_version!==void 0&&(s.vscodeChatHintDecorationMinVersion=e.feature_flags.vscode_chat_hint_decoration_min_version),e.feature_flags.next_edit_debounce_ms!==void 0&&(s.nextEditDebounceMs=e.feature_flags.next_edit_debounce_ms),e.feature_flags.enable_completion_file_edit_events!==void 0&&(s.enableCompletionFileEditEvents=e.feature_flags.enable_completion_file_edit_events),e.feature_flags.vscode_enable_cpu_profile!==void 0&&(s.vscodeEnableCpuProfile=e.feature_flags.vscode_enable_cpu_profile),e.feature_flags.verify_folder_is_source_repo!==void 0&&(s.verifyFolderIsSourceRepo=e.feature_flags.verify_folder_is_source_repo),e.feature_flags.refuse_to_sync_home_directories!==void 0&&(s.refuseToSyncHomeDirectories=e.feature_flags.refuse_to_sync_home_directories),e.feature_flags.enable_file_limits_for_syncing_permission!==void 0&&(s.enableFileLimitsForSyncingPermission=e.feature_flags.enable_file_limits_for_syncing_permission),e.feature_flags.enable_chat_mermaid_diagrams!==void 0&&(s.enableChatMermaidDiagrams=e.feature_flags.enable_chat_mermaid_diagrams),e.feature_flags.enable_summary_titles!==void 0&&(s.enableSummaryTitles=e.feature_flags.enable_summary_titles),e.feature_flags.vscode_new_threads_menu_min_version!==void 0&&(s.vscodeNewThreadsMenuMinVersion=e.feature_flags.vscode_new_threads_menu_min_version),e.feature_flags.vscode_editable_history_min_version!==void 0&&(s.vscodeEditableHistoryMinVersion=e.feature_flags.vscode_editable_history_min_version),e.feature_flags.vscode_agent_mode_min_version!==void 0&&(s.vscodeAgentModeMinVersion=e.feature_flags.vscode_agent_mode_min_version)}let c=[];if(e.languages===void 0)c=Wg;else{oa("BackGetModelsResult","languages",e.languages),c=[];for(let u of e.languages)c.push(this.toLanguage(u))}return{defaultModel:n,models:i,languages:c,featureFlags:s}}async getModelConfig(){let e=this._configListener.config,n=this.createRequestId();return await this.callApi(n,e,"get-models",{},s=>this.toGetModelsResult(s))}async getAccessToken(e,n,i,s){let c=this._configListener.config,u=this.createRequestId(),d={grant_type:"authorization_code",client_id:c.oauth.clientID,code_verifier:i,redirect_uri:e,code:s};return await this.callApi(u,c,"token",d,f=>f.access_token,n)}async clientMetrics(e){let n=this._configListener.config,i=this.createRequestId();await this.callApi(i,n,"client-metrics",{metrics:e},void 0,void 0,t.defaultRequestTimeoutMs)}async reportClientCompletionTimelines(e){let n=this._configListener.config,i=this.createRequestId();await this.callApi(i,n,"/client-completion-timelines",{timelines:e},void 0,void 0,t.defaultRequestTimeoutMs)}},Gi=class extends Error{constructor(){super("The completion URL setting is invalid")}},Gg=class{_sequenceId=0;next(){return this._sequenceId++}};var ir=class{_disposables=[];add(e){if(e===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(e),e}addAll(...e){e.forEach(n=>this.add(n))}adopt(e){this._disposables.push(...e._disposables),e._disposables.length=0}dispose(){for(let e of this._disposables)e.dispose();this._disposables.length=0}},st=class{_disposables=new ir;_priorityDisposables=new ir;constructor(e=new ir,n=new ir){this._disposables.adopt(e),this._priorityDisposables.adopt(n)}addDisposable(e,n=!1){return n?this._priorityDisposables.add(e):this._disposables.add(e)}addDisposables(...e){this._disposables.addAll(...e)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}};var sj={Augment:null},aj="v",il=class t extends st{_config;_configChanged=new ue;_configMonitor;_logger=ve("AugmentConfigListener");constructor(){super(),this._configMonitor=new Ko("Config",this._logger),this._refreshConfig(),this.addDisposable(Rt.onDidChangeConfiguration(()=>this._refreshConfig()))}get config(){return this._config}get onDidChange(){return this._configChanged.event}_refreshConfig(){let e=this._config;this._config=t.normalizeConfig(this._getUserConfig()),this._configMonitor.update(this._config)&&this._configChanged.fire({previousConfig:e,newConfig:this._config})}static normalizeConfig(e){return{apiToken:e.advanced.apiToken,completionURL:e.advanced.completionURL,modelName:e.advanced.model,conflictingCodingAssistantCheck:e.conflictingCodingAssistantCheck,codeInstruction:{model:e.advanced.codeInstruction.model||void 0},chat:{url:e.advanced.chat.url||void 0,model:e.advanced.chat.model||void 0,stream:e.advanced.chat.stream??void 0,enableEditableHistory:e.advanced.chat.enableEditableHistory??!1,useRichTextHistory:e.advanced.chat.useRichTextHistory??!0,smartPasteUsePrecomputation:e.advanced.chat.smartPasteUsePrecomputation??!0,experimentalFullFilePaste:e.advanced.chat.experimentalFullFilePaste??!1,modelDisplayNameToId:e.advanced.chat.modelDisplayNameToId||sj,userGuidelines:e.chat.userGuidelines||""},autofix:{enabled:e.advanced.autofix.enabled,locationUrl:e.advanced.autofix.locationUrl||void 0,autofixUrl:e.advanced.autofix.autofixUrl||void 0},oauth:{clientID:e.advanced.oauth.clientID||aj,url:e.advanced.oauth.url||"https://auth.augmentcode.com"},enableUpload:e.advanced.enableWorkspaceUpload,enableShortcutsAboveSelectedText:e.enableShortcutsAboveSelectedText,shortcutsDisplayDelayMS:e.shortcutsDisplayDelayMS,enableEmptyFileHint:e.enableEmptyFileHint,enableBackgroundSuggestions:e.enableBackgroundSuggestions,enableGlobalBackgroundSuggestions:e.enableGlobalBackgroundSuggestions,showAllBackgroundSuggestionLineHighlights:e.showAllBackgroundSuggestionLineHighlights,enableDataCollection:e.advanced.enableDataCollection,enableDebugFeatures:e.advanced.enableDebugFeatures,enableReviewerWorkflows:e.advanced.enableReviewerWorkflows,completions:{enableAutomaticCompletions:e.completions.enableAutomaticCompletions,disableCompletionsByLanguage:new Set(e.completions.disableCompletionsByLanguage),enableQuickSuggestions:e.completions.enableQuickSuggestions,timeoutMs:e.advanced.completions.timeoutMs,maxWaitMs:e.advanced.completions.maxWaitMs,addIntelliSenseSuggestions:e.advanced.completions.addIntelliSenseSuggestions,filterThreshold:e.advanced.completions.filterThreshold},openFileManagerV2:{enabled:e.advanced.openFileManagerV2.enabled},nextEdit:{enabled:e.advanced.nextEdit.enabled,backgroundEnabled:e.advanced.nextEdit.backgroundEnabled,url:e.advanced.nextEdit.url,locationUrl:e.advanced.nextEdit.locationUrl||e.advanced.nextEdit.url,generationUrl:e.advanced.nextEdit.generationUrl||e.advanced.nextEdit.url,showInstructionTextbox:e.advanced.nextEdit.showInstructionTextbox,model:e.advanced.nextEdit.model,useDebounceMs:e.advanced.nextEdit.useDebounceMs,useCursorDecorations:e.advanced.nextEdit.useCursorDecorations,useSmallHover:e.advanced.nextEdit.useSmallHover,noDiffMode:e.advanced.nextEdit.noDiffMode,animateNoDiffMode:e.advanced.nextEdit.animateNoDiffMode,allowDuringDebugging:e.advanced.nextEdit.allowDuringDebugging,useMockResults:e.advanced.nextEdit.useMockResults,noDiffModeUseCodeLens:e.advanced.nextEdit.noDiffModeUseCodeLens,showDiffByDefault:e.advanced.nextEdit.showDiffByDefault},recencySignalManager:e.advanced.recencySignalManager,preferenceCollection:{enable:e.advanced.preferenceCollection.enable,enableRetrievalDataCollection:e.advanced.preferenceCollection.enableRetrievalDataCollection,enableRandomizedMode:e.advanced.preferenceCollection.enableRandomizedMode},vcs:{watcherEnabled:e.advanced.vcs.watcherEnabled},smartPaste:{url:e.advanced.smartPaste?.url,model:e.advanced.smartPaste?.model},instructions:{model:e.advanced.instructions?.model},integrations:{...e.advanced.integrations}}}_getUserConfig(){let e=Rt.getConfiguration("augment");return t.normalizeUserConfig(e)}static normalizeUserConfig(e){return{completions:{enableAutomaticCompletions:ft(e.enableAutomaticCompletions??e.completions?.enableAutomaticCompletions,!0),disableCompletionsByLanguage:e.disableCompletionsByLanguage||e.completions?.disableCompletionsByLanguage||[],enableQuickSuggestions:ft(e.completions?.enableQuickSuggestions,!0)},chat:{userGuidelines:e.chat?.userGuidelines||""},enableShortcutsAboveSelectedText:ft(e.enableShortcutsAboveSelectedText,!1),shortcutsDisplayDelayMS:cj(e.shortcutsDisplayDelayMS,2e3),enableEmptyFileHint:ft(e.enableEmptyFileHint,!0),enableBackgroundSuggestions:ft(e.enableBackgroundSuggestions,!0),enableGlobalBackgroundSuggestions:ft(e.enableGlobalBackgroundSuggestions,!1),showAllBackgroundSuggestionLineHighlights:ft(!e.advanced?.nextEdit?.noDiffMode,!e.advanced?.nextEdit?.noDiffMode),conflictingCodingAssistantCheck:ft(e.conflictingCodingAssistantCheck,!0),advanced:{apiToken:(e.advanced?.apiToken||e.apiToken||"").trim().toUpperCase(),completionURL:(e.advanced?.completionURL||e.completionURL||"").trim(),enableWorkspaceUpload:ft(e.advanced?.enableWorkspaceUpload,!0),model:e.advanced?.model||"",enableDebugFeatures:ft(e.advanced?.enableDebugFeatures,!1),enableReviewerWorkflows:ft(e.advanced?.enableReviewerWorkflows,!1),enableDataCollection:ft(e.advanced?.enableDataCollection,!1),codeInstruction:{model:e.advanced?.codeInstruction?.model||void 0},chat:{url:e.advanced?.chat?.url||void 0,model:e.advanced?.chat?.model||void 0,stream:e.advanced?.chat?.stream??void 0,enableEditableHistory:e.advanced?.chat?.enableEditableHistory,useRichTextHistory:e.advanced?.chat?.useRichTextHistory,smartPasteUsePrecomputation:e.advanced?.chat?.smartPasteUsePrecomputation,modelDisplayNameToId:e.advanced?.chat?.modelDisplayNameToId,experimentalFullFilePaste:e.advanced?.chat?.experimentalFullFilePaste},autofix:{enabled:ft(e.advanced?.autofix?.enabled,!1),locationUrl:e.advanced?.autofix?.locationUrl||void 0,autofixUrl:e.advanced?.autofix?.autofixUrl||void 0},oauth:{clientID:e.advanced?.oauth?.clientID,url:e.advanced?.oauth?.url},completions:{timeoutMs:e.advanced?.completions?.timeoutMs??800,maxWaitMs:e.advanced?.completions?.maxWaitMs??1600,addIntelliSenseSuggestions:e.advanced?.completions?.addIntelliSenseSuggestions??!0,filterThreshold:e.advanced?.completions?.filter_threshold??void 0},openFileManagerV2:{enabled:ft(e.advanced?.openFileManager?.useV2,!1)},nextEdit:{enabled:e.advanced?.nextEdit?.enabled,backgroundEnabled:ft(e.advanced?.nextEdit?.backgroundEnabled,!0),url:e.advanced?.nextEdit?.url,locationUrl:e.advanced?.nextEdit?.locationUrl,generationUrl:e.advanced?.nextEdit?.generationUrl,showInstructionTextbox:ft(e.advanced?.nextEdit?.showInstructionTextbox,!1),model:e.advanced?.nextEdit?.model,useDebounceMs:e.advanced?.nextEdit?.useDebounceMs,useCursorDecorations:ft(e.advanced?.nextEdit?.useCursorDecorations,!1),useSmallHover:ft(e.advanced?.nextEdit?.useSmallHover,!0),noDiffMode:e.advanced?.nextEdit?.noDiffMode,animateNoDiffMode:e.advanced?.nextEdit?.animateNoDiffMode,showDiffByDefault:e.advanced?.nextEdit?.showDiffByDefault,allowDuringDebugging:ft(e.advanced?.nextEdit?.allowDuringDebugging,!1),useMockResults:ft(e.advanced?.nextEdit?.useMockResults,!1),noDiffModeUseCodeLens:ft(e.advanced?.nextEdit?.noDiffModeUseCodeLens,!1)},recencySignalManager:{collectTabSwitchEvents:e.advanced?.recencySignalManager?.collectTabSwitchEvents??!1},preferenceCollection:{enable:ft(e.advanced?.preferenceCollection?.enable,!1),enableRetrievalDataCollection:ft(e.advanced?.preferenceCollection?.enableRetrievalDataCollection,!1),enableRandomizedMode:ft(e.advanced?.preferenceCollection?.enableRandomizedMode,!0)},vcs:{watcherEnabled:ft(e.advanced?.vcs?.watcherEnabled,!1)},smartPaste:{url:e.advanced?.smartPaste?.url,model:e.advanced?.smartPaste?.model},instructions:{model:e.advanced?.instructions?.model},integrations:{...e.advanced?.integrations}}}}};function ft(t,e){return t==null?e:typeof t=="string"?t.toLowerCase()!=="false":!!t}function cj(t,e){return t==null?e:typeof t=="string"?parseInt(t):t}var eT=Ze(Qo());var ca="augment.sessions",Zk=["email"],ol=class extends st{constructor(n,i){super();this._context=n;this._config=i;this.addDisposables(this.onDidChangeSession(s=>{this._isLoggedIn=!!s}),this._context.secrets.onDidChange(async s=>{s.key===ca&&this._sessionChangeEmitter.fire(await this.getSession())})),this._ready=this.initState()}_sessionChangeEmitter=new ue;_readyEmitter=new ue;_isLoggedIn;_ready;get onDidChangeSession(){return this._sessionChangeEmitter.event}get onReady(){return this._readyEmitter.event}get useOAuth(){let n=this._config.config;return!!n.oauth&&!!n.oauth.url&&!!n.oauth.clientID&&!n.apiToken&&!n.completionURL}async initState(){this._isLoggedIn=!!await this.getSession(),this._readyEmitter.fire()}get isLoggedIn(){return this._isLoggedIn}async saveSession(n,i){await this._context.secrets.store(ca,JSON.stringify({accessToken:n,tenantURL:i,scopes:Zk}))}async getSession(){let n=await this._context.secrets.get(ca);if(n){let i=JSON.parse(n);if((0,eT.isEqual)(i.scopes,Zk))return i}return null}async removeSession(){await this._context.secrets.get(ca)&&await this._context.secrets.delete(ca)}};var ua=require("crypto"),Kg=require("url");var sl="augment.oauth-state",uj=process.env.TEST_HOSTNAME??".augmentcode.com",lj=10,al=class{constructor(e,n,i,s){this._context=e;this._config=n;this._apiServer=i;this._authSession=s;this.authRedirectURI=ot.from({scheme:"vscode",authority:"augment.vscode-augment",path:"/auth/result"})}_logger=ve("OAuthFlow");authRedirectURI;async startFlow(){return this._logger.info("Creating new session..."),(await this.login()).toString(!0)}async createOAuthState(){this._logger.info("Creating OAuth state");let e=Qg((0,ua.randomBytes)(32)),n=Qg(dj(Buffer.from(e))),i=Qg((0,ua.randomBytes)(8)),s={codeVerifier:e,codeChallenge:n,state:i,creationTime:new Date().getTime()};return await this._context.secrets.store(sl,JSON.stringify(s)),this._logger.info("Created OAuth state"),s}async getOAuthState(){this._logger.info("Getting OAuth state");let e=await this._context.secrets.get(sl);if(e){let n=JSON.parse(e);if(new Date().getTime()-n.creationTime<lj*60*1e3)return n}return null}async removeOAuthState(){this._logger.info("Removing OAuth state"),await this._context.secrets.get(sl)&&await this._context.secrets.delete(sl)}async login(){try{let e=await this.createOAuthState();return this.generateAuthorizeURL(e)}catch(e){throw await this.removeOAuthState(),e}}generateAuthorizeURL(e){let n=new URLSearchParams({response_type:"code",code_challenge:e.codeChallenge,client_id:this._config.config.oauth.clientID||"",state:e.state,prompt:"login"}),i=new Kg.URL(`/authorize?${n.toString()}`,this._config.config.oauth.url);return ot.parse(i.toString())}async processAuthRedirect(e){let n=new URLSearchParams(e.query),i=n.get("state");if(!i)throw new Error("No state");let s=await this.getOAuthState();if(!s)throw new Error("No OAuth state found");if(await this.removeOAuthState(),s.state!==i)throw new Error("Unknown state");let c=n.get("error");if(c){let f=[`(${c})`],m=n.get("error_description");throw m&&f.push(m),new Error(`OAuth request failed: ${f.join(" ")}`)}let u=n.get("code");if(!u)throw new Error("No code");let d=n.get("tenant_url");if(d){if(!new Kg.URL(d).hostname.endsWith(uj))throw new Error("OAuth request failed: invalid OAuth tenant URL")}else throw new Error("No tenant URL");try{this._logger.info("Calling get access token to retrieved access token");let f=await this._apiServer.getAccessToken("",d,s.codeVerifier,u);await this._authSession.saveSession(f,d),this._logger.info("Successfully retrieved access token")}catch(f){throw this._logger.error(`Failed to get and save access token: ${pe(f)}`),new Error(`If you have a firewall, please add "${d}" to your allowlist.`)}}async handleAuthURI(e){try{await this.processAuthRedirect(e)}catch(n){this._logger.warn("Failed to process auth request:",n)}}async handleAuthJson(e){let n=JSON.parse(e);if(!await this.getOAuthState())throw new Error("No OAuth state found");let s=this.authRedirectURI.with({query:`code=${n.code}&state=${n.state}&tenant_url=${n.tenant_url}`});return await this.processAuthRedirect(s),"success"}};function Qg(t){return t.toString("base64").replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function dj(t){return(0,ua.createHash)("sha256").update(t).digest()}var BT=require("console"),UT=require("crypto");var cl=class t{constructor(e,n,i,s,c=t.chunkSize){this._apiServer=e;this._completionTimeoutMs=n,this._completionParams={prefixSize:i,suffixSize:s,chunkSize:c}}static chunkSize=1024;_completionTimeoutMs;_completionParams;createRequestId(){return this._apiServer.createRequestId()}get completionParams(){return this._completionParams}async complete(e,n,i,s,c,u,d,f,m,y,x,D,N){let A=await this._apiServer.complete(e,n,i,s,c,u,d,f,m,y,x,D,N);return A.completionTimeoutMs!==void 0&&(this._completionTimeoutMs=A.completionTimeoutMs),A.suggestedPrefixCharCount!==void 0&&(this._completionParams.prefixSize=A.suggestedPrefixCharCount),A.suggestedSuffixCharCount!==void 0&&(this._completionParams.suffixSize=A.suggestedSuffixCharCount),A.suggestedPrefixCharCount!==void 0&&(this._completionParams.chunkSize=t.chunkSize),A}};function fj(t,e){return n=>{let i=performance.now()-t;return e(Math.round(i)),n}}function tT(t,e){return(...n)=>{let i=performance.now(),s=fj(i,e),c=t(...n);return c instanceof Promise?c.then(s):s(c)}}function hj(t,e,n,i,s,c,u){return{blob_name:t,path:e,char_start:n,char_end:i,replacement_text:s,present_in_blob:c,expected_blob_name:u}}function nT(t){return t.map(e=>hj(e.blobName,e.pathName,e.origStart,e.origStart+e.origLength,e.text,e.uploaded,e.expectedBlobName))}var ul=class{constructor(e,n,i,s){this.completionText=e;this.suffixReplacementText=n;this.skippedSuffix=i;this.range=s}toString(){return`text: ${this.completionText}
    suffixReplacementText: ${this.suffixReplacementText}
    skippedSuffix: ${this.skippedSuffix}
    start: ${this.range.startOffset}
    end: ${this.range.endOffset}`}};var ll=class{constructor(e,n,i){this._extension=e;this._configListener=n;this._metricsReporter=i;this.generateCompletion=tT(this.generateCompletion.bind(this),s=>{this._metricsReporter.report({client_metric:"generate_completion_latency",value:s})})}_logger=ve("CompletionsModel");_completionSerial=0;async generateCompletion(e,n,i){let s=this._extension.workspaceManager;if(s===void 0)return;let c=s.completionServer,u=c.createRequestId(),d=s.safeResolvePathName(e.uri);if(d===void 0)return;let[f,m]=[d.rootPath,d.relPath],y=e.offsetAt(n),[x,D,N,A,L]=this._extractPrefixAndSuffix(e,y);y+=L;let B={prefixBegin:N,cursorPosition:y,suffixEnd:A},F=(await this._requestCompletion(s,c,u,e,x,D,B,d,i)).completionItems;if(F.length===0)return{completions:[],document:e,requestId:u,repoRoot:f,pathName:m,prefix:x,suffix:D,occuredAt:new Date,isReused:!1};F.length>1&&this._logger.warn("Multiple completions not supported, ignoring all but the first");let P=[],te=F[0];return te.skippedSuffix.includes(`
`)&&(this._logger.debug("Skipped suffix spans multiple lines, dropping it"),te.skippedSuffix="",te.suffixReplacementText=""),P.push(new ul(te.text,te.suffixReplacementText,te.skippedSuffix,{startOffset:e.offsetAt(n),endOffset:e.offsetAt(n)})),this._logger.debug(`Returning ${P.length} completion(s)`),{occuredAt:new Date,completions:P,document:e,requestId:u,repoRoot:f,pathName:m,prefix:x,suffix:D,isReused:!1}}async _requestCompletion(e,n,i,s,c,u,d,f,m){let y=s.languageId;if(this._configListener.config.completions.disableCompletionsByLanguage.has(y))throw new gi(`Language ${y} is disabled.`);let D=this._completionSerial++;this._logger.debug(`Requesting new completion - #${D} submitted; requestId: ${i}`);let N=e.translateRange(f,d.prefixBegin,d.suffixEnd),A=N===void 0?d:{prefixBegin:N.beginOffset,cursorPosition:d.cursorPosition,suffixEnd:N.endOffset},L=e.getContext(),B=L.blobs,K=this._getRecentChanges(L),F=void 0;try{let P=await n.complete(i,c,u,f.relPath,N?.blobName,A,y,B,K,F,void 0,void 0,m);return P.unknownBlobNames.length>0&&e.handleUnknownBlobs(L,P.unknownBlobNames),P.checkpointNotFound&&e.handleUnknownCheckpoint(i,B.checkpointId),gj(P.completionItems,u,this._logger),this._extension.updateModelInfo(P),P.completionItems=P.completionItems.filter(U=>(U.text+U.suffixReplacementText).length>0),P}catch(P){if(vt.isAPIErrorWithStatus(P,1))throw this._logger.debug(`Completion #${D} cancelled in back end; requestId ${i}`),new gi("Cancelled in back end");if(vt.isRetriableAPIError(P))throw this._logger.debug(`Completion #${D} retriable error on back end; requestId ${i}`),new gi("Retriable error on back end");let U=pe(P);throw this._logger.warn(`Completion #${D} failed: ${U}; requestId ${i}`),P}}_extractPrefixAndSuffix(e,n){let i=this._extension.modelInfo,s=i.suggestedPrefixCharCount,c=i.suggestedSuffixCharCount,[u,d]=[void 0,0];u!==void 0&&(n+=d);let f=Math.max(0,n-s),m=n+c;if(u!==void 0)throw new Error("Notebook support not implemented");let y=e.positionAt(f),x=e.positionAt(n),D=e.positionAt(n),N=e.positionAt(m),A=new bi(y,x),L=new bi(D,N),B=e.getText(A),K=e.getText(L);return[B,K,f,n+K.length,0]}_getRecentChanges(e){let n=e.recentChunks,i=e.lastChatResponse;if(i!==void 0){let s={seq:i.seq,uploaded:!1,repoRoot:"",pathName:"",blobName:"",text:i.text,origStart:0,origLength:0,expectedBlobName:""},c=n.findIndex(u=>u.seq<s.seq);c<0&&(c=n.length),n=n.slice(0,c).concat([s]).concat(n.slice(c))}return nT(n)}};function gj(t,e,n){for(let i of t)e.startsWith(i.skippedSuffix)||(n.warn(`Skipped suffix does not match the actual suffix. Skipped suffix: ${i.skippedSuffix}. First ${i.skippedSuffix.length} characters of suffix: ${e.substring(0,i.skippedSuffix.length)}`),i.suffixReplacementText="",i.skippedSuffix="")}var Zo=class{constructor(e){this._maxItems=e;this._items=new Array(e),this._emptySlots=e}_insertCount=0;_emptySlots;_items;get empty(){return this.length===0}get length(){return this._maxItems-this._emptySlots}addItem(e){this._items[this._insertCount%this._maxItems]=e,this._emptySlots=Math.max(this._emptySlots-1,0),this._insertCount++}shiftLeft(e){this._emptySlots=Math.min(this._emptySlots+e,this._maxItems)}shiftRight(e){this._insertCount-=Math.min(e,this.length),this._emptySlots=Math.min(this._emptySlots+e,this._maxItems)}at(e){if(e<0)return e<-this.length?void 0:this._items[(this._insertCount+e)%this._maxItems];if(!(e>=this.length))return this._items[(this._insertCount+this._emptySlots+e)%this._maxItems]}_normalizeSliceIdx(e){return e>=0?Math.min(e,this.length):Math.max(this.length+e,0)}_translateIdx(e){return(this._insertCount+this._emptySlots+e)%this._maxItems}slice(e,n){let i=this._normalizeSliceIdx(e??0),s=this._normalizeSliceIdx(n??this.length);if(i>s)return[];let c=s-i,u=this._translateIdx(i);return u+c<=this._maxItems?this._items.slice(u,u+c):this._items.slice(u).concat(this._items.slice(0,(u+c)%this._maxItems))}*[Symbol.iterator](){for(let e=0;e<this.length;e++)yield this.at(e)}clear(){this._emptySlots=this._maxItems}};var es=class{constructor(e,n,i,s){this._uploadMsec=i;this._uploadBatchSize=s;this._store=new Zo(n),this._logger=ve(e)}_logger;_store;_uploadIntervalId=void 0;_currentUploadPromise;report(e){this._store.addItem(e)}get uploadEnabled(){return this._uploadIntervalId!==void 0}enableUpload(){this.uploadEnabled||(this._uploadIntervalId=setInterval(()=>{this._currentUploadPromise===void 0&&(async()=>{try{this._currentUploadPromise=this._doUpload(),await this._currentUploadPromise}finally{this._currentUploadPromise=void 0}})()},this._uploadMsec))}async _doUpload(){if(this._store.length===0)return;let e=this._store.slice();this._store.clear();for(let n=0;n<e.length;n+=this._uploadBatchSize){let i=e.slice(n,n+this._uploadBatchSize);await Fn(async()=>{if(this.uploadEnabled)try{return this._logger.debug(`Uploading ${i.length} metric(s)`),await this.performUpload(i)}catch(s){throw this._logger.error(`Error uploading metrics: ${s} ${s instanceof Error?s.stack:""}`),s}},this._logger)}}disableUpload(){clearInterval(this._uploadIntervalId),this._uploadIntervalId=void 0}dispose(){this.disableUpload()}};var dl=class t extends es{constructor(n){super("ClientMetricsReporter",t.defaultMaxRecords,t.defaultUploadMsec,t.defaultBatchSize);this._apiServer=n}static defaultMaxRecords=1e4;static defaultBatchSize=500;static defaultUploadMsec=1e4;performUpload(n){return this._apiServer.clientMetrics(n)}};function Jg(t){let e=Math.floor(t/1e3),n=t%1e3*1e6;return[e,n]}var fl=class t extends es{constructor(n,i,s,c){super("CompletionAcceptanceReporter",i??t.defaultMaxRecords,s??t.defaultUploadMsec,c??t.defaultBatchSize);this._apiServer=n}static defaultMaxRecords=1e4;static defaultBatchSize=1e3;static defaultUploadMsec=1e4;reportResolution(n,i,s,c){let[u,d]=Jg(i),[f,m]=Jg(s);this.report({request_id:n,emit_time_sec:u,emit_time_nsec:d,resolve_time_sec:f,resolve_time_nsec:m,accepted_idx:c??-1})}performUpload(n){return this._apiServer.resolveCompletions(n)}};var hl=class t{constructor(e){this._extensionContext=e}static storageSubDir="augment-global-state";update(e,n){return this._extensionContext.globalState.update(e,n)}get(e){return this._extensionContext.globalState.get(e)}async save(e,n,i){await this._ensureStorageUriExists(i);let s=this._getFileUri(e,i);return await Xu(s.fsPath,JSON.stringify(n))}async load(e,n){await this._ensureStorageUriExists(n);let i=this._getFileUri(e,n);try{let s=await Jo(i.fsPath);return JSON.parse(s)}catch{return}}async _ensureStorageUriExists(e){let n=this._getStorageUri(e);await Wk(n.fsPath)||await ia(n.fsPath)}_getStorageUri(e){return e?.uniquePerWorkspace&&this._extensionContext.storageUri?Tt.joinPath(this._extensionContext.storageUri,t.storageSubDir):Tt.joinPath(this._extensionContext.globalStorageUri,t.storageSubDir)}_getFileUri(e,n){return Tt.joinPath(this._getStorageUri(n),`${e}.json`)}};var iT=Ze(require("crypto")),da=2023102300,la=class extends Error{constructor(e){super(`content exceeds maximum size of ${e}`)}},ts=class{constructor(e){this.maxBlobSize=e}_textEncoder=new TextEncoder;_hash(e,n){let i=iT.createHash("sha256");return i.update(e),i.update(n),i.digest("hex")}calculateOrThrow(e,n,i=!0){if(typeof n=="string"&&(n=this._textEncoder.encode(n)),i&&n.length>this.maxBlobSize)throw new la(this.maxBlobSize);return this._hash(e,n)}calculate(e,n){try{return this.calculateOrThrow(e,n,!0)}catch{return}}calculateNoThrow(e,n){return this.calculateOrThrow(e,n,!1)}};var OT=Ze(Qo());var Xg=class{async read(e){try{return await jk(e)}catch{return}}stat(e){try{return Vi(e)}catch{return}}};function oT(){return new Xg}var sT=Ze(require("crypto"));function aT(t){let e=sT.createHash("sha256");return e.update(t),e.digest("hex")}var ap=Ze(bT());var fa=class{accepted=!0},yi=class{accepted=!1};var at=Ze(require("node:path"));var ha=at.posix.sep,Ij=new RegExp("/\\/g");function _T(t){return at.isAbsolute(t)}function Mj(t){return at.sep==="\\"?t.replace(Ij,ha):t}function vT(t){return Mj(t)}function yT(t){return t.with({path:at.dirname(t.fsPath)})}function pl(t){let e=at.dirname(t);return e==="."?"":Aj(e)}function Aj(t){return t.endsWith(ha)?t:t+ha}function ns(t){for(;;){if(t===at.sep||t===ha||!t.endsWith(ha)&&!t.endsWith(at.sep))return t;t=t.slice(0,-1)}}function ga(t,e){let n=typeof t=="string"?t:t.fsPath,i=typeof e=="string"?e:e.fsPath;return at.relative(n,i)}function Ur(t,e){let n=ga(t,e);return n==="."?"":n.length>0&&!n.endsWith(at.sep)?n+at.sep:n}function et(t,e,n=!1){let i=at.join(t,e);return n&&!i.endsWith(at.sep)&&(i+=at.sep),i}function pa(t,e){let n=ga(t,e);if(!(n===".."||n.startsWith(".."+at.sep)||n.startsWith(".."+at.posix.sep)))return n}function or(t,e){return pa(t,e)!==void 0}function ml(t,e){let n=ga(t,e);return n===""||n==="."||n==="."+at.sep||n==="."+at.posix.sep}async function bl(t,e,n){let i=typeof t=="string"?ot.file(t):t;for(;;){try{let c=Tt.joinPath(i,e),u=await $k(c.fsPath);if(n===void 0||u.type===n)return i}catch{}let s=at.dirname(i.fsPath);if(at.relative(s,i.fsPath).length===0)return;i=i.with({path:s})}}function Nj(t){return!1}function Jt(t){return ns(t.fsPath)}function wT(t){return t.fsPath}function $r(t){if(t.scheme==="file"||t.scheme==="untitled"||Nj(t))return Jt(t)}var wi=class extends fa{explicit=!1;format(){return"Tracked"}},rp=class extends fa{constructor(n){super();this.ignoreSourceName=n}explicit=!0;format(){return`Tracked (${this.ignoreSourceName})`}},ip=class extends yi{constructor(n){super();this.ignoreSourceName=n}explicit=!0;format(){return`Not tracked (${this.ignoreSourceName})`}},_l=class{constructor(e,n,i,s){this.dirName=e;this.ignoreSource=n;this.rules=i;this.next=s}},op=class t{constructor(e,n,i){this._rootUri=e;this._ignoreSource=n;this._top=i}static async buildNew(e,n,i){let s=new Array;if(Ur(i,n)!==""){let u=n;for(;;){u=yT(u);let d=Ur(i,u),f=await e.getRules(u);if(f&&s.push([d,e,f]),d==="")break}}let c;for(let u=s.length-1;u>=0;u--){let[d,f,m]=s[u];c=new _l(d,f,m,c)}return new t(i,e,c)}async buildAtop(e,n){let i=await this._ignoreSource.getRules(e,n);if(i===void 0)return this;let s=Ur(this._rootUri,e),c=new _l(s,this._ignoreSource,i,this._top);return new t(this._rootUri,this._ignoreSource,c)}getPathInfo(e){for(let n=this._top;n!==void 0;n=n.next){if(!or(n.dirName,e))throw new Error(`candidatePath "${e}" is not below ignore file's parent "${n.dirName}"`);let i=e.slice(n.dirName.length),s=n.rules.test(i);if(s.ignored)return new ip(n.ignoreSource.getName(Tt.joinPath(this._rootUri,n.dirName)));if(s.unignored)return new rp(n.ignoreSource.getName(Tt.joinPath(this._rootUri,n.dirName)))}return new wi}},sp=class t{constructor(e,n){this._ignoreSources=e;this._ignoreStacks=n}static async buildNew(e,n,i){let s=new Array;for(let c of e){let u=await op.buildNew(c,n,i);s.push(u)}return new t(e,s)}async buildAtop(e,n){let i=new Array,s=0;for(let c=0;c<this._ignoreStacks.length;c++){let u=this._ignoreStacks[c],d=await u.buildAtop(e,n);d!==u&&s++,i.push(d)}return s===0?this:new t(this._ignoreSources,i)}getPathInfo(e){for(let n=this._ignoreStacks.length-1;n>=0;n--){let s=this._ignoreStacks[n].getPathInfo(e);if(s.explicit)return s}return new wi}};async function qj(t,e){let n=Tt.joinPath(t,e);try{let i=await Jo(n.fsPath),s=(0,ap.default)({ignorecase:!1});return s.add(i),s}catch{}}var rs=class{constructor(e=[]){this._ignoreSources=e}async build(e,n){return await sp.buildNew(this._ignoreSources,e,n)}},ma=class{constructor(e){this.filename=e}getName(e){return wT(Tt.joinPath(e,this.filename))}async getRules(e,n){if(!(n!==void 0&&!(n.find(([s,c])=>c==="File"&&this.filename===s)!==void 0)))return qj(e,this.filename)}},vl=class{constructor(e){this._sourceFolderRootPath=e}getName(){return"default Augment rules"}getRules(e){return new Promise(n=>{if(Jt(e)!==this._sourceFolderRootPath)n(void 0);else{let i=(0,ap.default)({ignorecase:!1});i.add([".git","*.pem","*.key","*.pfx","*.p12","*.jks","*.keystore","*.pkcs12","*.crt","*.cer","id_rsa","id_ed25519","id_ecdsa","id_dsa",".augment-guidelines",".env"]),n(i)}})}};var yl=class{constructor(e){this.name=e}start=Date.now();increments=[];charge(e){this.increments.push({name:e,end:Date.now()})}*[Symbol.iterator](){let e=this.start;for(let{name:n,end:i}of this.increments)yield[n,i-e],e=i;yield["total",e-this.start]}format(){return(this.name?`${this.name}:
`:"")+Array.from(this).map(([n,i])=>`  - ${n}: ${i} ms`).join(`
`)}},wl=class{_value=0;_add(e=1){this._value+=e}_invalidate(){this._value=Number.NaN}get value(){return this._value}},cp=class extends wl{increment(e=1){this._add(e)}},up=class extends wl{_start=void 0;start(){this._start=Date.now()}stop(){this._start===void 0?this._invalidate():this._add(Date.now()-this._start)}},is=class{constructor(e){this.name=e}counters=new Map;timings=new Map;counterMetric(e){let n=this.counters.get(e);return n===void 0&&(n=new cp,this.counters.set(e,n)),n}timingMetric(e){let n=this.timings.get(e);return n===void 0&&(n=new up,this.timings.set(e,n)),n}format(){let e=this.name?`${this.name}:`:"",n=Array.from(this.counters.entries()).map(([c,u])=>`  - ${c}: ${u.value}`).join(`
`),i="  - timing stats:",s=Array.from(this.timings.entries()).map(([c,u])=>`    - ${c}: ${u.value} ms`).join(`
`);return e+`
`+n+`
`+i+`
`+s}};var ST=Ze(require("os"));function xT(t){let e=ST.default.homedir();return ml(t,e)}var ba=Ze(require("node:path"));async function xl(t,e,n,i){let s=await n.build(t,e),c=new Map;c.set("",s),c.set(".",s);let u=200,d=Date.now(),f=[];f.push([t,s]);let m;for(;(m=f.pop())!==void 0;){Date.now()-d>=u&&(await new Promise(B=>setTimeout(B,0)),d=Date.now());let[x,D]=m,N=Ur(e,x),A=ra(x.fsPath),L=await D.buildAtop(x,A);L!==D&&c.set(N,L);for(let[B,K]of A){if(Date.now()-d>=u&&(await new Promise(ye=>setTimeout(ye,0)),d=Date.now()),B==="."||B===".."||K!=="Directory")continue;let P=Tt.joinPath(x,B),U=et(N,B,!0);L.getPathInfo(U).accepted&&f.push([P,L])}}return new dp(c,i)}var os=class{constructor(e,n,i,s){this._name=e;this._startUri=n;this._rootUri=i;this._pathFilter=s;if(!ba.isAbsolute(n.fsPath))throw new Error(`PathIterator[${this._name}]: startUri ${this._name} must contain an absolute pathname`);if(!ba.isAbsolute(i.fsPath))throw new Error(`PathIterator[${this._name}]: rootUri ${i.toString()} must contain an absolute pathname`);if(!or(Jt(i),Jt(n)))throw new Error(`PathIterator[${this._name}]: startUri ${Jt(this._startUri)} must be inside rootUri ${Jt(this._rootUri)}`);this._logger.verbose(`Created PathIterator for startUri ${this._startUri.fsPath}, rootUri ${this._rootUri.fsPath}`)}stats=new is("Path metrics");_logger=ve("PathIterator");_dirsEmitted=this.stats.counterMetric("directories emitted");_filesEmitted=this.stats.counterMetric("files emitted");_otherEmitted=this.stats.counterMetric("other paths emitted");_totalEmitted=this.stats.counterMetric("total paths emitted");_readDirMs=this.stats.timingMetric("readDir");_filterMs=this.stats.timingMetric("filter");_yieldMs=this.stats.timingMetric("yield");_totalMs=this.stats.timingMetric("total");async*[Symbol.asyncIterator](){this._totalMs.start();let e=200,n=Date.now(),i=new Array;i.push(this._startUri);let s;for(;(s=i.pop())!==void 0;){Date.now()-n>=e&&(await new Promise(m=>setTimeout(m,0)),n=Date.now());let u=Ur(this._rootUri,s),d=this._pathFilter.makeLocalPathFilter(u);this._readDirMs.start();let f=ra(s.fsPath);this._readDirMs.stop();for(let[m,y]of f){if(Date.now()-n>=e&&(await new Promise(B=>setTimeout(B,0)),n=Date.now()),m==="."||m==="..")continue;this._filterMs.start();let D=Tt.joinPath(s,m),N=et(u,m,y==="Directory"),A=d.getPathInfo(N,y);this._filterMs.stop();let L=N;y==="File"?this._filesEmitted.increment():y==="Directory"?(L=ns(N),this._dirsEmitted.increment()):this._otherEmitted.increment(),this._totalEmitted.increment(),this._yieldMs.start(),yield[D,L,y,A],this._yieldMs.stop(),y==="Directory"&&A.accepted&&i.push(D)}}this._totalMs.stop()}},lp=class extends yi{constructor(n){super();this.extension=n}format(){return`Unsupported file extension (${this.extension})`}},Sl=class{_fileExtensions;constructor(e){if(e){this._fileExtensions=new Set;for(let n of e)this._fileExtensions.add(n.toLowerCase())}else this._fileExtensions=void 0}acceptsPath(e,n="File"){return this.getPathInfo(e,n).accepted}getPathInfo(e,n="File"){if(n==="File"){let s=ba.extname(e);if(this._fileExtensions!==void 0&&!this._fileExtensions.has(s.toLowerCase()))return new lp(s)}else n==="Directory"&&!e.endsWith("/")&&(e+="/");let i=this._getIgnoreStack(pl(e));return i===void 0?new wi:i.getPathInfo(e)}},dp=class extends Sl{constructor(n,i){super(i);this._ignorePathMap=n}makeLocalPathFilter(n){let i=this._getIgnoreStack(n);return new fp(i,this._fileExtensions)}_getIgnoreStack(n){if(_T(n))throw new Error(`Absolute path ${n} passed to PathFilter`);let i=n;for(let s=0;s<1e4;s++){let c=this._ignorePathMap.get(i);if(c)return c;if(pl(i)===i)return;i=pl(i)}throw new Error(`Too-deep or malformed directory name ${n}`)}},fp=class extends Sl{constructor(n,i){super(i);this._ignoreStack=n}_getIgnoreStack(n){return this._ignoreStack}};var Pl=class t{static defaultStartSeq=1e4;_next;constructor(e=t.defaultStartSeq){this._next=e}next(){return this._next++}};var hp=class extends Error{constructor(){super("SingletonExecutor has been disposed")}},Cl=class t extends st{constructor(n){super();this._execute=n;this.addDisposable({dispose:()=>this._stopping=!0})}static _disposedError=new hp;_nextExecutionScheduled=!1;_kickPromise=Promise.resolve();_stopping=!1;kick(){return this._nextExecutionScheduled?this._kickPromise:(this._nextExecutionScheduled=!0,this._kickPromise=this._kickPromise.then(async()=>(this._nextExecutionScheduled=!1,this._stopping?Promise.reject(t._disposedError):this._execute())),this._kickPromise)}};var gp=class{_items=new Map;_inProgress=!1;_stopping=!1;dispose(){this._stopping=!0}get size(){return this._items.size}cancel(e){this._items.delete(e)}_insert(e,n,i=!1){let s=this._items.has(e);return!i&&s?!1:(this._items.set(e,n),!s)}async kick(){if(!(this._inProgress||this._stopping||this._items.size===0)){for(this._inProgress=!0;this._items.size>0&&!this._stopping;){let e=this._items;this._items=new Map;for(let n of e){try{await this._processEntry(n)}catch{}if(this._stopping)break}}this._inProgress=!1,await this._processEntry(void 0)}}};var Hn=class extends gp{constructor(n){super();this._processItem=n}insert(n,i,s=!1){return this._insert(n,i,s)}get(n){return this._items.get(n)}async _processEntry(n){return this._processItem(n)}},Wr=class{constructor(e){this._processItem=e}_keys=new Set;_items=new Array;_inProgress=!1;_stopping=!1;get size(){return this._items.length}dispose(){this._stopping=!0}insert(e){return this._keys.has(e)?!1:(this._keys.add(e),this._items.push(e),!0)}async kick(){if(!(this._inProgress||this._stopping)){for(this._inProgress=!0;this._items.length>0&&!this._stopping;){let e=this._items;this._keys.clear(),this._items=new Array;for(let n of e){try{await this._processItem(n)}catch{}if(this._stopping)break}}this._inProgress=!1,await this._processItem(void 0)}}},sr=class{constructor(e,n){this._toKick=e;this._intervalId=setInterval(this._kick.bind(this),n)}_intervalId;_kick(){this._toKick.kick()}dispose(){this._intervalId!==void 0&&clearInterval(this._intervalId)}};var Oj=Object.freeze({git:{name:".git",type:"Directory"}});async function PT(t){for(let[e,n]of Object.entries(Oj)){let i=await bl(t,n.name,n.type);if(i!==void 0)return{root:i,toolName:e}}}var _a=class extends Error{constructor(n,i){let s=n instanceof Error?n.message:`${n}`;super(`WorkQueueItemFailed: ${s}, retry = ${i}`);this.toThrow=n;this.retry=i}},pp=class{throwError(e,n){throw new _a(e,n)}},mp=class{progressReporter;completedItems=0;totalItems;constructor(e,n){this.progressReporter=e,this.totalItems=n,this.update(0,this.totalItems)}update(e,n){this.completedItems+=e,this.totalItems=this.completedItems+n,this.progressReporter.update(this.completedItems,this.totalItems)}cancel(){this.progressReporter.cancel()}},bp=class{constructor(e,n){this.item=e;this.process=n}itemCount(){return 1}start(e){return this.process(this.item,e)}},_p=class{constructor(e,n){this.items=e;this.process=n}itemCount(){return this.items.size}start(e){return this.process(this.items,e)}},kl=class{queue=new Set;get size(){return this.queue.size}add(e){this.queue.add(e)}delete(e){this.queue.delete(e)}},vp=class extends kl{constructor(n){super();this.process=n}dequeue(){let n=this.queue.values().next();if(n.done)throw new Error("Cannot dequeue from empty queue");let i=n.value;return this.queue.delete(i),new bp(i,this.process)}},yp=class extends kl{constructor(n,i){super();this.process=n;this._maxBatchSize=i}dequeue(){let n;if(this._maxBatchSize===void 0||this.queue.size<=this._maxBatchSize)n=this.queue,this.queue=new Set;else{n=new Set;let i=this.queue.values();for(let s=0;s<this._maxBatchSize;s++){let c=i.next();if(c.done)break;let u=c.value;this.queue.delete(u),n.add(u)}}return new _p(n,this.process)}},Tl=class t{constructor(e,n,i,s){this.name=e;this.shutdownError=i;if("processOne"in n)this.queue=new vp(n.processOne);else if("processBatch"in n)this.queue=new yp(n.processBatch,n.maxBatchSize);else throw new Error("Invalid processor type");this.backoffParams=s||t.defaultBackoffParams,this.logger=ve(`WorkQueue[${e}]`),this.errorHandler=new pp}static queueStatusChanged="QueueStatusChanged";static itemFailed="ItemFailed";static defaultBackoffParams={initialMS:100,mult:2,maxMS:3e4};eventEmitters={[t.queueStatusChanged]:new ue,[t.itemFailed]:new ue};queue;countReporters=new Set;progressReporters=new Set;errorHandler;backoffParams;itemsInProgress=0;stopping=!1;logger;stop(){this.stopping=!0,this.update(),this.notifyStatusChanged()}add(e){this.stopping||(this.queue.add(e),this.update(),this.kick())}delete(e){this.stopping||(this.queue.delete(e),this.update())}size(){return this.queue.size+this.itemsInProgress}reportQueueSize(e){let n=this.size();e.update(n),this.countReporters.add(e)}awaitEmpty(e,n=!0){if(this.stopping)return Promise.resolve();let i=this.size();return i===0?Promise.resolve():(e&&this.progressReporters.add(new mp(e,i)),new Promise((s,c)=>{let u=[];function d(){for(let f of u)f.dispose()}u.push(this.eventEmitters[t.queueStatusChanged].event(()=>{d(),this.stopping?c(this.shutdownError):s()})),n&&u.push(this.eventEmitters[t.itemFailed].event(f=>{d(),c(f)}))}))}update(e=0){if(this.stopping){for(let n of this.countReporters)n.cancel();for(let n of this.progressReporters)n.cancel()}else{this.itemsInProgress-=e;let n=this.size();for(let i of this.countReporters)i.update(n);for(let i of this.progressReporters)i.update(e,n)}}notifyStatusChanged(){this.eventEmitters[t.queueStatusChanged].fire(null),this.progressReporters.clear()}notifyItemFailed(e){let n=e instanceof _a?e.toThrow:e;this.eventEmitters[t.itemFailed].fire(n)}delay(e){return new Promise(n=>setTimeout(n,e))}async kick(){if(!this.itemsInProgress){for(;!this.stopping&&this.queue.size!==0;){let e=this.queue.dequeue();this.itemsInProgress=e.itemCount();let n=0,i=0;do{try{await e.start(this.errorHandler),i&&this.logger.debug(`item succeeded; retries = ${i}`);break}catch(s){if(this.notifyItemFailed(s),!(s instanceof _a&&s.retry)){this.logger.debug(`item failed, not retrying; retries = ${i}`);break}}this.logger.debug(`item failed, retrying in ${n} ms; retries = ${i}`),await this.delay(n),this.logger.debug("retrying"),n===0?n=this.backoffParams.initialMS:n=Math.min(n*this.backoffParams.mult,this.backoffParams.maxMS),i++}while(!this.stopping);this.stopping||this.update(this.itemsInProgress)}this.notifyStatusChanged()}}};function ss(t,e){let n=new Set(e);return t.filter(i=>!n.has(i))}var Rl=class t extends st{static defaultCheckpointThreshold=1e3;_checkpointId;_checkpointBlobNames=new Map;_toAdd=new Map;_toRemove=new Set;_apiServer;_logger;_checkpointQueue;_checkpointThreshold;_maxCheckpointBatchSize=1e4;_featureFlagManager;_onContextChange=new ue;onContextChange=this._onContextChange.event;get _flags(){return this._featureFlagManager.currentFlags}constructor(e,n,i,s){super(),this._logger=ve("BlobsCheckpointManager"),this._checkpointId=void 0,this._apiServer=e,this._featureFlagManager=n,this._checkpointThreshold=s??t.defaultCheckpointThreshold,this.addDisposable(i(c=>{this.updateBlob(c.absPath,c.prevBlobName,c.newBlobName)})),this._checkpointQueue=new Tl("checkpoint",{processOne:async(c,u)=>await this._checkpoint(c,u)}),this._logger.info(`BlobsCheckpointManager created. checkpointThreshold: ${this._checkpointThreshold}`)}refBlob(e){let n=this._checkpointBlobNames.get(e);n!==void 0?(this._checkpointBlobNames.set(e,n+1),n===0&&this._toRemove.delete(e)):this._toAdd.set(e,(this._toAdd.get(e)??0)+1)}derefBlob(e){!this.derefFromCheckpoint(e)&&!this.derefFromAdded(e)&&this._logger.error(`derefBlob: blob ${e} not found in checkpoint or toAdd`)}derefFromCheckpoint(e){let n=this._checkpointBlobNames.get(e);return n===void 0||n<=0?(n!==void 0&&this._logger.error(`derefFromCheckpoint: blob ${e} has reference count ${n}. In toRemove? ${this._toRemove.has(e)}`),!1):(this._checkpointBlobNames.set(e,n-1),n===1&&this._toRemove.add(e),!0)}derefFromAdded(e){let n=this._toAdd.get(e);return n===void 0?!1:n<=1?(this._toAdd.delete(e),n===1):(this._toAdd.set(e,n-1),!0)}getCheckpointedBlobNames(){return Array.from(this._checkpointBlobNames.keys())}getContext(){return{checkpointId:this._checkpointId,addedBlobs:Array.from(this._toAdd.keys()),deletedBlobs:Array.from(this._toRemove)}}getContextAdjusted(e,n){let i=new Set(this._toAdd.keys()),s=new Set(this._toRemove);for(let c of e)this._checkpointBlobNames.has(c)||i.add(c),s.delete(c);for(let c of n)this._checkpointBlobNames.has(c)&&s.add(c),i.delete(c);return{checkpointId:this._checkpointId,addedBlobs:Array.from(i),deletedBlobs:Array.from(s)}}blobsPayload(e){let n=this.getCheckpointedBlobNames(),i=ss(e,n),s=ss(n,e);return{checkpointId:this._checkpointId,addedBlobs:i,deletedBlobs:s}}expandBlobs(e){if(e.checkpointId===void 0)return e.addedBlobs;if(e.checkpointId!==this._checkpointId)throw new Error(`expandBlobs: checkpointId mismatch: ${e.checkpointId} != ${this._checkpointId}`);let n=this.getCheckpointedBlobNames();if(n.push(...e.addedBlobs),e.deletedBlobs.length>0){let i=new Set(e.deletedBlobs);return n.filter(s=>!i.has(s))}return n}validateMatching(e,n,i=!1){if(e.checkpointId!==n.checkpointId)return this._logger.error(`checkpointId mismatch: ${e.checkpointId} vs ${n.checkpointId}`),!1;let s=!0,c=ss(e.addedBlobs,n.addedBlobs),u=ss(n.addedBlobs,e.addedBlobs);return(c.length>0||u.length>0)&&(s=!1,this._logger.error(`addedBlobs mismatch: -${c.length}/+${u.length}`),i&&(this._logger.error(`left-added: ${c.slice(0,5).join(",")}`),this._logger.error(`right-added: ${u.slice(0,5).join(",")}`))),c=ss(e.deletedBlobs,n.deletedBlobs),u=ss(n.deletedBlobs,e.deletedBlobs),(c.length>0||u.length>0)&&(s=!1,this._logger.error(`deletedBlobs mismatch: -${c.length}/+${u.length}`),i&&(this._logger.error(`left-deleted: ${c.slice(0,5).join(",")}`),this._logger.error(`right-deleted: ${u.slice(0,5).join(",")}`))),s}updateBlob(e,n,i){this._logger.verbose(`notifyBlobChange ${e}: ${n} to ${i}`),i&&i!==n&&this.refBlob(i),n&&i!==n&&this.derefBlob(n),this._toAdd.size+this._toRemove.size>=this._checkpointThreshold&&this._checkpointQueue.size()===0&&this._queueCheckpoint()}resetCheckpoint(){for(let[e,n]of this._checkpointBlobNames)n>0?this._toAdd.set(e,n):this._toRemove.delete(e)||this._logger.warn(`blob with 0 references was not found in toRemove: ${e}`);for(let e of this._toRemove)this._logger.warn(`blob in toRemove was not found in checkpoint: ${e}`);this._toRemove.clear(),this._checkpointId=void 0,this._checkpointBlobNames.clear(),this._onContextChange.fire(this.getContext())}async awaitEmptyQueue(){await this._checkpointQueue.awaitEmpty(void 0,!1)}async _checkpoint(e,n){let{checkpointId:i,addedBlobs:s,deletedBlobs:c}=e;this._logger.debug(`Begin checkpoint of working set into ${i}`),this._logger.debug(`add ${s.length} blobs, remove ${c.length} blobs into ${i}`);let u={newCheckpointId:""};try{u=await this._apiServer.checkpointBlobs(e)}catch(d){let f=d instanceof Error?d.message:`${d}`,m=this._checkpointId?this._checkpointId:"{initial}";vt.isAPIErrorWithStatus(d,5)||vt.isAPIErrorWithStatus(d,4)?(this._logger.warn(`checkpoint-blobs from ${m} failed with invalid argument: ${f}. Recreating checkpoint.`),this.resetCheckpoint(),this._queueCheckpoint(),n.throwError(d,!1)):(this._logger.error(`checkpoint-blobs failed with error: ${f}.`),n.throwError(d,!1))}if(i!==this._checkpointId)this._logger.warn(`original checkpointId ${i} does not match current checkpointId ${this._checkpointId}. Abandoning new checkpoint.`);else{this._logger.debug(`checkpointId ${i} advanced to ${u.newCheckpointId}`),this._checkpointId=u.newCheckpointId;for(let d of s){let f=this._toAdd.get(d);f===void 0?(this._checkpointBlobNames.set(d,0),this._toRemove.add(d)):(this._checkpointBlobNames.set(d,f),this._toAdd.delete(d))}for(let d of c){let f=this._checkpointBlobNames.get(d);f===void 0?this._logger.warn(`In _checkpoint: deleted blob ${d} not found in checkpoint`):f>0&&this._toAdd.set(d,f),this._checkpointBlobNames.delete(d),this._toRemove.delete(d)}this._onContextChange.fire(this.getContext())}this._toAdd.size+this._toRemove.size>=this._checkpointThreshold&&(this._logger.debug(`starting a new round of checkpointing due to size ${this._toAdd.size} + ${this._toRemove.size}`),this._queueCheckpoint())}_queueCheckpoint(){this._logger.debug("queue checkpoint");let e=Array.from(this._toAdd.keys()).slice(0,this._maxCheckpointBatchSize),n=Array.from(this._toRemove).slice(0,this._maxCheckpointBatchSize),i={checkpointId:this._checkpointId,addedBlobs:e,deletedBlobs:n};this._logger.debug(`queue checkpoint: version: ${i.checkpointId}, add: ${i.addedBlobs.length} blobs, rm: ${i.deletedBlobs.length} blob`),this._checkpointQueue.add(i)}};function Si(t,e,n,i){let s=0;for(let c of n)if(t.log(e,`  ${c}`),s++,i!==void 0&&s>=i){t.log(e,"  ...");break}}var wp=class{constructor(e,n){this.maxItems=e;this.maxByteSize=n}items=new Map;byteSize=0;addItem(e,n){let i=this.items.get(e);if(i===void 0){if(this.items.size>=this.maxItems||this.byteSize+n.byteSize>=this.maxByteSize)return!1;this.items.set(e,[n]),this.byteSize+=n.byteSize}else i.push(n);return!0}},Sp=class{constructor(e){this.maxItemCount=e}items=new Map;get full(){return this.items.size>=this.maxItemCount}addItem(e,n){if(this.items.has(e))return!1;this.items.set(e,n)}},Dl=class t extends st{constructor(n,i,s,c,u){super();this.workspaceName=n;this._apiServer=i;this._pathHandler=s;this._pathMap=c;this._logger=ve(`DiskFileManager[${n}]`),u===void 0?this._probeBatchSize=t.maxProbeBatchSize:(u<t.minProbeBatchSize?this._logger.verbose(`Rejecting requested probe batch size of ${u} (min = ${t.minProbeBatchSize})`):u>t.maxProbeBatchSize&&this._logger.verbose(`Rejecting requested probe batch size of ${u} (max = ${t.maxProbeBatchSize})`),this._probeBatchSize=Math.max(Math.min(u,t.maxProbeBatchSize),t.minProbeBatchSize)),this._toCalculate=new Hn(this._calculate.bind(this)),this.addDisposable(this._toCalculate),this._toProbe=new Hn(this._probe.bind(this)),this.addDisposable(this._toProbe),this._probeBatch=this._newProbeBatch(),this._toUpload=new Hn(this._upload.bind(this)),this.addDisposable(this._toUpload),this._uploadBatch=this._newUploadBatch(),this._probeRetryWaiters=new Hn(this._enqueueForProbe.bind(this)),this.addDisposable(this._probeRetryWaiters),this._probeRetryKicker=new sr(this._probeRetryWaiters,t.probeRetryPeriodMs),this.addDisposable(this._probeRetryKicker),this._probeRetryBackoffWaiters=new Hn(this._enqueueForProbe.bind(this)),this._probeRetryBackoffKicker=new sr(this._probeRetryBackoffWaiters,t.probeRetryBackoffPeriodMs),this.addDisposable(this._probeRetryBackoffKicker)}static minProbeBatchSize=1;static maxProbeBatchSize=1e3;static maxUploadBatchBlobCount=128;static maxUploadBatchByteSize=1e6;static probeRetryPeriodMs=3*1e3;static probeBackoffAfterMs=60*1e3;static probeRetryBackoffPeriodMs=60*1e3;_notAPlainFile="Not a file";_fileNotAccessible="File not readable";_fileNotText="Binary file";_fileUploadFailure="Upload failed";_onDidChangeInProgressItemCountEmitter=new ue;onDidChangeInProgressItemCount=this._onDidChangeInProgressItemCountEmitter.event;_onQuiescedEmitter=new ue;_onQuiesced=this._onQuiescedEmitter.event;_textDecoder=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0});_toCalculate;_toProbe;_probeBatch;_probeBatchSize;_probeRetryWaiters;_probeRetryKicker;_probeRetryBackoffWaiters;_probeRetryBackoffKicker;_toUpload;_uploadBatch;_itemsInFlight=new Map;_seq=1e3;metrics=new is("File metrics");_logger;_stopping=!1;_pathsAccepted=this.metrics.counterMetric("paths accepted");_pathsNotAccessible=this.metrics.counterMetric("paths not accessible");_nonFiles=this.metrics.counterMetric("not plain files");_largeFiles=this.metrics.counterMetric("large files");_blobNameCalculationFails=this.metrics.counterMetric("blob name calculation fails");_encodingErrors=this.metrics.counterMetric("encoding errors");_mtimeCacheHits=this.metrics.counterMetric("mtime cache hits");_mtimeCacheMisses=this.metrics.counterMetric("mtime cache misses");_probeBatches=this.metrics.counterMetric("probe batches");_blobNamesProbed=this.metrics.counterMetric("blob names probed");_filesRead=this.metrics.counterMetric("files read");_blobsUploaded=this.metrics.counterMetric("blobs uploaded");_ingestPathMs=this.metrics.timingMetric("ingestPath");_probeMs=this.metrics.timingMetric("probe");_statMs=this.metrics.timingMetric("stat");_readMs=this.metrics.timingMetric("read");_uploadMs=this.metrics.timingMetric("upload");stop(){this.dispose()}dispose(){this._stopping=!0,super.dispose()}get probeBatchSize(){return this._probeBatchSize}get itemsInFlight(){return this._itemsInFlight.size}ingestPath(n,i){this._ingestPathMs.start(),!this._stopping&&(i=vT(i),this._enqueueForCalculate(n,i),this._ingestPathMs.stop())}async awaitQuiesced(){if(!(this._stopping||this._itemsInFlight.size===0))return aa(this._onQuiesced)}_nextSeq(){return this._seq++}_makeAbsPath(n,i){let s=this._pathMap.getRepoRoot(n);if(s!==void 0)return et(s,i)}_fileTooLargeString(n){return`File too large (${n} > ${this._pathHandler.maxBlobSize})`}_getMtime(n,i,s,c){this._statMs.start();let u=this._pathHandler.classifyPath(n);switch(this._statMs.stop(),u.type){case"inaccessible":this._pathsNotAccessible.increment(),this._pathMapInvalidate(i,s,c,this._fileNotAccessible);return;case"not a file":this._nonFiles.increment(),this._pathMapInvalidate(i,s,c,this._notAPlainFile);return;case"large file":this._largeFiles.increment(),this._pathMapInvalidate(i,s,c,this._fileTooLargeString(u.size));return;case"accepted":return u.mtime}}async _readAndValidate(n,i,s,c){this._readMs.start();let u=await this._pathHandler.readText(n);switch(this._readMs.stop(),this._filesRead.increment(),u.type){case"inaccessible":this._pathsNotAccessible.increment(),this._pathMapInvalidate(i,s,c,this._fileNotAccessible);return;case"large file":this._largeFiles.increment(),this._pathMapInvalidate(i,s,c,this._fileTooLargeString(u.size));return;case"binary":this._pathMapInvalidate(i,s,c,this._fileNotText);return;case"text":return u.contents}}_calculateBlobName(n,i,s,c){try{return this._pathHandler.calculateBlobName(n,i)}catch(u){if(u instanceof la){this._largeFiles.increment();let d=this._fileTooLargeString(i.length);this._pathMapInvalidate(s,n,c,d)}else this._blobNameCalculationFails.increment(),this._pathMapInvalidate(s,n,c,pe(u));return}}async _calculate(n){if(n===void 0)return;let[i,[s,c]]=n;if(!this._pathMapVerify(s,c,i))return;let u=this._makeAbsPath(s,c);if(u===void 0){this._inflightItemRemove(i);return}let d=this._getMtime(u,s,c,i);if(d===void 0)return;let f,m=this._pathMap.getBlobInfo(s,c,d);if(m!==void 0){this._mtimeCacheHits.increment();let[x,D]=m;if(D>0){this._pathMapUpdate(s,c,i,x,d);return}f=x}else{let x=await this._readAndValidate(u,s,c,i);if(x===void 0||(this._mtimeCacheMisses.increment(),f=this._calculateBlobName(c,x,s,i),f===void 0))return}this._pathsAccepted.increment();let y={folderId:s,relPath:c,blobName:f,mtime:d,startTime:Date.now()};this._enqueueForProbeRetry(i,y)}_newProbeBatch(){return new Sp(this._probeBatchSize)}_grabProbeBatch(){if(this._probeBatch.items.size===0)return;let n=this._probeBatch;return this._probeBatch=this._newProbeBatch(),n}async _probe(n){if(n!==void 0){let[u,d]=n;if(!this._pathMapVerify(d.folderId,d.relPath,u)||(this._probeBatch.addItem(u,d),!this._probeBatch.full))return}let i=this._grabProbeBatch();if(i===void 0)return;let s=new Set;for(let[u,d]of i.items)s.add(d.blobName);this._probeBatches.increment(),this._blobNamesProbed.increment(i.items.size),this._logger.verbose(`probe ${s.size} blobs`),this._probeMs.start();let c;try{c=await Fn(async()=>this._apiServer.findMissing([...s]),this._logger)}catch{}if(this._probeMs.stop(),c!==void 0){this._logger.verbose(`find-missing reported ${c.unknownBlobNames.length} unknown blob names and ${c.nonindexedBlobNames.length} nonindexed blob names.`),c.unknownBlobNames.length>0&&(this._logger.verbose("unknown blob names:"),Si(this._logger,"verbose",c.unknownBlobNames,5)),c.nonindexedBlobNames.length>0&&(this._logger.verbose("nonindexed blob names:"),Si(this._logger,"verbose",c.nonindexedBlobNames,5));let u=new Set(c.unknownBlobNames),d=new Set(c.nonindexedBlobNames),f=this._beginUploadBatch();for(let[m,y]of i.items)this._pathMapVerify(y.folderId,y.relPath,m)&&(u.has(y.blobName)?this._enqueueForUpload(m,y.folderId,y.relPath,!1):d.has(y.blobName)?this._enqueueForProbeRetry(m,y):this._pathMapUpdate(y.folderId,y.relPath,m,y.blobName,y.mtime));f.dispose()}else for(let[u,d]of i.items)this._enqueueForProbeRetry(u,d)}_newUploadBatch(){return new wp(t.maxUploadBatchBlobCount,t.maxUploadBatchByteSize)}_grabUploadBatch(){if(this._uploadBatch.items.size===0)return;let n=this._uploadBatch;return this._uploadBatch=this._newUploadBatch(),n}async _upload(n){let i;if(n!==void 0){let[d,{seq:f,folderId:m,relPath:y}]=n;if(!this._pathMapVerify(m,y,f))return;let x=this._getMtime(d,m,y,f);if(x===void 0)return;let D=await this._readAndValidate(d,m,y,f);if(D===void 0)return;let N=this._calculateBlobName(y,D,m,f);if(N===void 0)return;let A;try{A=this._textDecoder.decode(D)}catch(B){this._pathMapInvalidate(m,y,f,pe(B)),this._encodingErrors.increment();return}let L={seq:f,folderId:m,pathName:y,text:A,blobName:N,mtime:x,byteSize:D.length,metadata:[]};if(this._uploadBatch.addItem(N,L))return;i=L}let s=this._grabUploadBatch();if(s===void 0)return;i!==void 0&&this._uploadBatch.addItem(i.blobName,i),this._logger.verbose(`upload ${s.items.size} blobs`);let c=new Array;for(let[d,f]of s.items)c.push(f[0]);this._uploadMs.start();let u=await this._uploadBlobBatch(c);this._uploadMs.stop(),this._blobsUploaded.increment(u.size);for(let[d,f]of s.items){let m=u.get(d);if(m===void 0)for(let y of f)this._pathMapInvalidate(y.folderId,y.pathName,y.seq,this._fileUploadFailure);else for(let y of f){let x={folderId:y.folderId,relPath:y.pathName,blobName:m,mtime:y.mtime,startTime:Date.now()};this._enqueueForProbeRetry(y.seq,x)}}}async _uploadBlobBatch(n){this._logger.verbose(`upload begin: ${n.length} blobs`);for(let c of n)this._logger.verbose(`    - ${c.folderId}:${c.pathName}; expected blob name ${c.blobName}`);let i;try{i=await Fn(async()=>await this._apiServer.batchUpload(n),this._logger)}catch(c){this._logger.error(`batch upload failed: ${pe(c)}`)}let s=new Map;if(i!==void 0)for(let c=0;c<i.blobNames.length;c++)s.set(n[c].blobName,i.blobNames[c]);return await this._uploadBlobsSequentially(n,i?.blobNames.length??0,s),s}async _uploadBlobsSequentially(n,i,s){for(let c=i;c<n.length;c++){let u=n[c];try{this._logger.verbose(`sequential upload of ${u.pathName} -> ${u.blobName}`);let d=await Fn(async()=>this._apiServer.memorize(u.pathName,u.text,u.blobName,[]),this._logger);s.set(u.blobName,d.blobName)}catch{}}}_inflightItemAdd(n,i,s){this._itemsInFlight.set(n,[i,s])}_inflightItemRemove(n){this._itemsInFlight.delete(n),this._onDidChangeInProgressItemCountEmitter.fire(this._itemsInFlight.size),this._itemsInFlight.size===0&&(this._logger.verbose("inflight items signaling empty"),this._onQuiescedEmitter.fire())}_pathMapVerify(n,i,s){if(!this._pathMap.shouldTrack(n,i))return this._inflightItemRemove(s),!1;let c=this._pathMap.getContentSeq(n,i);return c!==void 0&&c>=s?(this._inflightItemRemove(s),!1):!0}_pathMapUpdate(n,i,s,c,u){this._inflightItemRemove(s),this._pathMap.update(n,i,s,c,u)}_pathMapInvalidate(n,i,s,c){this._logger.verbose(`path map invalidate: ${n}:${i} (${c})`),this._pathMap.markUntrackable(n,i,s,c),this._inflightItemRemove(s),this._pathMap.markUntrackable(n,i,s,c)}_enqueueForCalculate(n,i){let s=this._nextSeq();this._inflightItemAdd(s,n,i),this._toCalculate.insert(s,[n,i])?this._toCalculate.kick():this._inflightItemRemove(s)}_enqueueForProbe(n){if(n===void 0)this._toProbe.kick();else{let[i,s]=n;this._logger.verbose(`probe enqueue ${s.blobName} -> ${i}, ${s.folderId}:${s.relPath}`),this._toProbe.insert(i,s)}return Promise.resolve()}_beginUploadBatch(){return new rr(()=>this._toUpload.kick())}_enqueueForUpload(n,i,s,c=!0){this._logger.verbose(`upload enqueue ${i}:${s} -> ${n}`);let u=this._makeAbsPath(i,s);if(u===void 0){this._inflightItemRemove(n);return}let d=this._toUpload.get(u);if(d!==void 0){let f=d.seq;if(f>n)this._inflightItemRemove(f);else if(f<n){this._inflightItemRemove(n);return}}this._toUpload.insert(u,{seq:n,folderId:i,relPath:s},!0),c&&this._toUpload.kick()}_enqueueForProbeRetry(n,i){Date.now()-i.startTime<t.probeBackoffAfterMs?(this._logger.verbose(`probe-retry enqueue ${i.blobName} -> ${n}, ${i.folderId}:${i.relPath}`),this._probeRetryWaiters.insert(n,i)):(this._logger.verbose(`probe-retry enqueue backoff ${i.blobName} -> ${n}, ${i.folderId}:${i.relPath}`),this._probeRetryBackoffWaiters.insert(n,i))}};var jr=class{static cacheFileName="mtime-cache.json";static tmpFileName="mtime-cache.json.tmp"},xp=class{constructor(e=da){this.namingVersion=e}entries=new Array};function Bj(t){if(!(t.mtime===void 0||typeof t.mtime!="number"||!t.mtime)&&!(t.name===void 0||typeof t.name!="string"||!t.name))return{mtime:t.mtime,name:t.name}}function Uj(t){return et(t,jr.cacheFileName)}function El(t){let e=Uj(t);return Ju(e)}async function kT(t,e){let n=et(t,jr.cacheFileName),i=et(e,jr.cacheFileName);await ia(e),await Mg(n,i)}async function TT(t,e){let n=new Map,i=ve(`MtimeCache[${t}]`),s=et(e,jr.cacheFileName);i.info(`reading blob name cache from ${s}`);try{let c=0,u=await Jo(s),d=JSON.parse(u);if(d.namingVersion===void 0||d.namingVersion!==da)i.info(`blob naming version ${d.namingVersion} !== ${da}`);else if(Array.isArray(d.entries))for(let[f,m]of d.entries){let y=Bj(m);y!==void 0&&(n.set(f,{mtime:y.mtime,name:y.name}),c++)}i.info(`read ${c} entries from ${s}`)}catch(c){let u=pe(c);c instanceof Error&&"code"in c&&c.code==="ENOENT"?i.info(`no blob name cache found at ${s} (probably new source folder); error = ${u}`):i.error(`failed to read blob name cache ${s}: ${u}`)}return n}var Fl=class extends jr{constructor(n,i){super();this._name=n;this._cacheDirName=i;this._cacheFileName=et(this._cacheDirName,jr.cacheFileName),this._tmpFileName=et(this._cacheDirName,jr.tmpFileName)}_cacheFileName;_tmpFileName;_logger=ve("MTimeCacheWriter");get cacheFileName(){return this._cacheFileName}async write(n){this._logger.debug(`persisting to ${this._cacheFileName}`);let i=new xp;for(let[s,c,u]of n)i.entries.push([s,{mtime:c,name:u}]);await ia(this._cacheDirName),await Xu(this._tmpFileName,JSON.stringify(i,void 0,4)),await Mg(this._tmpFileName,this._cacheFileName),this._logger.debug(`persisted ${i.entries.length} entries at naming version ${da} to ${this._cacheFileName}`)}};var DT=Ze(Qo());var RT=require("console");var Pp=class{constructor(e,n,i,s){this.seq=e;this.start=n;this.length=i;this.origLength=s}get end(){return this.start+this.length}get localShift(){return this.origLength-this.length}},as=class t{constructor(e,n,i,s,c){this.seq=e;this.start=n;this.length=i;this.origStart=s;this.origLength=c}static fromMod(e,n){return new t(e.seq,e.start,e.length,e.start+n,e.origLength)}get end(){return this.start+this.length}get origEnd(){return this.origStart+this.origLength}setStart(e){let n=this.start-e;this.start-=n,this.length+=n,this.origStart-=n,this.origLength+=n}setEnd(e){let n=e-this.end;this.length+=n,this.origLength+=n}},cs=class t{static _logger=ve("ChangeTracker");_modifications=[];_seq=0;get seq(){return this._seq}get empty(){return this._modifications.length===0}get length(){return this._modifications.length}translate(e,n){let i=e+Math.max(n,0),s=0,c=0;for(;s<this._modifications.length&&this._modifications[s].end<e;)c+=this._modifications[s].localShift,s++;let d=(s===this._modifications.length||e<this._modifications[s].start?e:this._modifications[s].start)+c;for(;s<this._modifications.length&&this._modifications[s].end<i;)c+=this._modifications[s].localShift,s++;let m=(s===this._modifications.length||i<this._modifications[s].start?i:this._modifications[s].start+this._modifications[s].origLength)+c;return[d,m-d]}apply(e,n,i,s){let c=0,u,d,f,m=n,y=0,x;for(;c<this._modifications.length&&this._modifications[c].end<n;)c++;if(x=c,c<this._modifications.length&&this._modifications[c].start<=n){u=this._modifications[c],d=u.length,f=u.origLength;let D=m-u.start;(0,RT.assert)(D<=u.length);let N=Math.min(u.length-D,i-y);d-=N,y+=N,m=u.end,++c}else u=new Pp(e,n,0,0),d=0,f=0;for(;c<this._modifications.length&&y<i;c++){let D=this._modifications[c],N=D.start-m,A=Math.min(N,i-y);if(f+=A,y+=A,m+A<D.start)break;let L=Math.min(D.length,i-y);d+=D.length-L,y+=L,f+=D.origLength,m=D.end}for(u.length=d+s,u.origLength=f+(i-y),u.seq=e,this._modifications.splice(x,c-x,u),c=x+1;c<this._modifications.length;c++)this._modifications[c].start+=s-i;this._seq=e}merge(e){for(let n of e._modifications)this.apply(n.seq,n.start,n.origLength,n.length)}advance(){for(let e of this._modifications)e.origLength=e.length}getEdits(){let e=[],n=0;for(let i of this._modifications)e.push(new as(i.seq,i.start,i.length,i.start+n,i.origLength)),n+=i.localShift;return e}countChunks(e){if(this._modifications.length===0)return 0;let n=this._modifications.at(-1).end;return this.getChunks(e,n).length}getChunks(e,n){if(this._modifications.length===0)return[];let i=new Array,s,c=0;for(let d of this._modifications){let f=as.fromMod(d,c);if(c+=d.localShift,s!==void 0&&(f.start-s.start>=e||f.end-s.start>e&&f.length<=e)&&(i.push(s),s=void 0),s===void 0)s=new as(f.seq,f.start,0,f.origStart,0);else{let x=f.start-s.end;s.length+=x,s.origLength+=x}let m=f.length,y=Math.min(m,e-s.length);s.length+=y,s.origLength+=f.origLength,s.seq=Math.max(s.seq,f.seq);for(let x=y;x<m;x+=y){i.push(s);let D=f.start+x;s=new as(f.seq,D,0,f.origEnd,0),y=Math.min(m-x,e),s.length+=y}}return s!==void 0&&i.push(s),this._widen(i,e,n),this._validateChunks(i)}_widen(e,n,i){let s=0;for(let c=0;c<e.length;c++){let u=e[c],d=c+1===e.length?i:e[c+1].start,f=n-u.length,m,y,x=Math.floor(u.start-f/2);x<=s?(m=s,y=Math.min(m+n,d)):(y=Math.min(x+n,d),m=Math.max(y-n,s)),u.setStart(m),u.setEnd(y),s=y}}_validateChunks(e){let n=new Array;for(let i of e)i.origStart>i.origEnd?t._logger.error("invalid chunk: ",JSON.stringify(i)):n.push(i);return n}};var FT=6,ET=6,va=FT*ET,Wj=1e3,jj=2e3,zj=60*1e3,Hj=200,Vj=30*1e3,Gj=30*1e3,Qj=60*1e3,Cp=class{constructor(e,n,i,s){this.folderId=e;this.pathName=n;this.key=i;this.appliedSeq=s;this.recentChangesets=new Zo(ET),this.addChangeset(s),this.changesSinceUpload=new cs}uploadedBlobName;uploadedSeq;recentChangesets;changesSinceUpload;uploadRequested=!1;inProgressUpload;_embargoed=!1;invalidateUploadState(){this.uploadedBlobName=void 0,this.uploadedSeq=void 0}_clear(){this.uploadedBlobName=void 0,this.uploadedSeq=void 0,this.recentChangesets.clear(),this.changesSinceUpload=void 0,this.uploadRequested=!1,this.inProgressUpload=void 0}embargo(){this._clear(),this._embargoed=!0}get embargoed(){return this._embargoed}get uploadInProgress(){return this.inProgressUpload!==void 0}getBlobName(){return this.recentChanges(!1)?.blobName}longestHistory(e){if(this.uploadedSeq===void 0)return;let n=this.recentChangesets.at(0);if(n!==void 0){if(e)return{changeTracker:n.changeTracker,blobName:this.uploadedBlobName};if(this.uploadedBlobName!==void 0&&!(n.initialSeq>this.uploadedSeq))return{changeTracker:n.changeTracker,blobName:this.uploadedBlobName}}}recentChanges(e){return this.inProgressUpload!==void 0?this.inProgressUpload.savedChangeset:this.longestHistory(e)}applyAll(e,n,i,s){for(let c of this.recentChangesets)c.changeTracker.apply(e,n,i,s);this.appliedSeq=e}advanceAll(){for(let e of this.recentChangesets)e.changeTracker.advance()}addChangeset(e){this.recentChangesets.addItem({initialSeq:e,changeTracker:new cs})}purgeChangesets(e){let n=0;for(;!this.recentChangesets.empty&&!((this.recentChangesets.at(1)?.initialSeq??this.appliedSeq)>=e);)this.recentChangesets.shiftLeft(1),n++;return n}},kp=class extends Cp{constructor(n,i,s,c,u){super(n,i,s,u);this.document=c}get documentType(){return 0}getText(){return this.document.getText()}},Il=class t extends st{constructor(n,i,s,c,u,d){super();this._apiServer=n;this._completionServer=i;this._configListener=s;this._blobNameCalculator=c;this._pathMap=u;this._sequenceGenerator=d;this._logger=ve("OpenFileManager"),this._uploadQueue=new Wr(this._upload.bind(this)),this.addDisposable(this._uploadQueue),this._verifyWaiters=new Wr(this._enqueueForVerify.bind(this)),this.addDisposable(this._verifyWaiters),this._verifyWaitersKicker=new sr(this._verifyWaiters,jj),this.addDisposable(this._verifyWaitersKicker),this._longWaiters=new Wr(this._enqueueForVerify.bind(this)),this.addDisposable(this._longWaiters),this._longWaitersKicker=new sr(this._longWaiters,zj),this.addDisposable(this._longWaitersKicker),this._verifyQueue=new Wr(this._verify.bind(this)),this.addDisposable(this._verifyQueue)}_trackedFolders=new Map;_uploadQueue;_verifyWaiters;_longWaiters;_verifyWaitersKicker;_longWaitersKicker;_verifyQueue;_verifyBatch=new Map;_prevUpdatedDocument;_logger;openSourceFolder(n){if(this._trackedFolders.has(n))throw new Error(`Source folder ${n} is already open`);return this._trackedFolders.set(n,new Map),this._logger.info(`Opened source folder ${n}`),new rr(()=>{this._closeSourceFolder(n)})}_closeSourceFolder(n){this._trackedFolders.delete(n),this._logger.info(`Closed source folder ${n}`)}startTracking(n,i,s){this._trackDocument(n,i,s)}stopTracking(n,i,s){let c=this._getFolder(n);if(c===void 0)return;let u=c.get(i);u!==void 0&&(s!==void 0&&u.documentType!==s||(c.delete(i),this._prevUpdatedDocument===u&&(this._prevUpdatedDocument=void 0),this._logger.verbose(`stop tracking ${n}:${i}`)))}isTracked(n,i){return this._getDocument(n,i)!==void 0}getTrackedPaths(n){let i=this._getFolder(n);return i===void 0?new Array:Array.from(i.keys())}loseFocus(){this._setFocus(void 0)}get _chunkSize(){return this._completionServer.completionParams.chunkSize}_getFolder(n){return this._trackedFolders.get(n)}_getDocument(n,i,s){let c=typeof n=="number"?this._getFolder(n):n;if(c===void 0)return;let u=c.get(i);if(u!==void 0&&!(s!==void 0&&u.key!==s))return u}getBlobName(n,i){return this._getDocument(n,i)?.getBlobName()}translateRange(n){let i=this._getDocument(n.folderId,n.relPath);if(i===void 0||i.uploadedBlobName===void 0)return;let s=i.changesSinceUpload;if(s===void 0)return;let c=s.translate(n.beginOffset,n.endOffset-n.beginOffset);return{blobName:i.uploadedBlobName,beginOffset:c[0],endOffset:c[0]+c[1]}}notifyMissingBlob(n,i,s){let c=this._getDocument(n,i);return c===void 0||c.uploadedBlobName!==s?!1:(c.invalidateUploadState(),this._tryEnqueueUpload(n,i,"blob name reported missing",c),!0)}getRecencySummary(n){let i=new Map,s=new Array;for(let[c,u]of this._trackedFolders){let d=new Map;i.set(c,d);for(let[f,m]of u){if(m.embargoed||m.uploadedSeq===void 0)continue;let y=m.recentChanges(!1);if(y===void 0||y.blobName===void 0)continue;d.set(f,y.blobName);let x=m.getText(),D=y.changeTracker.getChunks(n,x.length);if(D.length===0)continue;let N=this._blobNameCalculator.calculateNoThrow(f,x);for(let A of D)s.push({seq:A.seq,uploaded:A.seq<=m.uploadedSeq,folderId:c,pathName:f,blobName:y.blobName,text:x.slice(A.start,A.end),origStart:A.origStart,origLength:A.origLength,expectedBlobName:N})}}return s.sort(t._compareChunks),{folderMap:i,recentChunks:s}}getRecentChunkInfo(n,i=!1){let s=new Array;for(let[c,u]of this._trackedFolders)for(let[d,f]of u){if(f.embargoed||f.uploadedSeq===void 0)continue;let m=f.recentChanges(i);if(m===void 0)continue;let y=m.changeTracker.getChunks(n,f.getText().length);if(y.length!==0)for(let x of y)s.push({seq:x.seq,uploaded:x.seq<=f.uploadedSeq,folderId:c,pathName:d,blobName:m.blobName})}return s.sort(t._compareChunks),s}static _compareChunks(n,i){return n.uploaded===i.uploaded?i.seq-n.seq:n.uploaded?1:-1}applyTextDocumentChange(n,i,s){let c=this._getDocument(n,i);if(c===void 0){this._trackDocument(n,i,s.document);return}if(!this._prepareForUpdate(c)||s.contentChanges.length===0)return;let u=s.contentChanges.map(d=>[d.rangeOffset,d.rangeLength,d.text.length]);this._applyChangedRanges(n,i,c,u)}_setFocus(n){this._prevUpdatedDocument!==void 0&&n!==this._prevUpdatedDocument&&(this._tryEnqueueUpload(this._prevUpdatedDocument.folderId,this._prevUpdatedDocument.pathName,"document lost focus"),this._purgeUnneededChangesets()),this._prevUpdatedDocument=n}_trackDocument(n,i,s){let c=this._getFolder(n);if(c===void 0)throw new Error(`Source folder ${n} is not open`);let u=this._getDocument(c,i);if(this._setFocus(u),u!==void 0)return;let d=this._sequenceGenerator.next(),f=s;u=new kp(n,i,d,f,d),c.set(i,u);let m=u.getText(),y=this._blobNameCalculator.calculate(i,m);if(y===void 0){this._embargo(n,i,u,"blob name calculation failed");return}this._pathMap.getAnyPathName(y)===void 0?this._tryEnqueueUpload(n,i,"new document has no blob name",u):(u.uploadedBlobName=y,u.uploadedSeq=u.appliedSeq),this._logger.verbose(`start tracking ${n}:${i}`)}_prepareForUpdate(n){return this._setFocus(n),!n.embargoed}_applyChangedRanges(n,i,s,c){let u=this._sequenceGenerator.next();s.recentChangesets.empty&&(s.addChangeset(u),this._logger.verbose(`apply: new changeset for ${n}:${i}; total = ${s.recentChangesets.length}`));let d=s.inProgressUpload;for(let y of c){let[x,D,N]=y;d!==void 0&&(d.savedChangeset!==void 0&&d.savedChangeset.changeTracker.apply(u,x,D,N),d.changesSinceUpload.apply(u,x,D,N)),s.applyAll(u,x,D,N),s.changesSinceUpload?.apply(u,x,D,N)}if(s.appliedSeq=u,d!==void 0){let y=d.changesSinceUpload.length>=Hj;y||(y=d.changesSinceUpload.countChunks(this._chunkSize)>=va),y&&this._cancelInProgressUpload(n,i,s)}if(s.changesSinceUpload!==void 0){let y=s.changesSinceUpload.countChunks(this._chunkSize);y>1&&this._tryEnqueueUpload(n,i,"multiple non-uploaded chunks",s),y>=va&&(this._logger.verbose(`apply: no longer tracking non-uploaded changes for ${n}:${i}`),s.changesSinceUpload=void 0)}let m=s.recentChangesets.at(-1).changeTracker.countChunks(this._chunkSize);m>=FT&&(s.addChangeset(u),this._logger.verbose(`apply: new changeset for ${n}:${i}; chunks = ${m}; total = ${s.recentChangesets.length}`))}_cancelInProgressUpload(n,i,s){this._logger.verbose(`cancel in-progress upload: ${n}:${i}`),s.inProgressUpload=void 0,s.key=this._sequenceGenerator.next()}_validateInProgressUpload(n,i,s){let c=this._getDocument(n,i,s);if(!(c===void 0||c.inProgressUpload===void 0))return[c,c.inProgressUpload]}_tryEnqueueUpload(n,i,s,c){let u=c??this._getDocument(n,i);u!==void 0&&(u.uploadRequested||u.appliedSeq!==u.uploadedSeq&&u.appliedSeq!==u.inProgressUpload?.uploadSeq&&(this._logger.verbose(`upload request: ${n}:${i}; reason = ${s}`),u.uploadRequested=!0,u.uploadInProgress?this._logger.verbose(`upload request delayed: upload for ${n}:${i} already in progress`):this._enqueueUpload(n,i,u.key)))}_retryUpload(n,i){this._logger.verbose(`retry upload; ${n}:${i}`);let s=this._getDocument(n,i);if(s===void 0){this._logger.verbose(`retry upload: document is no longer tracked; ${n}:${i}`);return}if(s.inProgressUpload!==void 0){this._logger.verbose(`retry upload: upload already in progress; ${n}:${i}`);return}s.uploadRequested=!0,this._enqueueUpload(n,i,s.key)}_enqueueUpload(n,i,s){this._uploadQueue.insert([n,i,s])&&(this._logger.verbose(`enqueue upload: ${n}:${i}`),this._uploadQueue.kick())}async _upload(n){if(n===void 0)return;let[i,s,c]=n,u=this._getDocument(i,s,c);if(u===void 0){this._logger.verbose(`upload: upload cancelled or no longer tracking document ${i}:${s}`);return}u.uploadRequested=!1;let d=u.getText(),f=this._blobNameCalculator.calculate(s,d);if(f===void 0){this._embargo(i,s,u,"failed to compute blob name");return}let m=u.longestHistory(!1),y=m===void 0||m.blobName===void 0?void 0:{changeTracker:(0,DT.cloneDeep)(m.changeTracker),blobName:m.blobName};u.inProgressUpload={uploadSeq:u.appliedSeq,blobName:f,savedChangeset:y,changesSinceUpload:new cs},u.advanceAll(),u.uploadedBlobName=void 0;let x;try{this._logger.verbose(`upload: begin; ${i}:${s}, ${f}`);let N=Date.now();x=await Fn(async()=>{if(!(Date.now()-N>Vj)&&this._validateInProgressUpload(i,s,c))return this._apiServer.memorize(s,d,f,[])},this._logger)}catch(N){return this._logger.verbose(`upload: failed; ${i}:${s}, ${f}; ${pe(N)};`),this._embargo(i,s,u,`upload encountered permanent error: ${pe(N)}`)}if(!this._validateInProgressUpload(i,s,c))return this._logger.verbose(`upload: upload cancelled; pathName = ${i}:${s}`),this._retryUpload(i,s);if(x===void 0)return this._logger.verbose(`upload: upload timed out, cancelling; pathName = ${i}:${s}`),this._cancelInProgressUpload(i,s,u),this._retryUpload(i,s);let D=x.blobName;D===f?this._logger.verbose(`upload: completed; ${i}:${s}, ${D}`):this._logger.error(`upload: completed with mismatched blobName; pathName, received, expected = ${i}:${s}, ${D}, ${f}`),u.inProgressUpload.blobName=D,this._enqueueVerifyWaiter({folderId:i,pathName:s,key:c,startTime:Date.now()},D)}_requeueVerifyWaiter(n,i){let s=n.folderId,c=n.pathName;if(!this._validateInProgressUpload(s,c,n.key))return this._logger.verbose(`requeue verify-wait: upload cancelled; ${s}:${c}, ${i}`),this._retryUpload(s,c);Date.now()-n.startTime>Qj?(this._logger.verbose(`verify-wait: enqueue long; pathName = ${s}:${c}`),this._longWaiters.insert(n)):this._enqueueVerifyWaiter(n,i)}_enqueueVerifyWaiter(n,i){this._logger.verbose(`verify-wait: enqueue; ${n.folderId}:${n.pathName}, ${i}`),this._verifyWaiters.insert(n)}_enqueueForVerify(n){return n===void 0?(this._verifyQueue.kick(),Promise.resolve()):(this._verifyQueue.insert(n),Promise.resolve())}_grabVerifyBatch(){if(this._verifyBatch.size===0)return;let n=this._verifyBatch;return this._verifyBatch=new Map,n}async _verify(n){if(n!==void 0){let u=this._getDocument(n.folderId,n.pathName,n.key);if(u===void 0||u.inProgressUpload===void 0)return;let d=this._verifyBatch.get(u.inProgressUpload.blobName);if(d===void 0&&(d=new Array,this._verifyBatch.set(u.inProgressUpload.blobName,d)),d.push(n),this._verifyBatch.size<Wj)return}let i=this._grabVerifyBatch();if(i===void 0)return;let s=[...i.keys()];this._logger.verbose(`verify batch: blob count = ${s.length}`);let c;try{let u=Date.now();c=await Fn(async()=>{if(!(Date.now()-u>Gj))return this._apiServer.findMissing(s)},this._logger)}catch{}if(c===void 0){this._logger.verbose("verify: timeout exceeded");for(let u of s){let d=i.get(u);for(let f of d)this._requeueVerifyWaiter(f,u)}}else{this._logVerifyResult(c);let u=new Set(c.unknownBlobNames),d=new Set(c.nonindexedBlobNames);for(let[f,m]of i)if(u.has(f))for(let y of m)this.notifyMissingBlob(y.folderId,y.pathName,f);else if(d.has(f))for(let y of m)this._requeueVerifyWaiter(y,f);else for(let y of m)this._commit(y,f)}}_commit(n,i){let s=n.folderId,c=n.pathName,u=this._validateInProgressUpload(s,c,n.key);if(u===void 0){this._logger.verbose(`commit: upload cancelled for ${s}:${c}`);return}let[d,f]=u;d.inProgressUpload=void 0,this._logger.verbose(`commit: ${s}:${c}, ${i}; uploadSeq = ${f.uploadSeq}`),d.uploadedBlobName=i,d.uploadedSeq=f.uploadSeq,d.changesSinceUpload=f.changesSinceUpload,d.uploadRequested&&this._retryUpload(n.folderId,n.pathName)}_purgeUnneededChangesets(){let n=this.getRecentChunkInfo(this._chunkSize,!0);if(n.length<va)return;let i=n[va-1].seq,s=new Set;for(let c=va;c<n.length;c++){let u=this._getDocument(n[c].folderId,n[c].pathName);u!==void 0&&s.add(u)}for(let c of s){if(c===void 0)continue;let u=c.purgeChangesets(i);u>0&&this._logger.verbose(`purge: removed ${u} changesets from ${c.folderId}:${c.pathName}`)}}_embargo(n,i,s,c){this._logger.info(`embargoing: ${n}:${i} reason = ${c}`),s.embargo()}_logVerifyResult(n){let i=n.unknownBlobNames.length>0?"error":"verbose";this._logger.log(i,`find-missing reported ${n.unknownBlobNames.length} unknown blob names and ${n.nonindexedBlobNames.length} nonindexed blob names.`),n.unknownBlobNames.length>0&&(this._logger.log(i,"unknown blob names:"),Si(this._logger,i,n.unknownBlobNames,5)),n.nonindexedBlobNames.length>0&&(this._logger.log(i,"nonindexed blob names:"),Si(this._logger,i,n.nonindexedBlobNames,5))}};var Tp=Ze(Qo());var Ml=class{constructor(e,n,i){this._configListener=e;this._openFileManagerV1=n;this._openFileManagerV2=i}_logger=ve("OpenFileManagerProxy");get isV2Enabled(){return!1}startTrackingFolder(e,n){return this.isV2Enabled?[this._openFileManagerV2.startTrackingFolder(e,n),this._openFileManagerV1.openSourceFolder(n)]:[this._openFileManagerV1.openSourceFolder(n)]}addOpenedDocument(e){this._openFileManagerV1.startTracking(e.folderId,e.relPath,e.document),this.isV2Enabled&&this._openFileManagerV2.addOpenedDocument(e)}getBlobName(e,n){if(this.isV2Enabled){let i=this._openFileManagerV2.getBlobName(e,n),s=this._openFileManagerV1.getBlobName(e,n);return(i===void 0&&s!==void 0||i!==void 0&&s===void 0)&&this._logger.debug(`[WARN] getBlobName returned different results between v1 and v2 [${e}:${n}]
[${JSON.stringify(i)}]
[${JSON.stringify(s)}]`),s}else return this._openFileManagerV1.getBlobName(e,n)}handleMissingBlob(e,n,i){if(this.isV2Enabled){let s=this._openFileManagerV2.handleMissingBlob(e,n,i);return this._openFileManagerV1.notifyMissingBlob(e,n,i)||s}else return this._openFileManagerV1.notifyMissingBlob(e,n,i)}loseFocus(){this._openFileManagerV1.loseFocus()}stopTracking(e,n){this._openFileManagerV1.stopTracking(e,n),this.isV2Enabled&&this._openFileManagerV2.stopTracking(e,n)}handleClosedDocument(e){this._openFileManagerV1.stopTracking(e.folderId,e.relPath,0),this.isV2Enabled&&this._openFileManagerV2.handleClosedDocument(e)}handleChangedDocument(e){this._openFileManagerV1.applyTextDocumentChange(e.folderId,e.relPath,e.event),this.isV2Enabled&&this._openFileManagerV2.handleChangedDocument(e)}isTracked(e,n){if(this.isV2Enabled){let i=this._openFileManagerV2.isTracked(e,n),s=this._openFileManagerV1.isTracked(e,n);return i!==s&&this._logger.debug(`[WARN] isTracked returned different results between v1 and v2 [${e}:${n}]
[${JSON.stringify(i)}]
[${JSON.stringify(s)}]`),s}else return this._openFileManagerV1.isTracked(e,n)}getTrackedPaths(e){if(this.isV2Enabled){let n=this._openFileManagerV2.getTrackedPaths(e),i=this._openFileManagerV1.getTrackedPaths(e),s=(0,Tp.difference)(n,i);s.length>0&&this._logger.debug(`[WARN] getTrackedPaths in new but not in old [${e}]
[${JSON.stringify(s)}]`);let c=(0,Tp.difference)(i,n);return c.length>0&&this._logger.debug(`[WARN] getTrackedPaths in old but not in new [${e}]
[${JSON.stringify(c)}]`),i}else return this._openFileManagerV1.getTrackedPaths(e)}dispose(){this._openFileManagerV2?.dispose(),this._openFileManagerV1.dispose()}getRecencySummary(e){return this._openFileManagerV1.getRecencySummary(e)}translateRange(e,n,i,s){if(this.isV2Enabled){let c=this._openFileManagerV2.translateRange(e,n,i,s),u=this._openFileManagerV1.translateRange({folderId:e,relPath:n,beginOffset:i,endOffset:s});return(c?.blobName!==u?.blobName||c?.beginOffset!==u?.beginOffset||c?.endOffset!==u?.endOffset)&&this._logger.debug(`[WARN] translateRange returned different results between v1 and v2 [${e}:${n}]
[${JSON.stringify(c)}]
[${JSON.stringify(u)}]`),u}else return this._openFileManagerV1.translateRange({folderId:e,relPath:n,beginOffset:i,endOffset:s})}};var IT=require("node:buffer");var Al=class{constructor(e,n){this._fileReader=n;this._blobNameCalculator=new ts(e)}_blobNameCalculator;get maxBlobSize(){return this._blobNameCalculator.maxBlobSize}classifyPath(e){let n=this._fileReader.stat(e);return n?n.type!=="File"?{type:"not a file",mtime:n.mtime}:n.size>this._blobNameCalculator.maxBlobSize?{type:"large file",mtime:n.mtime,size:n.size}:{type:"accepted",size:n.size,mtime:n.mtime}:{type:"inaccessible"}}async readText(e){let n;try{if(n=await this._fileReader.read(e),n===void 0)return{type:"inaccessible"}}catch{return{type:"inaccessible"}}return(0,IT.isUtf8)(n)?n.length>this._blobNameCalculator.maxBlobSize?{type:"large file",size:n.length}:{type:"text",contents:n}:{type:"binary"}}calculateBlobName(e,n){return this._blobNameCalculator.calculateOrThrow(e,n)}};var ar=class t{constructor(e,n){this.rootPath=e;this.relPath=n}static from(e){return new t(e.rootPath,e.relPath)}get absPath(){return et(this.rootPath,this.relPath)}equals(e){return t.equals(this,e)}static equals(e,n){if(e===n)return!0;if(e==null||n==null)return!1;let i=MT(e)?et(e.rootPath,e.relPath):e.fsPath,s=MT(n)?et(n.rootPath,n.relPath):n.fsPath;return i===s}};function MT(t){return t!=null&&typeof t=="object"&&("rootPath"in t||"relPath"in t)}var Nl=class{_nextFolderId=100;_sourceFolders=new Map;_blobNameChangedEmitter=new ue;_nextEntryTS=1e3;_logger=ve("PathMap");constructor(){}dispose(){for(let[e,n]of this._sourceFolders)n.dispose()}get nextEntryTS(){return this._nextEntryTS}get onDidChangeBlobName(){return this._blobNameChangedEmitter.event}onDidChangePathStatus(e){return this._sourceFolders.get(e)?.onDidChangePathStatus}openSourceFolder(e,n){for(let[u,d]of this._sourceFolders){if(ml(d.folderRoot,e))throw new Error(`Source folder ${e} is already open`);if(or(e,d.folderRoot))throw new Error(`Source folder ${e} contains ${d.folderRoot}`);if(or(d.folderRoot,e))throw new Error(`Source folder ${d.folderRoot} contains ${e}`)}let i=this._nextFolderId++,s=new Rp(e,n);this._sourceFolders.set(i,s);let c=s.onDidChangeBlobName(this._handleBlobNameChangeEvent.bind(this));return s.addDisposable(c),this._logger.info(`Opened source folder ${e} with id ${i}`),i}closeSourceFolder(e){let n=this._sourceFolders.get(e);if(n===void 0)return;n.clear();let i=n.folderRoot;this._sourceFolders.delete(e),n.dispose(),this._logger.info(`Closed source folder ${i} with id ${e}`)}_handleBlobNameChangeEvent(e){this._blobNameChangedEmitter.fire(e)}getRepoRoot(e){return this._sourceFolders.get(e)?.repoRoot}hasFile(e,n){return this._sourceFolders.get(e)?.hasFile(n)??!1}getBlobName(e,n){return this._sourceFolders.get(e)?.getBlobName(n)}getBlobInfo(e,n,i){return this._sourceFolders.get(e)?.getBlobInfo(n,i)}getAnyPathName(e){for(let n of this._sourceFolders.values()){let i=n.getPathName(e);if(i!==void 0)return this._makeQualifiedPathName(n,i)}}getAllPathNames(e){let n=new Array;for(let i of this._sourceFolders.values()){let s=i.getPathName(e);s!==void 0&&n.push(new ar(i.repoRoot,s))}return n}getUniquePathCount(e){let n=0;for(let i of this._sourceFolders.values())i.getPathName(e)!==void 0&&n++;return n}getAllQualifiedPathNames(e){return this.getAllQualifiedPathInfos(e).map(n=>n.qualifiedPathName)}getAllQualifiedPathInfos(e){let n=new Array;for(let i of this._sourceFolders.values()){let s=i.getPathInfo(e);if(s!==void 0){let[c,u]=s;n.push({qualifiedPathName:new ar(i.repoRoot,e),fileType:c,isAccepted:u.accepted})}}return n}getAllPathInfo(e){let n=new Array;for(let i of this._sourceFolders.values()){let s=i.getPathName(e);s!==void 0&&n.push([i.folderRoot,i.repoRoot,s])}return n}getPathInfo(e,n){return this._sourceFolders.get(e)?.getPathInfo(n)}reportMissing(e){for(let n of this._sourceFolders.values()){let i=n.reportMissing(e);if(i!==void 0)return this._makeQualifiedPathName(n,i)}}insert(e,n,i,s){let c=this._nextEntryTS++;this._sourceFolders.get(e)?.insert(n,c,i,s)}remove(e,n){this._sourceFolders.get(e)?.remove(n)}shouldTrack(e,n){return this._sourceFolders.get(e)?.shouldTrack(n)??!1}getContentSeq(e,n){return this._sourceFolders.get(e)?.getContentSeq(n)}update(e,n,i,s,c){this._sourceFolders.get(e)?.update(n,i,s,c)}markUntrackable(e,n,i,s){this._sourceFolders.get(e)?.markUntrackable(n,i,s)}purge(e,n){this._sourceFolders.get(e)?.purge(n)}*pathsWithBlobNames(){for(let[e,n]of this._sourceFolders)for(let[i,s,c]of n.pathsWithBlobNames())yield[e,n.repoRoot,i,s,c]}*pathsInFolder(e){let n=this._sourceFolders.get(e);n!==void 0&&(yield*n.allPaths())}enablePersist(e,n,i){this._sourceFolders.get(e)?.enablePersist(n,i)}_makeQualifiedPathName(e,n){return new ar(e.repoRoot,n)}trackedFileCount(e){return this._sourceFolders.get(e)?.trackedFileCount??0}getFolderIds(){return Array.from(this._sourceFolders.keys())}},Rp=class extends st{constructor(n,i){super();this.folderRoot=n;this.repoRoot=i}static defaultPersistThreshold=100;_allPathNames=new Map;_trackableFilePaths=new Set;_blobNameToPathName=new Map;_persistState=void 0;_pathStatusChangedEmitter=new ue;_blobNameChangedEmitter=new ue;get onDidChangePathStatus(){return this._pathStatusChangedEmitter.event}get onDidChangeBlobName(){return this._blobNameChangedEmitter.event}get trackedFileCount(){return this._trackableFilePaths.size}shouldTrack(n){let i=this._allPathNames.get(n);return i===void 0?!1:i.fileType==="File"&&i.pathAcceptance.accepted}getContentSeq(n){return this._allPathNames.get(n)?.fileInfo?.contentSeq}insert(n,i,s,c){let u=this._allPathNames.get(n),d=u===void 0||u.fileType!==s||u.pathAcceptance.format()!==c.format();if(u===void 0)u={entryTS:i,fileType:s,pathAcceptance:c},this._allPathNames.set(n,u);else{let f=u.fileInfo;u.entryTS=i,u.fileType=s,u.pathAcceptance=c,c.accepted||(u.fileInfo=void 0,f?.trackable&&(this._blobNameToPathName.delete(f.blobName),this._publishBlobNameChange(n,f.blobName,void 0),this._markDirty()))}u.fileType==="File"&&u.pathAcceptance.accepted&&u.fileInfo?.trackable!==!1?this._trackableFilePaths.add(n):this._trackableFilePaths.delete(n),d&&this._pathStatusChangedEmitter.fire({relPath:n})}remove(n){let i=this._allPathNames.get(n);if(i!==void 0){if(this._allPathNames.delete(n),this._trackableFilePaths.delete(n),i.fileInfo!==void 0&&i.fileInfo.trackable){let s=i.fileInfo.blobName;this._blobNameToPathName.delete(s),this._publishBlobNameChange(n,s,void 0),this._markDirty()}this._pathStatusChangedEmitter.fire({relPath:n})}}clear(){for(let[n,i]of this._allPathNames)i.fileInfo!==void 0&&i.fileInfo.trackable&&this._publishBlobNameChange(n,i.fileInfo.blobName,void 0);this._allPathNames.clear(),this._trackableFilePaths.clear(),this._blobNameToPathName.clear(),this._markDirty()}update(n,i,s,c){let u=this._allPathNames.get(n);if(u===void 0||u.fileType!=="File"||!u.pathAcceptance.accepted||u.fileInfo!==void 0&&u.fileInfo.contentSeq>i)return;let d=u.fileInfo===void 0?!0:u.fileInfo.trackable,f;u.fileInfo?.trackable&&(f=u.fileInfo.blobName),u.fileInfo={trackable:!0,contentSeq:i,blobName:s,mtime:c},this._trackableFilePaths.add(n),s!==f&&(f!==void 0&&this._blobNameToPathName.delete(f),this._blobNameToPathName.set(s,n),this._publishBlobNameChange(n,f,s),this._markDirty()),(!d||f===void 0)&&this._pathStatusChangedEmitter.fire({relPath:n})}markUntrackable(n,i,s){let c=this._allPathNames.get(n);if(c===void 0||c.fileType!=="File"||!c.pathAcceptance.accepted||c.fileInfo!==void 0&&c.fileInfo.contentSeq>i)return;let u=c.fileInfo;c.fileInfo={trackable:!1,contentSeq:i,reason:s},this._trackableFilePaths.delete(n);let d=!1;if(u===void 0)d=!0;else if(u.trackable===!0){d=!0;let f=u.blobName;this._blobNameToPathName.delete(f),this._publishBlobNameChange(n,f,void 0),this._markDirty()}else d=s!==u.reason;d&&this._pathStatusChangedEmitter.fire({relPath:n})}_makeAbsPath(n){return et(this.repoRoot,n)}_publishBlobNameChange(n,i,s){i!==s&&this._blobNameChangedEmitter.fire({absPath:this._makeAbsPath(n),prevBlobName:i,newBlobName:s})}purge(n){let i=new Array;for(let[s,c]of this._allPathNames)c.entryTS<n&&i.push(s);for(let s of i)this.remove(s)}hasFile(n){return this._trackableFilePaths.has(n)}getBlobName(n){let i=this._allPathNames.get(n);if(i?.fileInfo?.trackable)return i.fileInfo?.blobName}getBlobInfo(n,i){let s=this._allPathNames.get(n);if(s?.fileInfo?.trackable&&s.fileInfo.mtime===i)return[s.fileInfo.blobName,s.fileInfo.contentSeq]}getPathName(n){return this._blobNameToPathName.get(n)}getPathInfo(n){let i=this._allPathNames.get(n);if(i!==void 0)return[i.fileType,i.pathAcceptance]}reportMissing(n){let i=this._blobNameToPathName.get(n);if(i===void 0)return;let s=this._allPathNames.get(i);if(s?.fileInfo?.trackable)return s.fileInfo.contentSeq=0,i}*pathsWithBlobNames(){for(let[n,i]of this._allPathNames){let s=i.fileInfo;s?.trackable&&(yield[n,s.mtime,s.blobName,s.contentSeq])}}*allPaths(){for(let[n,i]of this._allPathNames){let s=i.pathAcceptance.accepted,c=!1,u=i.pathAcceptance.format();s&&(i.fileType==="Other"?(s=!1,u="Not a file"):i.fileInfo!==void 0&&(i.fileInfo?.trackable===!0?c=!0:(s=!1,u=i.fileInfo.reason))),yield[n,i.fileType,s,c,u]}}_markDirty(){this._persistState!==void 0&&this._persistState.dirtyCount++}enablePersist(n,i){if(this._persistState)return;this._persistState={dirtyCount:this._trackableFilePaths.size,lastPersistDirtyCount:0,mtimeCacheWriter:n,persisting:!1},this._maybePersist();let s=setInterval(()=>void this._maybePersist(),i);this.addDisposable({dispose:()=>clearInterval(s)})}async _maybePersist(){if(!(this._persistState===void 0||this._persistState.persisting)){this._persistState.persisting=!0;try{this._persistState.dirtyCount>this._persistState.lastPersistDirtyCount&&await this._persist(this._persistState)}finally{this._persistState.persisting=!1}}}async _persist(n){let i=function*(u){for(let[d,f]of u){let m=f.fileInfo;m?.trackable&&(yield[d,m.mtime,m.blobName])}},s=n.dirtyCount;await n.mtimeCacheWriter.write(i(this._allPathNames.entries())),n.lastPersistDirtyCount=s}};var ql=require("fs");var ya=Ze(og());var Qi=class t{static instance;connection=null;logger;constructor(){this.logger=ve("NotificationManager")}static getInstance(){return t.instance||(t.instance=new t),t.instance}setConnection(e){this.connection=e}sendLogMessage(e,n){if(!this.connection){this.logger.error("Failed to send log message to client: No connection available");return}this.logger.debug(`Sending log message to client: ${e}`);let i={type:this._logLevelToMessageType(n),message:e};this.connection.sendNotification("window/logMessage",i)}_logLevelToMessageType(e){switch(e){case 1:return ya.MessageType.Error;case 2:return ya.MessageType.Warning;case 3:return ya.MessageType.Info;case 4:return ya.MessageType.Debug}}};var Dp=class{_onDidCreate=new ue;_onDidChange=new ue;_onDidDelete=new ue;logger=ve("FileSystemWatcher");workspaceFolder;watcher;onDidCreate=this._onDidCreate.event;onDidChange=this._onDidChange.event;onDidDelete=this._onDidDelete.event;constructor(e){this.workspaceFolder=e;let n={recursive:!0,persistent:!0};this.watcher=(0,ql.watch)(e,n,(i,s)=>this.listener(i,s)),this.watcher.on("error",i=>{this.logger.error(pe(i)),Qi.getInstance().sendLogMessage(`Filesystem watcher error (workspace ${e}): ${pe(i)}`,1)}),this.logger.info(`Filesystem watcher initialized with directory ${e}`)}listener(e,n){if(n===null)return;this.logger.debug(`Received raw filesystem event: ${e} ${n}`);let i=et(this.workspaceFolder,n);e==="rename"?this.statThenFire(i):e==="change"&&this.fireEvent(i,"change")}statThenFire(e){(0,ql.existsSync)(e)?this.fireEvent(e,"create"):this.fireEvent(e,"delete")}fireEvent(e,n){this.logger.debug(`Firing filesystem event: ${n} ${e}`),n==="create"?this._onDidCreate.fire(ot.file(e)):n==="change"?this._onDidChange.fire(ot.file(e)):n==="delete"&&this._onDidDelete.fire(ot.file(e))}dispose(){this.watcher.close()}},Fp=class{onDidCreate=new ue().event;onDidChange=new ue().event;onDidDelete=new ue().event;dispose(){}};function AT(t){try{return new Dp(t)}catch(e){let n=ve("FileSystemWatcher");return e instanceof Error?(n.error(`Failed to create filesystem watcher: ${pe(e)}`),n.error(`Stack trace: ${e.stack}`)):n.error(`Failed to create filesystem watcher: ${pe(e)}`),n.error("Falling back to dummy filesystem watcher"),Qi.getInstance().sendLogMessage(`Failed to create filesystem watcher (workspace ${t}): ${pe(e)}`,1),new Fp}}var Ep=class extends yi{constructor(n){super();this.reason=n}format(){return this.reason}},Ol=class extends st{constructor(n,i,s,c,u){super();this.folderName=n;this.folderRoot=i;this.repoRoot=s;this._pathFilter=c;this._workspaceFolder=u;this._logger=ve(`PathNotifier[${i}]`)}_pathFoundEmitter=new ue;_pathCreatedEmitter=new ue;_pathChangedEmitter=new ue;_pathDeletedEmitter=new ue;_logger;_filesystemWatcherCreated=!1;_stopping=!1;_deletedPaths=void 0;get onDidFindPath(){return this._pathFoundEmitter.event}get onDidCreatePath(){return this._pathCreatedEmitter.event}get onDidChangePath(){return this._pathChangedEmitter.event}get onDidDeletePath(){return this._pathDeletedEmitter.event}dispose(){this._stopping=!0,super.dispose()}async enumeratePaths(){if(this._stopping)return;this._deletedPaths=new Set,this._workspaceFolder!==void 0&&!this._filesystemWatcherCreated&&(this._createFilesystemWatcher(this._workspaceFolder),this._filesystemWatcherCreated=!0);let n=new os(this.folderName,ot.file(this.folderRoot),ot.file(this.repoRoot),this._pathFilter);for await(let[s,c,u,d]of n){if(this._stopping)return;this._pathFoundEmitter.fire({relPath:c,fileType:u,acceptance:d})}if(this._stopping)return;let i=this._deletedPaths;this._deletedPaths=void 0;for(let s of i)this._pathDeletedEmitter.fire(s);return n.stats}_handlePathChanged(n,i){let s=this._getRelPath(n);if(s===void 0)return;let c,u;try{c=Vi(Jt(n)).type,u=this._pathFilter.getPathInfo(s,c)}catch(f){c="Other",u=new Ep(pe(f))}let d=i?"created":"changed";this._logger.verbose(`${c} ${d}: ${s}, acceptance = ${u.format()}`),this._deletedPaths?.delete(s),i?this._pathCreatedEmitter.fire({relPath:s,fileType:c,acceptance:u}):this._pathChangedEmitter.fire({relPath:s,fileType:c,acceptance:u})}_handlePathDeleted(n){let i=this._getRelPath(n);i!==void 0&&(this._logger.verbose(`Path deleted: ${i}`),this._deletedPaths!==void 0?this._deletedPaths?.add(i):this._pathDeletedEmitter.fire(i))}_getRelPath(n){if(this._stopping)return;let i=$r(n);if(i!==void 0)return pa(this.repoRoot,i)}_createFilesystemWatcher(n){let i=AT(n.uri.fsPath);this.addDisposables(i,i.onDidCreate(s=>this._handlePathChanged(s,!0)),i.onDidChange(s=>this._handlePathChanged(s,!1)),i.onDidDelete(s=>this._handlePathDeleted(s)))}};async function NT(t,e,n,i){let s=Jt(t),c=Jt(e),u=new Array;u.push(s);let d=new Array,f=200,m=Date.now(),y;for(;(y=u.pop())!==void 0&&(i===void 0||d.length<i);){Date.now()-m>=f&&(await new Promise(L=>setTimeout(L,0)),m=Date.now());let D=Ur(c,y),N=n.makeLocalPathFilter(D),A=ra(y);for(let[L,B]of A){if(Date.now()-m>=f&&(await new Promise(P=>setTimeout(P,0)),m=Date.now()),L==="."||L==="..")continue;let F=et(D,L,B==="Directory");N.acceptsPath(F,B)&&(B==="File"?d.push(F):B==="Directory"&&u.push(et(y,L)))}}return Promise.resolve(d)}var Ll=class t{constructor(e,n,i,s){this._apiServer=e;this._pathHandler=n;this._fileExtensions=i;this._maxTrackedFiles=s}static verifyBatchSize=1e3;async describe(e,n,i){let s=await this._getAllPathNames(e,n,i);if(s.length>this._maxTrackedFiles)return{trackable:!1};let c=s.length,u=await this._chooseBlobNameSample(n,s);if(u.length===0)return{trackable:!0,trackableFiles:0,uploadedFraction:1};let d=await this._apiServer.findMissing(u),f=u.length,m=Math.min(d.unknownBlobNames.length,f);return{trackable:!0,trackableFiles:c,uploadedFraction:(f-m)/f}}async _getAllPathNames(e,n,i){let s=await xl(ot.file(e),ot.file(n),new rs(i),this._fileExtensions);return await NT(ot.file(e),ot.file(n),s,this._maxTrackedFiles+1)}async _chooseBlobNameSample(e,n){let i=new Array;for(;i.length<t.verifyBatchSize&&n.length>0;){let s=Math.floor(Math.random()*n.length),c=n[s];n[s]=n[n.length-1],n.pop();let u=et(e,c),d=await this._pathHandler.readText(u);if(d.type!=="text")continue;let f=this._pathHandler.calculateBlobName(c,d.contents);i.push(f)}return i}};var Bl=class t extends st{constructor(n,i){super();this._apiServer=n;this._workspaceManager=i;this._logger=ve("UnknownBlobHandler"),this._toProbe=new Hn(this._probe.bind(this)),this.addDisposable(this._toProbe),this._probeWaiters=new Hn(this._enqueueForProbe.bind(this)),this.addDisposable(this._probeWaiters),this._probeWaitersKicker=new sr(this._probeWaiters,t.probeRetryWaitMs),this.addDisposable(this._probeWaitersKicker),this._longWaiters=new Hn(this._enqueueForProbe.bind(this)),this.addDisposable(this._longWaiters),this._longWaitersKicker=new sr(this._longWaiters,t.longRetryWaitMs),this.addDisposable(this._longWaitersKicker)}static probeBatchSize=1e3;static probeRetryWaitMs=5*1e3;static probePatienceMs=2*60*1e3;static longRetryWaitMs=60*1e3;_toProbe;_currentBatch=new Map;_probeWaiters;_probeWaitersKicker;_longWaiters;_longWaitersKicker;_logger;enqueue(n){for(let[i,s]of n)this._logger.verbose(`enqueue: ${s.rootPath}:${s.relPath}`),this._toProbe.insert(i,{qualifiedPath:s,startTime:Date.now()});this._toProbe.kick()}_grabCurrentBatch(){if(this._currentBatch.size===0)return;let n=this._currentBatch;return this._currentBatch=new Map,n}async _probe(n){if(n!==void 0){let[u,d]=n;if(this._workspaceManager.getBlobName(d.qualifiedPath)!==u||(this._currentBatch.set(u,d),this._currentBatch.size<t.probeBatchSize))return}let i=this._grabCurrentBatch();if(i===void 0)return;let s=[...i.keys()],c;try{c=await Fn(async()=>this._apiServer.findMissing(s),this._logger)}catch{}if(c===void 0)for(let[u,d]of i)this._addRetryWaiter(u,d);else{this._logger.verbose(`find-missing reported ${c.nonindexedBlobNames.length} nonindexed blob names`),c.nonindexedBlobNames.length>0&&Si(this._logger,"verbose",c.nonindexedBlobNames,5);let u=new Set(c.unknownBlobNames),d=new Set(c.nonindexedBlobNames);for(let[f,m]of i)u.has(f)?this._workspaceManager.notifyBlobMissing(m.qualifiedPath,f):d.has(f)&&this._addRetryWaiter(f,m)}}_enqueueForProbe(n){if(n===void 0)this._toProbe.kick();else{let[i,s]=n;this._logger.verbose(`probe enqueue: ${s.qualifiedPath.rootPath}:${s.qualifiedPath.relPath}: ${i}`),this._toProbe.insert(i,s)}return Promise.resolve()}_addRetryWaiter(n,i){Date.now()-i.startTime<t.probePatienceMs?(this._logger.verbose(`retry enqueue: ${i.qualifiedPath.rootPath}:${i.qualifiedPath.relPath}: ${n}`),this._probeWaiters.insert(n,i)):(this._logger.verbose(`long retry enqueue: ${i.qualifiedPath.rootPath}:${i.qualifiedPath.relPath}: ${n}`),this._longWaiters.insert(n,i))}};var us=class t{constructor(e,n,i,s,c){this.blobs=e;this.recentChunks=n;this.trackedPaths=i;this.lastChatResponse=s;this.blobNames=c}static empty(){return new t({checkpointId:void 0,addedBlobs:[],deletedBlobs:[]},new Array,new Map,void 0,[])}};function LT(t){return ns(Jt(t))}function Ul(t){if(t.scheme==="file")return LT(t)}var Ip=class extends st{constructor(n,i,s,c,u,d,f,m,y,x,D){super(y,x);this.folderName=n;this.folderRoot=i;this.repoRoot=s;this.workspaceFolder=c;this.vcsDetails=u;this.folderId=d;this.diskFileManager=f;this.cacheDirPath=m;this.logger=D;this._operationQueue=new Wr(async N=>await this._runSerializedOperation(N)),this.addDisposables(this._operationQueue,{dispose:()=>this._disposeTracker()})}_operationQueue;_tracker;_newlyTracked=!1;_initialEnumerationComplete=!1;_initialSyncComplete=!1;_stopped=!1;dispose(){this._stopped=!0,super.dispose()}get stopped(){return this._stopped}get type(){return this.workspaceFolder===void 0?1:0}_disposeTracker(){this._tracker?.dispose(),this._tracker=void 0}setTracker(n){if(this.stopped)throw new Error("Source folder has been disposed");this._disposeTracker(),this._tracker=n}get tracker(){return this._tracker}get initialEnumerationComplete(){return this._initialEnumerationComplete}setInitialEnumerationComplete(){this._initialEnumerationComplete=!0}get initialSyncComplete(){return this._initialSyncComplete}setInitialSyncComplete(){this._initialSyncComplete=!0}relativePathName(n){if(or(this.folderRoot,n))return ga(this.repoRoot,n)}acceptsPath(n){return this._tracker===void 0?!1:this._tracker.pathFilter.acceptsPath(n)}async enqueueSerializedOperation(n){this._operationQueue.insert(n),await this._operationQueue.kick()}async _runSerializedOperation(n){n!==void 0&&(!this._initialEnumerationComplete||this._stopped||await n())}},Mp=class extends st{constructor(n,i,s){super(s);this.pathFilter=n;this.pathNotifier=i}};var Kj=new Set(["home directory","too large"]),Jj=new Set(["nested"]);function wa(t){return Kj.has(t)}function qT(t){return Jj.has(t)}function cr(t){return t.containingFolderRoot!==void 0?"nested":t.isHomeDir?"home directory":t.folderQualification!==void 0&&!t.folderQualification.trackable?"too large":t.syncingPermission==="denied"?"permission denied":t.syncingPermission==="granted"?"trackable":t.folderQualification===void 0?"qualifying":"permission needed"}var $l=class t extends st{constructor(n,i,s,c,u,d,f,m,y=new Array,x){super();this._storageUriProvider=n;this._apiServer=i;this._configListener=s;this._featureFlagManager=c;this._clientMetricsReporter=u;this._completionServer=d;this._blobNameCalculator=f;this._maxUploadSizeBytes=m;this._enableFileLimitsForSyncingPermission=this._featureFlagManager.currentFlags.enableFileLimitsForSyncingPermission,this._maxTrackableFiles=this._featureFlagManager.currentFlags.maxTrackableFileCount,this._maxTrackableFilesWithoutPermission=Math.min(this._featureFlagManager.currentFlags.maxTrackableFileCountWithoutPermission,this._maxTrackableFiles);let D=Math.min(this._featureFlagManager.currentFlags.minUploadedPercentageWithoutPermission,100);if(this._verifyFolderIsSourceRepo=this._featureFlagManager.currentFlags.verifyFolderIsSourceRepo,this._minUploadedFractionWithoutPermission=D*.01,this._refuseToSyncHomeDirectories=this._featureFlagManager.currentFlags.refuseToSyncHomeDirectories,this._useCheckpointManagerContext=!0,this._validateCheckpointManagerContext=!1,this._folderEnumeratedEmitter=this.addDisposable(new ue),this._folderSyncedEmitter=this.addDisposable(new ue),this._syncingProgressEmitter=this.addDisposable(new ue),this._syncingStateEmitter=this.addDisposable(new ue),this._sourceFoldersChangedEmitter=this.addDisposable(new ue),this._sourceFolderContentsChangedEmitter=this.addDisposable(new ue),this._sourceFolderContentsChangedEmitter=this.addDisposable(new ue),this._fileChangedEmitter=this.addDisposable(new ue),this._textDocumentOpenedEmitter=this.addDisposable(new ue),this._textDocumentClosedEmitter=this.addDisposable(new ue),this._textDocumentChangedEmitter=this.addDisposable(new ue),this._fileDeletedEmitter=this.addDisposable(new ue),this._fileWillRenameEmitter=this.addDisposable(new ue),this._featureFlagManager.currentFlags.bypassLanguageFilter)this._fileExtensions=void 0;else{let A=new Set;for(let L of y)for(let B of L.extensions)A.add(B);this._fileExtensions=A}this._pathHandler=new Al(this._maxUploadSizeBytes,oT()),this._pathMap=this.addDisposable(new Nl),this._openFileManager=this.addDisposable(new Ml(this._configListener,new Il(this._apiServer,this._completionServer,this._configListener,this._blobNameCalculator,this._pathMap,this._sequenceGenerator)));let N=x?.blobsCheckpointThreshold;this._blobsCheckpointManager=this.addDisposable(new Rl(this._apiServer,this._featureFlagManager,this._pathMap.onDidChangeBlobName,N)),this._unknownBlobHandler=this.addDisposable(new Bl(this._apiServer,this)),this._sourceFolderReconciler=this.addDisposable(new Cl(()=>this._reconcileSourceFolders())),this._sourceFolderDescriber=new Ll(this._apiServer,this._pathHandler,this._fileExtensions,this._maxTrackableFiles),this.addDisposable(Rt.onDidChangeWorkspaceFolders(this._handleWorkspaceFolderChangeEvent.bind(this))),this.addDisposable(Rt.onDidChangeTextDocument(this._notifyTextDocumentChanged.bind(this))),this.addDisposable(Rt.onDidOpenTextDocument(this._notifyTextDocumentOpened.bind(this))),this.addDisposable(Rt.onDidCloseTextDocument(this._notifyTextDocumentClosed.bind(this))),this.addDisposable(Rt.onDidCloseTextDocument(A=>{this._notifyDocumentClosed(A)})),this.addDisposable(Rt.onWillRenameFiles(A=>{this._notifyWillRenameFile(A)})),this.addDisposable(new rr(()=>this._disposeSourceFolders())),this._registerInitialSourceFolders(),this._awaitInitialSourceFolders()}static augmentRootName=".augmentroot";static ignoreSources(n){return[new ma(".gitignore"),new vl(n),new ma(".augmentignore")]}static pathMapPersistFrequencyMs=6e4;static defaultPathAccept=new wi;static _textEncoder=new TextEncoder;_initialSourceFolders=new Set;_registeredSourceFolders=new Map;_trackedSourceFolders=new Map;_fileExtensions;_pathMap;_sequenceGenerator=new Pl;_pathHandler;_openFileManager;_blobsCheckpointManager;_unknownBlobHandler;_sourceFolderDescriber;_logger=ve("WorkspaceManager");_folderEnumeratedEmitter;_folderSyncedEmitter;_syncingProgressEmitter;_syncingPermissionInitialized=!1;_sourceFolderReconciler;_syncingStateEmitter;_sourceFoldersChangedEmitter;_sourceFolderContentsChangedEmitter;_fileChangedEmitter;_textDocumentOpenedEmitter;_textDocumentClosedEmitter;_textDocumentChangedEmitter;_fileDeletedEmitter;_fileWillRenameEmitter;_lastChatResponse=void 0;_enableFileLimitsForSyncingPermission;_maxTrackableFiles;_maxTrackableFilesWithoutPermission;_verifyFolderIsSourceRepo;_minUploadedFractionWithoutPermission;_refuseToSyncHomeDirectories;_useCheckpointManagerContext;_validateCheckpointManagerContext;_stopping=!1;dispose(){this._stopping=!0,super.dispose()}get verifyFolderIsSourceRepo(){return this._verifyFolderIsSourceRepo}get minUploadedFractionWithoutPermission(){return this._minUploadedFractionWithoutPermission}get refuseToSyncHomeDirectories(){return this._refuseToSyncHomeDirectories}get initialFoldersEnumerated(){return Array.from(this._initialSourceFolders).every(n=>{let i=this._registeredSourceFolders.get(n);if(i===void 0)return!0;let s=cr(i);return wa(s)||qT(s)||s==="permission denied"?!0:this._trackedSourceFolders.get(n)?.sourceFolder?.initialEnumerationComplete})}async awaitInitialFoldersEnumerated(){for(;!this.initialFoldersEnumerated;)await aa(this._folderEnumeratedEmitter.event)}get initialFoldersSynced(){return Array.from(this._initialSourceFolders).every(n=>{let i=this._registeredSourceFolders.get(n);if(i===void 0)return!0;let s=cr(i);return wa(s)||qT(s)||s==="permission denied"?!0:this._trackedSourceFolders.get(n)?.sourceFolder?.initialSyncComplete})}async awaitInitialFoldersSynced(){for(;!this.initialFoldersSynced;)await aa(this._folderSyncedEmitter.event)}get syncingEnabledState(){if(!this._syncingPermissionInitialized)return"initializing";let n=0;for(let[i,s]of this._registeredSourceFolders){let c=cr(s);if(wa(c)||c==="permission denied")return"disabled";c==="permission needed"&&n++}return n>0?"partial":"enabled"}get onDidChangeSyncingState(){return this._syncingStateEmitter.event}get onDidChangeSourceFolders(){return this._sourceFoldersChangedEmitter.event}get onDidChangeSourceFolderContents(){return this._sourceFolderContentsChangedEmitter.event}get onDidChangeFile(){return this._fileChangedEmitter.event}get completionServer(){return this._completionServer}_disposeSourceFolders(){this._registeredSourceFolders.forEach(n=>{n.cancel?.cancel(),n.cancel?.dispose(),n.cancel=void 0}),this._trackedSourceFolders.forEach(n=>n.sourceFolder?.dispose()),this._trackedSourceFolders.clear()}getSyncingProgress(){let n=new Array;return this._trackedSourceFolders.forEach((i,s)=>{n.push(this._getSyncingProgress(s,i.sourceFolder))}),n}_getSyncingProgress(n,i){let s=i?.initialEnumerationComplete?{newlyTracked:i._newlyTracked,trackedFiles:this._pathMap.trackedFileCount(i.folderId),backlogSize:i.diskFileManager.itemsInFlight}:void 0;return{folderRoot:n,progress:s}}_isHomeDir(n){return this._featureFlagManager.currentFlags.refuseToSyncHomeDirectories?xT(n):!1}_registerInitialSourceFolders(){let n=new Array;Rt.workspaceFolders?.forEach(s=>{let c=Ul(s.uri);c!==void 0&&this._mtimeCacheExists(c)&&n.push(c)});let i=new Array;Rt.workspaceFolders?.forEach(s=>{let c=s.name,u=Ul(s.uri);if(u===void 0)return;let d="granted";this._logger.info(`Adding workspace folder ${c}; folderRoot = ${u}; syncingPermission = ${d}`),this._initialSourceFolders.add(u),this._registeredSourceFolders.set(u,{folderName:c,isHomeDir:this._isHomeDir(u),folderType:0,syncingPermission:d,workspaceFolder:s}),d==="granted"&&i.push(u)})}_mtimeCacheExists(n){let i=this._computeCacheDirPath(n);return El(i)}async _awaitInitialSourceFolders(){let n=Date.now();this._kickSourceFolderReconciler(),await this.awaitInitialFoldersSynced(),this._reportWorkspaceStartup(Date.now()-n),this._folderSyncedEmitter.fire()}_handleWorkspaceFolderChangeEvent(n){for(let s of n.added){let c=s.name,u=Ul(s.uri);if(u===void 0)continue;let d="granted";this._logger.info(`Adding workspace folder ${c}; folderRoot = ${u}; syncingPermission = ${d}`),this._registeredSourceFolders.set(u,{folderName:c,isHomeDir:this._isHomeDir(u),folderType:0,syncingPermission:d,workspaceFolder:s})}let i=new Array;for(let s of n.removed){let c=Ul(s.uri);if(c===void 0)continue;this._logger.info(`Removing workspace folder ${c}`);let u=this._registeredSourceFolders.get(c);u!==void 0&&(u.cancel?.cancel(),u.cancel?.dispose(),u.cancel=void 0,this._registeredSourceFolders.delete(c),i.push(c))}this._kickSourceFolderReconciler()}enableSyncing(){this._logger.info("Enabling syncing for all trackable source folders");let n=new Array;this._registeredSourceFolders.forEach((i,s)=>{let c=cr(i);wa(c)||c==="qualifying"||(i.syncingPermission="granted",n.push(s))}),this._kickSourceFolderReconciler()}disableSyncing(){this._logger.info("Disabling syncing for all trackable source folders"),this._registeredSourceFolders.forEach(n=>{let i=cr(n);wa(i)||(n.syncingPermission="denied")}),this._kickSourceFolderReconciler()}requalifyLargeFolders(){this._registeredSourceFolders.forEach(n=>{n.folderQualification=void 0}),this._kickSourceFolderReconciler()}_kickSourceFolderReconciler(){let n=new Set;for(let[i,s]of this._registeredSourceFolders)if(cr(s)==="trackable"){for(let[u,d]of this._registeredSourceFolders)if(cr(d)==="trackable"&&i!==u&&or(u,i)){n.add(i);break}}for(let[i,s]of this._registeredSourceFolders){if(!n.has(i)){s.containingFolderRoot=void 0;continue}for(let c of this._registeredSourceFolders.keys())if(!(i===c||n.has(c))&&or(c,i)){s.containingFolderRoot!==c&&this._logger.info(`Source folder ${i} will not be tracked. Containing folder: ${c}`),s.containingFolderRoot=c;break}}for(let[i,s]of this._registeredSourceFolders)cr(s)==="qualifying"&&s.cancel===void 0&&this._qualifySourceFolder(i,s);this._syncingStateEmitter.fire(this.syncingEnabledState),this._sourceFoldersChangedEmitter.fire(),this._sourceFolderReconciler.kick()}async _qualifySourceFolder(n,i){let[s,c]=await this._findRepoRoot(n),u,d;if(this._enableFileLimitsForSyncingPermission){d="full",this._logger.info(`Beginning ${d} qualification of source folder ${n}`);let m=new Lr;if(i.cancel=m,u=await this._sourceFolderDescriber.describe(n,s,t.ignoreSources(n)),m.token.isCancellationRequested){this._logger.info(`Cancelled qualification of source folder ${n}`);return}i.cancel=void 0,m.dispose()}else d="phony",this._logger.info(`Beginning ${d} qualification of source folder ${n} per feature flag`),u={trackable:!0,trackableFiles:0,uploadedFraction:1};let f={...u,repoRoot:s,isRepo:c};i.folderQualification=f,f.trackable?(this._logger.info(`Finished ${d} qualification of source folder ${n}: trackable files: ${f.trackableFiles}, uploaded fraction: ${f.uploadedFraction}, is repo: ${f.isRepo}`),f.trackableFiles>this._maxTrackableFilesWithoutPermission?this._logger.info(`Requesting syncing permission because source folder has more than ${this._maxTrackableFilesWithoutPermission} files`):this._verifyFolderIsSourceRepo&&!f.isRepo?this._logger.info("Requesting syncing permission because source folder does not appear to be a source repo"):f.uploadedFraction<this._minUploadedFractionWithoutPermission?this._logger.info(`Requesting syncing permission because source folder has less than ${this._minUploadedFractionWithoutPermission*100}% of files uploaded`):i.syncingPermission="granted"):this._logger.info(`Finished ${d} qualification of source folder ${n}: folder not trackable; too large`),this._kickSourceFolderReconciler()}async _reconcileSourceFolders(){let n=this.syncingEnabledState==="disabled",i=new Map;for(let[c,u]of this._trackedSourceFolders){let d=this._registeredSourceFolders.get(c),f;if(d===void 0)f="source folder has been removed";else if(n)f="syncing is disabled";else if(d.containingFolderRoot!==void 0)f=`source folder is nested inside folder ${d.containingFolderRoot}`;else if(d.isHomeDir)f="source folder is a home directory";else if(d.folderQualification?.trackable===!1)f="source folder is too large";else{let m=cr(d);m==="permission denied"?f="syncing permission denied for this source folder":m==="permission needed"?f="syncing permission not yet granted for this source folder":m==="qualifying"&&(f="source folder is being qualified")}f!==void 0&&i.set(c,[u,f])}let s=new Map;for(let[c,u]of this._registeredSourceFolders){if(cr(u)!=="trackable")continue;let f=this._trackedSourceFolders.get(c);f===void 0&&(f={folderName:u.folderName,folderSpec:(0,OT.cloneDeep)(u),cancel:new Lr,sourceFolder:void 0,logger:ve(`WorkspaceManager[${u.folderName}]`)},s.set(c,f))}for(let[c,[u,d]]of i)u.logger.info(`Stop tracking: ${d}`),this._trackedSourceFolders.delete(c),this._stopTracking(u);for(let[c,u]of s)u.logger.info("Start tracking"),this._trackedSourceFolders.set(c,u),this._startTracking(c,u);return Promise.resolve()}async _startTracking(n,i){let s=new yl("Startup metrics"),c=i.cancel,u=await this._createSourceFolder(n,i,c.token);if(c.token.isCancellationRequested){i.logger.info("Cancelled in-progress creation of source folder"),u?.dispose();return}if(s.charge("create SourceFolder"),c.dispose(),u===void 0||this._stopping){i.logger.info("Stopped tracking source folder");return}i.sourceFolder=u;let d=i.folderName,f=u.folderId,m=await TT(d,u.cacheDirPath);if(s.charge("read MtimeCache"),u.stopped){i.logger.info("Stopped tracking source folder");return}for(let[x,D]of m)this._pathMap.insert(f,x,"File",t.defaultPathAccept),this._pathMap.update(f,x,0,D.name,D.mtime);s.charge("pre-populate PathMap");let y=new ir;try{u._newlyTracked=m.size===0,y.add({dispose:()=>u._newlyTracked=!1});let x=await this._refreshSourceFolder(u,s);if(x===void 0||u.stopped)return;s.charge("enumerate"),u.setInitialEnumerationComplete(),this._folderEnumeratedEmitter.fire();let D=this._pathMap.onDidChangePathStatus(f);if(D===void 0)return;u.addDisposable(D(A=>{this._sourceFolderContentsChangedEmitter.fire(n)}),!0),this._sourceFoldersChangedEmitter.fire(),await u.diskFileManager.awaitQuiesced(),u.setInitialSyncComplete(),this._folderSyncedEmitter.fire(),s?.charge("await DiskFileManager quiesced");let N=new Fl(d,u.cacheDirPath);this._pathMap.enablePersist(f,N,t.pathMapPersistFrequencyMs),s.charge("enable persist"),this._reportSourceFolderStartup(i.logger,u,s,x)}finally{y.dispose()}}async _createSourceFolder(n,i,s){let c=i.folderName,u=new ir,d=new ir,f=i.folderSpec.folderType===1?void 0:i.folderSpec.workspaceFolder,[m,y]=await this._findRepoRoot(n);if(s.isCancellationRequested)return;let x=this._pathMap.openSourceFolder(n,m);u.add(new rr(()=>this._pathMap.closeSourceFolder(x))),u.addAll(...this._openFileManager.startTrackingFolder(c,x));let D=new Dl(c,this._apiServer,this._pathHandler,this._pathMap);u.add(D);let N=void 0,A=await this._migrateMtimeCache(n,i);return new Ip(c,n,m,f,N,x,D,A,u,d,i.logger)}async _migrateMtimeCache(n,i){let s=this._computeCacheDirPath(n);if(El(s))return s;let c=this._computeCacheDirPath(i.folderName);if(!El(c))return s;try{i.logger.info(`Migrating mtime cache for ${i.folderName} from "${c}" to "${s}"`),await kT(c,s)}catch(u){i.logger.error(`Failed to migrate mtime cache for ${i.folderName} from "${c}" to "${s}": ${pe(u)}`)}return s}_computeCacheDirPath(n){return t.computeCacheDirPath(n,this._storageUriProvider.storageUri)}static computeCacheDirPath(n,i){let s=Jt(i),c=aT(t._textEncoder.encode(n));return et(s,c)}async refreshSourceFolders(){this.requalifyLargeFolders();let n=Array.from(this._trackedSourceFolders.values()).map(i=>i.sourceFolder).filter(i=>i!==void 0).map(i=>i.enqueueSerializedOperation(async()=>{await this._refreshSourceFolder(i)}));try{await Promise.allSettled(n)}catch(i){this._logger.info(`One or more source folders failed to refresh: ${pe(i)}`)}}async _refreshSourceFolder(n,i){n.logger.debug(`Refreshing source folder ${n.folderName}`);let s=await this._createSourceFolderTracker(n,i);try{n.setTracker(s)}catch(u){n.logger.info(`Failed to install SourceFolderTracker for ${n.folderName}: ${pe(u)}`),s.dispose();return}return await this._enumerateSourceFolder(n,i)}async _enumerateSourceFolder(n,i){let s=n.tracker;if(s===void 0)return;let c=this._pathMap.nextEntryTS,u=await s.pathNotifier.enumeratePaths();if(!n.stopped)return i?.charge("enumerate paths"),this._pathMap.purge(n.folderId,c),i?.charge("purge stale PathMap entries"),u}async _createSourceFolderTracker(n,i){let s=new ir,c=await xl(ot.file(n.folderRoot),ot.file(n.repoRoot),new rs(t.ignoreSources(n.folderRoot)),this._fileExtensions);i?.charge("create PathFilter");let u=this._createPathNotifier(n,c);return s.add(u),i?.charge("create PathNotifier"),new Mp(c,u,s)}_createPathNotifier(n,i){let s=new Ol(n.folderName,n.folderRoot,n.repoRoot,i,n.workspaceFolder);return s.addDisposables(s.onDidFindPath(c=>{this._handlePathFound(n,c.relPath,c.fileType,c.acceptance)}),s.onDidCreatePath(c=>{this._handlePathCreated(n,c.relPath,c.fileType,c.acceptance)}),s.onDidChangePath(c=>{c.fileType==="File"&&this._handleFileChanged(n,c.relPath,c.acceptance)}),s.onDidDeletePath(c=>{this._handlePathDeleted(n,c)})),s}async _findRepoRoot(n){let i;return i=await bl(n,t.augmentRootName),i===void 0&&(i=(await PT(n))?.root),i!==void 0?[LT(i),!0]:[n,!1]}_trackOpenDocuments(n){let i=this._openFileManager.getTrackedPaths(n.folderId);for(let s of i)n.acceptsPath(s)||this._openFileManager.stopTracking(n.folderId,s);Rt.textDocuments.forEach(s=>{let c=this._trackDocument(n,s)})}_trackDocument(n,i){let s=$r(i.uri);if(s===void 0)return;let c=n.relativePathName(s);if(c!==void 0&&n.acceptsPath(c))return this._openFileManager.addOpenedDocument({folderId:n.folderId,relPath:c,document:i}),c}_stopTracking(n){if(n.sourceFolder===void 0){let i=n.cancel;i.cancel(),i.dispose(),n.logger.info("Cancelled in-progress tracking of source folder")}else{let i=n.sourceFolder;n.sourceFolder=void 0,i.dispose(),n.logger.info("Stopped tracking source folder")}this._folderSyncedEmitter.fire(),this._folderEnumeratedEmitter.fire()}translateRange(n,i,s){let c=this._resolveAbsPath(n.absPath);if(c===void 0)return;let[u,d]=c;return this._openFileManager.translateRange(u.folderId,d,i,s)}getContext(){if(this._openFileManager===void 0||this._pathMap===void 0)return us.empty();let n=this._openFileManager.getRecencySummary(this._completionServer.completionParams.chunkSize),i=new Set,s=new Map,c=new Map;for(let[N,A]of n.folderMap){let L=this._pathMap.getRepoRoot(N);L!==void 0&&c.set(L,A);for(let[B,K]of A){i.add(K);let F=this._pathMap.getBlobName(N,B);F!==void 0&&F!==K&&s.set(F,(s.get(F)??0)+1)}}let u=new Set;for(let[N,A]of s)i.has(N)||A===this._pathMap.getUniquePathCount(N)&&u.add(N);let d=new Array;for(let N of n.recentChunks){let A=this._pathMap.getRepoRoot(N.folderId);A!==void 0&&d.push({seq:N.seq,uploaded:N.uploaded,repoRoot:A,pathName:N.pathName,blobName:N.blobName,text:N.text,origStart:N.origStart,origLength:N.origLength,expectedBlobName:N.expectedBlobName})}let f=this._blobsCheckpointManager,m=f!==void 0&&this._useCheckpointManagerContext,y=m&&this._validateCheckpointManagerContext,x;if(!m||y){let N=new Set(i);for(let[B,K,F,P,U]of this._pathMap.pathsWithBlobNames())n.folderMap.get(B)?.has(F)||N.add(U);let A=Array.from(N),L=this._blobNamesToBlobs(A);if(x=new us(L,d,c,this._lastChatResponse,A),!m)return x}let D=f.getContextAdjusted(i,u);return x!==void 0&&(f.validateMatching(x.blobs,D)||this._clientMetricsReporter.report({client_metric:"blob_context_mismatch",value:1})),new us(D,d,c,this._lastChatResponse)}getContextWithBlobNames(){let n=this.getContext();return n.blobNames!==void 0?n:{...n,blobNames:this._blobsCheckpointManager.expandBlobs(n.blobs)}}recordChatReponse(n){this._lastChatResponse={seq:this._sequenceGenerator.next(),text:n}}_blobNamesToBlobs(n){return this._blobsCheckpointManager===void 0?{checkpointId:void 0,addedBlobs:n,deletedBlobs:[]}:this._blobsCheckpointManager.blobsPayload(n)}handleUnknownBlobs(n,i){if(i.length===0)return;let s=new Set(i),c=new Array;for(let[u,d]of n.trackedPaths)if(u!==void 0)for(let[f,m]of d)s.has(m)&&(c.push([m,new ar(u,f)]),s.delete(m));for(let u of s){let d=this._pathMap.getAnyPathName(u);d!==void 0&&c.push([u,d])}this._unknownBlobHandler.enqueue(c)}handleUnknownCheckpoint(n,i){this._logger.info(`received checkpoint not found for request id ${n}`),this._blobsCheckpointManager.resetCheckpoint(),this._blobsCheckpointManager.updateBlob("")}notifyBlobMissing(n,i){let s=this._pathMap.reportMissing(i);if(s!==void 0){let u=this._getSourceFolder(s.rootPath);if(u!==void 0){u.diskFileManager.ingestPath(u.folderId,n.relPath);return}}let c=this._getSourceFolder(n.rootPath);c!==void 0&&this._openFileManager.handleMissingBlob(c.folderId,n.relPath,i)}_getSourceFolder(n){return this._trackedSourceFolders.get(n)?.sourceFolder}resolvePathName(n){let i=typeof n=="string"?n:$r(n);if(i===void 0)return;let s=this._resolveAbsPath(i);if(s===void 0)return;let[c,u]=s;return new ar(c.repoRoot,u)}getFolderRoot(n){let i=typeof n=="string"?n:$r(n);if(i===void 0)return;let s=this._resolveAbsPath(i);if(s===void 0)return;let[c,u]=s;return c.folderRoot}safeResolvePathName(n){let i=typeof n=="string"?n:$r(n);if(i===void 0)return;let s=this._resolveAbsPath(i);if(s===void 0)return new ar("",i);let[c,u]=s;return new ar(c.repoRoot,u)}_resolveAbsPath(n){for(let[i,s]of this._trackedSourceFolders){if(s.sourceFolder===void 0)continue;let c=s.sourceFolder.relativePathName(n);if(c!==void 0)return[s.sourceFolder,c]}}hasFile(n){let[i,s]=this._resolveAbsPath(n.absPath)??[void 0,void 0];return i===void 0||s===void 0?!1:this._pathMap.hasFile(i.folderId,n.relPath)}getBlobName(n){let[i,s]=this._resolveAbsPath(n.absPath)??[void 0,void 0];if(!(i===void 0||s===void 0))return this._openFileManager.getBlobName(i.folderId,n.relPath)??this._pathMap.getBlobName(i.folderId,n.relPath)}getAllPathNames(n){return this._pathMap.getAllPathNames(n)}getAllQualifiedPathInfos(n){return this._pathMap.getAllQualifiedPathInfos(n)}getAllQualifiedPathNames(n){return this._pathMap.getAllQualifiedPathNames(n)}getAllPathInfo(n){return this._pathMap.getAllPathInfo(n)}_handlePathFound(n,i,s,c){let u=n.folderId;this._pathMap.insert(u,i,s,c),s==="File"&&c.accepted&&n.diskFileManager.ingestPath(u,i)}_handlePathCreated(n,i,s,c){let u=n.folderId;if(this._pathMap.insert(u,i,s,c),!!c.accepted){if(s==="File")n.diskFileManager.ingestPath(n.folderId,i),this._emitFileNotification(u,i,"disk");else if(s==="Directory"){let d=n.tracker?.pathFilter;if(d===void 0)return;n.enqueueSerializedOperation(()=>this._handleDirectoryCreated(n,i,d))}}}_handleFileChanged(n,i,s){let c=n.folderId;this._pathMap.insert(c,i,"File",s),s.accepted&&(n.diskFileManager.ingestPath(c,i),this._emitFileNotification(c,i,"disk"))}_handlePathDeleted(n,i){let s=n.folderId,c=this._pathMap.getPathInfo(s,i);if(c===void 0)return;this._deletePath(n.folderId,i);let[u,d]=c;d.accepted&&(u==="Directory"?this._handleDirectoryRemoved(n,i):u==="File"&&this._emitFileNotification(s,i,"disk"))}_deletePath(n,i){this._pathMap.remove(n,i)}async _handleDirectoryCreated(n,i,s){n.logger.info(`Directory created: ${i}`);let c=ot.file(n.repoRoot),u=new os(n.folderName,Tt.joinPath(c,i),c,s);for await(let[d,f,m,y]of u)this._handlePathFound(n,f,m,y)}_handleDirectoryRemoved(n,i){n.logger.info(`Directory removed: ${i}`);let s=n.folderId,c=new Array;for(let[u]of this._pathMap.pathsInFolder(s))pa(i,u)!==void 0&&c.push(u);for(let u of c)this._deletePath(s,u)}_notifyTextDocumentChanged(n){let i=this._uriToPathInfo(n.document.uri);if(i===void 0)return;let[s,c]=i;this._openFileManager.handleChangedDocument({folderId:s,relPath:c,event:n}),this._emitFileNotification(s,c,"buffer"),this._textDocumentChangedEmitter.fire({folderId:s,relPath:c,event:n})}_notifyTextDocumentOpened(n){let i=this._uriToPathInfo(n.uri);if(i===void 0)return;let[s,c]=i;this._textDocumentOpenedEmitter.fire({folderId:s,relPath:c,document:n})}_notifyTextDocumentClosed(n){let i=this._uriToPathInfo(n.uri);if(i===void 0)return;let[s,c]=i;this._textDocumentClosedEmitter.fire({folderId:s,relPath:c,document:n})}_uriToPathInfo(n){if(n===void 0)return;let i=$r(n);if(i===void 0)return;let s=this._resolveAbsPath(i);if(s===void 0)return;let[c,u]=s;if(c.acceptsPath(u))return[c.folderId,u]}_notifyWillRenameFile(n){n.files.forEach(i=>{let s=this._resolveAbsPath(i.oldUri.fsPath),c=this._resolveAbsPath(i.newUri.fsPath);if(s===void 0||c===void 0)return;let[u,d]=s,[f,m]=c;if(u.folderId!==f.folderId){this._logger.debug(`[WARN] Rename should not cause a file to move between source folders.     old file: ${s[1]}     new file: ${c[1]}    old source folder: ${u.folderName}     new source folder: ${f.folderName}`);return}this._fileWillRenameEmitter.fire({folderId:u.folderId,oldRelPath:d,newRelPath:m,type:Vi(i.oldUri.fsPath).type})})}_notifyDocumentClosed(n){let i=n.uri,s=$r(i);if(s===void 0)return;let c=this._resolveAbsPath(s);if(c===void 0)return;let[u,d]=c;this._openFileManager.handleClosedDocument({folderId:u.folderId,relPath:d,document:n})}_emitFileNotification(n,i,s){this._fileChangedEmitter.fire({folderId:n,relPath:i,origin:s})}_reportSourceFolderStartup(n,i,s,c){let u=i.diskFileManager.metrics;n.info("Tracking enabled"),n.info(c.format()),n.info(u.format()),n.info(s.format())}_reportWorkspaceStartup(n){this._logger.info(`Workspace startup complete in ${n} ms`)}trackedSourceFolderNames(){return Array.from(this._registeredSourceFolders).filter(([n,i])=>cr(i)==="trackable").map(([n,i])=>({folderRoot:n}))}listSourceFolders(){let n=this.syncingEnabledState==="disabled",i=new Array;for(let[s,c]of this._registeredSourceFolders){if(c.containingFolderRoot!==void 0){let x=c.folderType===0?2:3;i.push({type:x,name:c.folderName,syncingEnabled:!1,folderRoot:s,containingFolderRoot:c.containingFolderRoot});continue}if(c.isHomeDir){i.push({type:4,name:c.folderName,syncingEnabled:!1,folderRoot:s,reason:"home directory"});continue}if(c.folderQualification!==void 0&&!c.folderQualification.trackable){i.push({type:4,name:c.folderName,syncingEnabled:!1,folderRoot:s,reason:"too large"});continue}if(c.syncingPermission==="denied"){i.push({type:4,name:c.folderName,syncingEnabled:!1,folderRoot:s,reason:"permission not granted"});continue}let u=c.folderType===0?0:1,d=this._trackedSourceFolders.get(s)?.sourceFolder;if(!d?.initialEnumerationComplete){let x=!n&&c.syncingPermission==="granted";i.push({name:c.folderName,type:u,folderRoot:s,syncingEnabled:x,trackedFileCount:0,containsExcludedItems:!1,containsUnindexedItems:!1,enumerationState:0});continue}let f=!n&&c.syncingPermission==="granted",m=!1,y=!1;for(let[x,D,N,A]of this._pathMap.pathsInFolder(d.folderId))N||(m=!0),D==="File"&&N&&!A&&(y=!0);i.push({name:c.folderName,type:u,folderRoot:s,syncingEnabled:f,trackedFileCount:this._pathMap.trackedFileCount(d.folderId),containsExcludedItems:m,containsUnindexedItems:y,enumerationState:1})}return i}unitTestOnlyGetRepoRoot(n){let i=this._trackedSourceFolders.get(n);if(i!==void 0)return i.sourceFolder?.repoRoot}unitTestOnlySourceFolderBacklog(n){let i=this._trackedSourceFolders.get(n);if(i===void 0)return;let s=i.sourceFolder;if(s!==void 0&&s.initialEnumerationComplete)return s.diskFileManager.itemsInFlight}};var Wl=class t extends st{constructor(n,i,s,c){super();this._extensionContext=n;this._augmentConfigListener=i;this._apiServer=s;this._auth=c;this._completionAcceptanceReporter=new fl(s),this._clientMetricsReporter=new dl(s),this._completionsModel=new ll(this,this._augmentConfigListener,this._clientMetricsReporter)}static modelConfigBackoffMsecMax=3e4;_completionServer=void 0;workspaceManager=void 0;_enableCancel;_defaultModel;_modelInfo;_blobNameCalculator;get modelInfo(){return this._modelInfo}_availableModels=[];_languages=[];get languages(){return this._languages}featureFlagManager=new tl({fetcher:this._fetchFeatureFlags.bind(this),refreshIntervalMSec:30*60*1e3});_completionAcceptanceReporter;_clientMetricsReporter;enabled=!1;disposeOnDisable=[];_completionsModel;_logger=ve("AugmentExtension");get completionServer(){return this._completionServer}get completionsModel(){return this._completionsModel}get completionReporter(){return this._completionAcceptanceReporter}get enableInProgress(){return this._enableCancel!==void 0}get ready(){return this.enabled&&!this.enableInProgress}async enable(){if(this.enabled||this.enableInProgress)return;let n=new Lr;this._enableCancel=n;try{await this._enable(n.token)}catch(i){if(this._logger.info(`Unable to enable extension: ${pe(i)}`),process.env.JEST_WORKER_ID)throw i}finally{n.dispose(),this._enableCancel=void 0}}async _enable(n){if((0,BT.assert)(!this.enabled),this._auth.useOAuth){if(!await this._auth.getSession()){this._logger.info("Auth session not found. Please log in.");return}}else{if(this._logger.info("Using API token"),!this._augmentConfigListener.config.apiToken){this._logger.warn("No API token is configured");return}if(!this._augmentConfigListener.config.completionURL){this._logger.warn("No completion URL is configured");return}}let i;try{if(i=await this._getModelConfig(n),i.models.length===0)throw new Au;this._defaultModel=i.defaultModel,this._languages=i.languages,this._availableModels=i.models.map(d=>`${d.name} - ${d.internalName}`);let u=this._augmentConfigListener.config.modelName||i.defaultModel;if(this._modelInfo=i.models.find(d=>[d.name,d.internalName].includes(u)||d.name===(0,UT.createHash)("sha256").update(u).digest("hex")),this._modelInfo===void 0)throw new Mu(u);this.featureFlagManager.update(i.featureFlags)}catch(c){if(vt.isAPIErrorWithStatus(c,7))return;if(c instanceof Gi)return;if(c instanceof Xo)return;let u=pe(c);throw this._logger.error(`Failed to get model config: ${u}`),c}this._completionServer=new cl(this._apiServer,this._modelInfo.completionTimeoutMs,this._modelInfo.suggestedPrefixCharCount,this._modelInfo.suggestedSuffixCharCount);let s=this.featureFlagManager.currentFlags.maxUploadSizeBytes;this._blobNameCalculator=new ts(s),this.workspaceManager=new $l(this._extensionContext,this._apiServer,this._augmentConfigListener,this.featureFlagManager,this._clientMetricsReporter,this._completionServer,this._blobNameCalculator,s,i.languages),this.disposeOnDisable.push(this.workspaceManager);{let c=[this._completionAcceptanceReporter];for(let u of c)u.enableUpload(),this.disposeOnDisable.push(u)}this.enabled=!0}async _fetchFeatureFlags(n){try{return(await this._getModelConfig(n)).featureFlags}catch(i){this._logger.error("Failed to fetch feature flags: ",i);return}}updateModelInfo(n){if(!this._modelInfo)throw new Error("Model info not set");n.suggestedPrefixCharCount!==void 0&&(this._modelInfo.suggestedPrefixCharCount=n.suggestedPrefixCharCount),n.suggestedSuffixCharCount!==void 0&&(this._modelInfo.suggestedSuffixCharCount=n.suggestedSuffixCharCount),this._modelInfo.completionTimeoutMs=n.completionTimeoutMs}async _getModelConfig(n){let i=1e3,s,c=0,u=6;try{for(;;){if(n.isCancellationRequested)throw new Xo;try{this._logger.info("Retrieving model config"),s=await this._apiServer.getModelConfig(),this._logger.info("Retrieved model config")}catch(d){if(this._logger.error("Failed to retrieve model config: ",d),vt.isAPIErrorWithStatus(d,7))throw d;if(d instanceof Gi)throw d;c++}if(n.isCancellationRequested)throw this._logger.info("Model config retrieval cancelled"),new Xo;if(s!==void 0)return this._logger.info("Returning model config"),s;c>=u&&this._logger.warn("Model config retrieval failed"),this._logger.info(`Retrying model config retrieval in ${i} msec`),await zg(i),i=Math.min(i*2,t.modelConfigBackoffMsecMax)}}finally{}}disable(){for(this.enabled=!1;this.disposeOnDisable.length;)this.disposeOnDisable.pop().dispose();this.reset()}reset(){this._enableCancel?.cancel(),this._enableCancel?.dispose(),this._enableCancel=void 0,this.workspaceManager?.dispose(),this.workspaceManager=void 0}};function $T(t){let e=t.get("sessionId");return(e===void 0||!sg(e))&&(e=Ws(),t.update("sessionId",e)),e}var be=Lk(),Yt=(0,ke.createConnection)(),Xj=Qi.getInstance();Xj.setConnection(Yt);var Yj=process.env.TEST_TMPDIR?Hl.join(process.env.TEST_TMPDIR,"data"):process.env.XDG_DATA_HOME||Hl.join(WT.default.homedir(),".local","share"),Zj=Hl.join(Yj,"vim-augment"),zl,Ap,ls,Xt,jl=new il;async function e2(t,e){ls=new ol(t,jl);let n=new hl(t),i=$T(n);be.info(`Session ID: ${i}`),zl=new rl(jl,ls,i,e,global.fetch),Ap=new al(t,jl,zl,ls),Xt=new Wl(t,jl,zl,ls),await Xt.enable()}Yt.onInitialize(async t=>{be.info("Initializing Language Server");let e=t.initializationOptions?.editor??"unknown",n=t.initializationOptions?.pluginVersion??"unknown",i=t.initializationOptions?.vimVersion??"unknown",s=`Augment.vim/${n} ${e}/${i}`;be.info(`User agent: ${s}`);let c=t.workspaceFolders??[];be.info(`Roots: ${JSON.stringify(c)}`);for(let f of c)be.info(`Root name, URI: ${f.name}, ${f.uri}`);Kk(c);let u;try{u=new el(ot.file(Zj))}catch(f){throw be.error(`Error initializing server context storage: ${pe(f)}`),f}return await e2(u,s),{capabilities:{completionProvider:{resolveProvider:!1},textDocumentSync:ke.TextDocumentSyncKind.Incremental}}});Yt.onInitialized(()=>{be.info("Language server initialized")});Yt.onCompletion(async t=>{if(!Xt?.ready)return be.debug("Not logged in. Please login first."),new ke.ResponseError(401,"Not logged in. Please login first.");let e=Rt.documents.get(t.textDocument.uri);if(!e)return be.warn(`Not tracking the document ${t.textDocument.uri}`),new ke.ResponseError(ke.ErrorCodes.InternalError,`Not tracking the document ${t.textDocument.uri}`);if(Xt?.completionServer===void 0)return be.debug("Completion server is not initialized."),new ke.ResponseError(ke.ErrorCodes.InternalError,"Completion server is not initialized");try{let n=new _i(e),i=new Or(t.position.line,t.position.character),s=await Xt.completionsModel.generateCompletion(n,i);return be.info(`Completion: ${s?.requestId}`),be.debug(`Completion result: ${JSON.stringify(s)}`),[{label:s?.requestId??"",insertText:s?.completions[0]?.completionText??""}]}catch(n){return n instanceof gi?(be.debug(`Completion was cancelled or skipped: ${pe(n)}`),[]):(be.error(`Error getting completion: ${pe(n)}`),new ke.ResponseError(ke.ErrorCodes.InternalError,`Error getting completion: ${pe(n)}`))}});Yt.onRequest("augment/login",async()=>{try{return ls.isLoggedIn?(be.info("Already logged in"),{loggedIn:!0,url:""}):(be.info("Logging in..."),{loggedIn:!1,url:await Ap.startFlow()})}catch(t){let e=t instanceof Error?t.stack:"No stack trace available",n=pe(t);return be.error(`Error handling augment/login: ${n}`),be.error(`Stack trace: ${e}`),new ke.ResponseError(ke.ErrorCodes.InternalError,"Failed to process request. See server log for details.")}});Yt.onRequest("augment/logout",async()=>{try{return await ls.removeSession(),Xt?.disable(),be.info("Logged out."),{success:!0}}catch(t){let e=t instanceof Error?t.stack:"No stack trace available",n=pe(t);return be.error(`Error handling augment/logout: ${n}`),be.error(`Stack trace: ${e}`),new ke.ResponseError(ke.ErrorCodes.InternalError,"Failed to process request. See server log for details.")}});Yt.onRequest("augment/token",async t=>{try{await Ap.handleAuthJson(t.code)}catch(e){let n=e instanceof Error?e.stack:"No stack trace available",i=pe(e);return be.error(`Error handling user code: ${i}`),be.error(`Stack trace: ${n}`),e instanceof SyntaxError?new ke.ResponseError(ke.ErrorCodes.InternalError,"Failed to parse user code. Did you paste the code provided by the sign in page?"):i==="Unknown state"?new ke.ResponseError(ke.ErrorCodes.InternalError,"Could not find sign in state. Did you paste the code into the same prompt where you copied the link and did you navigate to the full sign in URL (it may wrap onto multiple lines)?"):new ke.ResponseError(ke.ErrorCodes.InternalError,"Failed to process user code. Please try again.")}try{return await Xt?.enable(),be.info("Logged in."),{}}catch(e){let n=e instanceof Error?e.stack:"No stack trace available",i=pe(e);return be.error(`Error enabling extension: ${i}`),be.error(`Stack trace: ${n}`),new ke.ResponseError(ke.ErrorCodes.InternalError,"Failed to process request. See server log for details.")}});Yt.onRequest("augment/status",()=>{if(!Xt?.ready)return{loggedIn:!1};let t=Xt?.workspaceManager?.getSyncingProgress();if(!t)return{loggedIn:!0};let e=0,n=0;for(let s of t){if(!s.progress)return{loggedIn:!0};s.progress.newlyTracked&&(be.info(`New folder ${s.folderRoot} sync progress: tracked=${s.progress.trackedFiles}, backlog=${s.progress.backlogSize}`),e+=s.progress.trackedFiles,n+=s.progress.trackedFiles-s.progress.backlogSize)}return{loggedIn:!0,syncPercentage:e>0?Math.floor(n/e*100):100}});Yt.onRequest("augment/chat",async t=>{if(!Xt?.ready)return be.debug("Not logged in. Please login first."),new ke.ResponseError(401,"Not logged in. Please login first.");let e=t.textDocumentPosition.textDocument.uri,n=Rt.documents.get(e);if(n||be.warn(`Not tracking the document ${e}`),Xt?.completionServer===void 0)return be.debug("Completion server is not initialized."),new ke.ResponseError(ke.ErrorCodes.InternalError,"Completion server is not initialized");let i=t.textDocumentPosition.position,s=n?.offsetAt(i)||0,c=n?.getText()||"",u=s,d=c.length-s,f=c.slice(Math.max(0,s-u),s),m=c.slice(s,s+d),y=Xt.completionServer.createRequestId();be.info(`Chat (${y}) requested`),n&&(be.debug(`Document URI: ${n.uri}`),be.debug(`Language ID: ${n.languageId}`)),t.selectedText!==void 0&&be.debug(`Selected text: ${t.selectedText}`);let D=Xt?.workspaceManager?.getContext()?.blobs??{checkpointId:void 0,addedBlobs:[],deletedBlobs:[]},N="";try{let A=await zl.chatStream(y,t.message,t.history??[],D,[],[],void 0,[],void 0,t.selectedText,f,m,n?.uri,n?.languageId,void 0,!1,void 0,void 0);for await(let L of A){be.debug(`Chat chunk for ${y}: ${L.text}`);let B={requestId:y,text:L.text};await Yt.sendNotification("augment/chatChunk",B),N+=L.text}}catch(A){return be.error(`Error streaming chat: ${pe(A)}`),new ke.ResponseError(ke.ErrorCodes.InternalError,`Error streaming chat: ${pe(A)}`)}return Xt.workspaceManager?.recordChatReponse(N),{requestId:y,text:N}});Yt.onRequest("augment/pluginVersion",async t=>{if(t.version==="0.0.0")return{version:"0.0.0",isPrerelease:!1};let e=t.version.match(/^(\d+)\.(\d+)\.(\d+)$/);if(!e)return new ke.ResponseError(ke.ErrorCodes.InternalError,`Invalid version format: ${t.version}`);let n=parseInt(e[1]),i=parseInt(e[2]),s=parseInt(e[3]),c=s===0,u=c?"prerelease":"main";try{let d=`https://api.github.com/repos/augmentcode/augment.vim/commits/${u}`,f=await fetch(d);if(!f.ok)return be.error(`Failed to fetch version: ${f.statusText}`),new ke.ResponseError(ke.ErrorCodes.InternalError,`Failed to fetch plugin version: ${f.statusText}`);let m=await f.json();if(m?.commit?.message&&typeof m.commit.message=="string"){let y=m.commit.message.match(/^Augment Vim v(\d+)\.(\d+)\.(\d+)$/);if(!y)return new ke.ResponseError(ke.ErrorCodes.InternalError,"Unable to parse version from commit message");let x=parseInt(y[1]),D=parseInt(y[2]),N=parseInt(y[3]);return n>x||n===x&&i>D||n===x&&i===D&&s>N?new ke.ResponseError(ke.ErrorCodes.InternalError,`Plugin version ${t.version} is ahead of upstream version ${x}.${D}.${N}`):{version:`${x}.${D}.${N}`,isPrerelease:c}}return new ke.ResponseError(ke.ErrorCodes.InternalError,"Unable to parse message from commit reponse")}catch(d){return be.error(`Error fetching version: ${pe(d)}`),new ke.ResponseError(ke.ErrorCodes.InternalError,`Failed to fetch plugin version: ${pe(d)}`)}});Yt.onNotification("augment/resolveCompletion",t=>{if(be.info(`Completion (${t.requestId}) resolved with accept=${t.accept}`),Xt?.completionReporter===void 0){be.warn("Completion reporter is not yet initialized.");return}Xt.completionReporter.reportResolution(t.requestId,Date.now(),Date.now(),t.accept?0:void 0)});Yt.onDidOpenTextDocument(t=>{be.debug(`Document opened: ${t.textDocument.uri} ${t.textDocument.version}`);let e=t.textDocument,n=Ni.create(e.uri,e.languageId,e.version,e.text);Rt.documents.add(n),Og.fire(new _i(n))});Yt.onDidCloseTextDocument(t=>{be.debug(`Document closed: ${t.textDocument.uri}`);let e=Rt.documents.get(t.textDocument.uri);e&&(Rt.documents.remove(t.textDocument.uri),Bg.fire(new _i(e)))});Yt.onDidChangeTextDocument(t=>{be.debug(`Document changed: ${t.textDocument.uri}`),be.debug(`Document changed params: ${JSON.stringify(t)}`);let e=Rt.documents.get(t.textDocument.uri);if(!e)return;let n=[];for(let s of t.contentChanges){be.debug(`Content change: ${JSON.stringify(s)}`);let c;if("range"in s)c=new bi(s.range.start.line,s.range.start.character,s.range.end.line,s.range.end.character);else{let d=e.positionAt(e.getText().length);c=new bi(0,0,d.line,d.character)}let u={range:c,rangeOffset:e.offsetAt(c.start),rangeLength:e.offsetAt(c.end)-e.offsetAt(c.start),text:s.text};n.push(u),e=Ni.update(e,[s],t.textDocument.version),be.debug(`Updated document (${e.version}):
${e.getText()}`)}Rt.documents.add(e);let i={document:new _i(e),contentChanges:n};Lg.fire(i)});Yt.listen();
/*! Bundled license information:

lodash/lodash.js:
  (**
   * @license
   * Lodash <https://lodash.com/>
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
