---
name: Bug report
about: Create a report to help us improve apex
title: ''
labels: bug
assignees: ''

---

**Describe the Bug**

**Minimal Steps/Code to Reproduce the Bug**
<!--
Please list the *minimal* steps or provide a code snippet for us to be able to reproduce the bug.

A helpful guide on on how to craft a minimal bug report http://matthewrocklin.com/blog/work/2018/02/28/minimal-bug-reports.
--> 

**Expected Behavior**
<!-- A clear and concise description of what you expected to happen. -->

**Environment**
<!-- OS, version of Python, CUDA, PyTorch; collect these via `python -m torch.utils.collect_env` -->
