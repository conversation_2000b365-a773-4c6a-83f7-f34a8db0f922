name: 'Automatic pull request review'
description: '👍 Github action to automate pull requests'
author: '<PERSON> <andrew<PERSON><PERSON><PERSON>@gmail.com>'
runs:
  using: 'node12'
  main: 'dist/index.js'
branding:
  icon: 'thumbs-up'
  color: 'gray-dark'
inputs:
  event:
    description: 'The event to perform on the pull request review.'
    default: 'APPROVE'
  body:
    description: 'The contents of the review body comment. Required when event is COMMENT or REQUEST_CHANGES.'
  repo-token:
    description: 'The GH_TOKEN secret can be passed in using {{ secrets.GH_TOKEN }}'
    required: true
