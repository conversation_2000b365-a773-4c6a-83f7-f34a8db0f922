repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0  # Use the latest stable version
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-symlinks
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-json

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.2
    hooks:
      # Run the linter.
      - id: ruff
        args: [ --fix ]
      # Run the formatter.
      - id: ruff-format
      # manual stages to auto-correct
      - id: ruff
        args: [ --fix ]
        stages: [manual]
      - id: ruff-format
        stages: [manual]

  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline', 'audit']

  - repo: local
    hooks:
    - id: pyright
      name: pyright
      entry: pyright
      language: system
      types: [python]
      args: [--stats, -p, pyrightconfig.ci.json]
